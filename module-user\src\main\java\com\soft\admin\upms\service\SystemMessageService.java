package com.soft.admin.upms.service;

import com.soft.common.core.base.service.IBaseService;
import com.soft.common.core.object.MyPageData;
import com.soft.admin.upms.dto.SystemMessagePageDTO;
import com.soft.admin.upms.model.SystemMessage;
import com.soft.admin.upms.vo.SystemMessageVO;

import java.util.List;

/**
 * 系统消息Service接口
 * 
 * <AUTHOR>
 * @date 2023-06-26
 */
public interface SystemMessageService extends IBaseService<SystemMessage, Long> {

    /**
     * 分页查询系统消息
     * @param param
     * @return
     */
    MyPageData<SystemMessageVO> page(SystemMessagePageDTO param);

    /**
     * 批量已读
     * @param userId
     * @param idList
     */
    void batchRead( Long userId, List<Long> idList);

    /**
     * 删除系统消息
     * @param userId
     * @param id
     */
    void removeMessageUserRelation(Long userId, Long id);
}
