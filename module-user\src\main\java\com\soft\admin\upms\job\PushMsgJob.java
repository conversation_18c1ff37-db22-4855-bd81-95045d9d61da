package com.soft.admin.upms.job;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.soft.admin.upms.api.dingtalk.DingCallbackCrypto;
import com.soft.admin.upms.dao.SysUserMapper;
import com.soft.admin.upms.model.SysUser;
import com.soft.admin.upms.model.message.MessageRecordContentFacade;
import com.soft.admin.upms.model.message.MessageRecordContentNoticeFacade;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.compress.utils.Lists;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.soft.admin.upms.dao.SystemMessageMapper;
import com.soft.admin.upms.dao.SystemMessageUserRelationMapper;
import com.soft.admin.upms.model.SystemMessage;
import com.soft.admin.upms.model.SystemMessageUserRelation;
import com.soft.common.core.constant.GlobalDeletedFlag;
import com.soft.common.core.exception.MyRuntimeException;
import com.soft.common.core.exception.ServiceException;
import com.soft.common.core.util.DateUtils;
import com.xxl.job.core.handler.annotation.XxlJob;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class PushMsgJob {

    public static final String LOCK_KEY_PUSH_MSG = "PUSH:MSG:lock:";
    @Resource
    private SystemMessageMapper systemMessageMapper;
    @Resource
    private SystemMessageUserRelationMapper systemMessageUserRelationMapper;
    @Resource
    private ThreadPoolTaskExecutor syncDataTaskExecutor;
    @Resource
    private RedissonClient redissonClient;

    @Resource
    private SysUserMapper sysUserMapper;
    /**
     * 定时发送消息
     */
    @XxlJob("autoPushMsgHandler")
    public void pushMsg() {
        //查找未发送记录
        LambdaQueryWrapper<SystemMessage> queryWrapper = Wrappers.lambdaQuery(SystemMessage.class);
        queryWrapper.eq( SystemMessage::getStatus, 0);
        String date = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM);
        // 查询正在生效的巡更计划
        queryWrapper.ge(SystemMessage::getSendTime, date + ":00");
        queryWrapper.le(SystemMessage::getSendTime, date + ":59");
        queryWrapper.eq(SystemMessage::getDeletedFlag, GlobalDeletedFlag.NORMAL);

        List<SystemMessage> list = systemMessageMapper.selectList(queryWrapper);

        if (CollectionUtil.isNotEmpty(list)) {
            list.stream().forEach(systemMessage -> {
                syncDataTaskExecutor.execute(() -> {
                    try {
                        pushMessage(systemMessage);
                    } catch (Throwable e) {
                        log.error("发送系统消息发生异常", e);
                        throw e;
                    }
                });
            });
        }
    }

    public void pushMessage(SystemMessage msg){
        if(msg == null){
            throw new MyRuntimeException("未找到系统消息");
        }

        // 获取同步锁，防止用户多次点击同步数据
        RLock lock = redissonClient.getLock(LOCK_KEY_PUSH_MSG+msg.getId());
        if (lock.isLocked()) {
            throw new ServiceException("数据正在发送中，请稍后重试");
        }
        try {
            lock.lock();
            //消息发送中
            msg.setStatus(2);
            systemMessageMapper.updateById(msg);

            List<Long> userIds = Lists.newArrayList();

            //判断是否发送所有人
            if(msg.getAllUser() == 1){
                //发送所有人
                List<SysUser> users = sysUserMapper.selectList((Wrappers.lambdaQuery(SysUser.class).eq(SysUser::getDeletedFlag,GlobalDeletedFlag.NORMAL)));
                //将用户对象转成ID列表
                userIds =  users.stream().map(SysUser::getUserId).collect(Collectors.toList());
            }else{
                //发送指定人员
                List<SystemMessageUserRelation> systemMessageUserRelations = systemMessageUserRelationMapper
                        .selectList(Wrappers.lambdaQuery(SystemMessageUserRelation.class).eq(SystemMessageUserRelation::getMessageId, msg.getId()));
                if(CollectionUtil.isNotEmpty(systemMessageUserRelations)){
                    userIds = systemMessageUserRelations.stream().map(SystemMessageUserRelation::getUserId).collect(Collectors.toList());
                }
            }

            //发送
            if(CollectionUtil.isNotEmpty(userIds)){
                //发送消息
                //100条发送一次
                List<List<Long>> list = ListUtils.partition(userIds, 100);

                for(List<Long> userIdList : list){
                    MessageRecordContentFacade.NOTICE.CUSTOM().custom(msg.getId(),msg.getTitle(),  msg.getContent(),msg.getCreateUserId(), userIdList);
                }
            }

            //消息发送完成
            msg.setStatus(1);
            systemMessageMapper.updateById(msg);

        }catch (Exception e) {
            log.error(e.getMessage(),e);
        }finally {
            // 取消同步锁
            lock.unlock();
            log.info("数据发送完成");

        }
    }

}