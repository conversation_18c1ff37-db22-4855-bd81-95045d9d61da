package com.soft.admin.upms.dto.message;

import com.soft.admin.upms.enums.MessageRecordTypeEnums;
import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = true)
public class MessageRecordQueryDTO extends MyPageParam {

    @ApiModelProperty("消息类型：NOTICE：通知；ALARM：告警；WARNING：预警")
    private MessageRecordTypeEnums messageRecordType;
    @ApiModelProperty("是否已读")
    private Integer isRead;
    @ApiModelProperty("发布时间 年月日")
    private String date;
    @ApiModelProperty("发布状态 0未发布 1已发布")
    private Integer messageIssueStatus;

    @ApiModelProperty("开始日期")
    private String startDate;

    @ApiModelProperty("结束日期")
    private String endDate;
}
