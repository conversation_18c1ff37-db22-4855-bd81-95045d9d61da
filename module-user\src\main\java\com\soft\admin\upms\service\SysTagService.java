package com.soft.admin.upms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.admin.upms.dto.SysTagDTO;
import com.soft.admin.upms.dto.SysTagPageDTO;
import com.soft.admin.upms.model.SysTag;
import com.soft.admin.upms.vo.SysTagVO;
import com.soft.common.core.object.MyPageData;

/**
 * 标签Service接口
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
public interface SysTagService extends IService<SysTag> {

    /**
     * 新增/修改
     * @param tagDTO
     * @return
     */
    boolean update(SysTagDTO tagDTO);

    /**
     * 分页查询
     * @param pageDTO
     * @return
     */
    MyPageData<SysTagVO> pageList(SysTagPageDTO pageDTO);

    /**
     * 删除
     * @param tagId
     * @return
     */
    boolean delete(Long tagId);
}
