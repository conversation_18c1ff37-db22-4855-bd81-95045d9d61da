package com.soft.admin.upms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * SysAnnexVO视图对象
 *
 * <AUTHOR>
 * @date 2023-09-19
 */
@ApiModel("SysAnnexVO视图对象")
@Data
public class SysAnnexVO {

    @ApiModelProperty(value = "生产制度附件ID")
    private Long id;

    @ApiModelProperty(value = "附件名称")
    private String name;

    @ApiModelProperty(value = "路径")
    private String path;

    @ApiModelProperty(value = "后缀")
    private String suffix;

    @ApiModelProperty(value = "文件大小")
    private Integer size;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "删除标记(1: 正常 -1: 已删除)")
    private Integer deletedFlag;

    @ApiModelProperty(value = "创建人")
    private Long createUserId;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "编辑人")
    private Long updateUserId;

    @ApiModelProperty(value = "编辑时间")
    private Date updateTime;

}