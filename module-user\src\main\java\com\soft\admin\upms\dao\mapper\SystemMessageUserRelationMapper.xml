<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.SystemMessageUserRelationMapper">
    <resultMap type="com.soft.admin.upms.model.SystemMessageUserRelation" id="SystemMessageUserRelationResult">
        <result property="id" column="id" />
        <result property="messageId" column="message_id" />
        <result property="userId" column="user_id" />
        <result property="isRead" column="is_read" />
        <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="columns">
        id, message_id, user_id, is_read, create_time
    </sql>

    <sql id="selectSystemMessageUserRelationVo">
        select <include refid="columns"/> from sp_system_message_user_relation
    </sql>

    <insert id="batchInsert">
        insert into
            sp_system_message_user_relation(<include refid="columns"/>)
        values
            <foreach collection="list" item="record" separator=",">
                (
                 #{record.id},
                 #{record.messageId},
                 #{record.userId},
                 #{record.isRead},
                 #{record.createTime}
                )
            </foreach>
    </insert>

    <update id="batchRead">
        update
            sp_system_message_user_relation
        set
            is_read = 1
        where
            user_id = #{userId}
        and
            is_read = 0
        and
            message_id in
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </update>
    <delete id="deleteByMessageId">
        delete from
            sp_system_message_user_relation
        where
            user_id = #{userId}
        and
            message_id = #{messageId}
    </delete>

</mapper>