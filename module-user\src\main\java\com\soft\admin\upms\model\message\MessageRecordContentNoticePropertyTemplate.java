package com.soft.admin.upms.model.message;

import com.soft.admin.upms.enums.MessageRecordBusiTypeEnums;
import com.soft.admin.upms.enums.MessageRecordTypeEnums;

import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class MessageRecordContentNoticePropertyTemplate extends MessageRecordContentNoticeTemplate {

    private static final MessageRecordBusiTypeEnums BUSI_TYPE = MessageRecordBusiTypeEnums.PROPERTY;

//    private static final String CONTENT = "您提交的${applyType}${applyResult}，请及时查看！";

    private static final String HYPERLINK = "/property/myPropertyApply/applyDetail?id=${applyId}";
    /*
    入住申请 换房申请
    （入住申请已通过/驳回、换房申请通过/驳回）

    #您提交的#入住/换房申请#①审批通过②被驳回，请及时查看！

    #申请成功：请您于#入住时间#入住#房间位置（楼栋-楼层-点位）#房间名称#床位#
    举例：申请成功：请您于2024-11-15 10:30入住5号楼505-1号床
     */

    private static final String TITLE = "%s申请已%s";
    private static final String CONTENT = "申请成功：请您于%s入住%s%s%s";

    public MessageRecordContent push(String approver,String time,String space,String roomName,String roomPoint,Long applyId, Integer applyType, boolean applyResult, Long busiId, String level, Long sendUserId, Long... receiveUserIds) {
        String applyTypeName = Objects.equals(applyType, 1) ? "入住申请" : Objects.equals(applyType, 2) ? "换房申请" : "";
//        messageRecordContent.setTitle(applyTypeName);
        messageRecordContent.setTitle(String.format(TITLE,Objects.equals(applyType, 1) ? "入住" : Objects.equals(applyType, 2) ? "换房" : "", applyResult ? "通过" : "驳回"));
        messageRecordContent.setBusiId(busiId);
        messageRecordContent.setLevel(level);
//        messageRecordContent.setContent(CONTENT.replace("${applyType}", applyTypeName)
//                .replace("${applyResult}", applyResult ? "审批通过" : "被驳回"));
        messageRecordContent.setContent(applyResult?String.format(CONTENT,time,space,roomName,roomPoint):String.format("抱歉，您的入住申请被%s驳回",approver));
        messageRecordContent.setSendUserId(sendUserId);
        messageRecordContent.setReceiveUserIds(Stream.of(receiveUserIds).collect(Collectors.toList()));
        messageRecordContent.setHyperlink(HYPERLINK.replace("${applyId}", String.valueOf(applyId)));
        messageRecordContent.setType(MessageRecordTypeEnums.NOTICE);
        messageRecordContent.setBusiType(BUSI_TYPE);
        pushJiGuangMessage(Boolean.FALSE);
        push();
//        callback(messageRecordContent);
        workNotice(messageRecordContent);
        return messageRecordContent;
    }

}
