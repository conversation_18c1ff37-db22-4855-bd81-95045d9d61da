package com.rutong.medical.admin.dto.station;

import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("DeviceBaseStationPageQueryDTO")
@Data
public class DeviceBaseStationPageQueryDTO extends MyPageParam {

    @ApiModelProperty(value = "名称/编号")
    private String keyWord;

    @ApiModelProperty(value = "位置")
    private String spacePath;

    @ApiModelProperty(value = "基站分类表ID")
    private Long deviceBaseStationType;

    @ApiModelProperty(value = "在线状态(1:在线,0:离线) ")
    private Integer isOnline;

    private List<Long> deviceBaseStationTypeIdList;
}
