package com.rutong.medical.admin.dto.monitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * SmDeviceMonitorDTO对象
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@ApiModel("SmDeviceMonitorDTO对象")
@Data
public class DeviceMonitorDTO {

    @ApiModelProperty(value = "视频监控表ID")
    @NotNull(message = "数据验证失败，视频监控表ID不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "监控编号")
    private String monitorCode;

    @ApiModelProperty(value = "监控名称")
    private String monitorName;

    @ApiModelProperty(value = "监控类型(1:枪机,2:球机)")
    private Integer monitorType;

    @ApiModelProperty(value = "安装位置")
    private Long spaceId;

    @ApiModelProperty(value = "ip地址")
    private String ip;

    @ApiModelProperty(value = "端口")
    private Long port;

    @ApiModelProperty(value = "通道号")
    private String channelNum;

    @ApiModelProperty(value = "用户名")
    private String userCode;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "创建人")
    private Long createUserId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改人")
    private Long updateUserId;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

}
