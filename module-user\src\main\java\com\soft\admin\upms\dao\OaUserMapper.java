package com.soft.admin.upms.dao;

import com.soft.admin.upms.model.OaUser;
import com.soft.common.core.base.dao.BaseDaoMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * OA系统用户关系Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
public interface OaUserMapper extends BaseDaoMapper<OaUser> {

    /**
     * 根据oa用户id列表查询
     *
     * @param list
     * @return
     */
    List<OaUser> selectByOaUserIds(@Param("list") List<String> list);

    /**
     * 批量新增
     *
     * @param list
     */
    void batchInsert(@Param("list") List<OaUser> list);
}
