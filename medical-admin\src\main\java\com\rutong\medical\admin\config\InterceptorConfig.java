package com.rutong.medical.admin.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * @ClassName InterceptorConfig
 * @Description
 * <AUTHOR>
 * @Date 2025/7/8 10:51
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Configuration
public class InterceptorConfig implements WebMvcConfigurer {

    public static final String[] SWAGGER_WHITELIST = {
            "/swagger-ui.html",
            "/doc.html", //接口文档地址
            "/swagger-ui/*",
            "/swagger-resources/**",
            "/v2/api-docs",
            "/v3/api-docs",
            "/webjars/**"
    };

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
//        registry.addInterceptor(new AuthenticationInterceptor())
//                .addPathPatterns("/**")
//                .excludePathPatterns(SWAGGER_WHITELIST)
//                .order(-10);

        registry.addInterceptor(new AuthenticationInterceptor())
                .addPathPatterns("/admin/**")
                .addPathPatterns("/core/**")
                .addPathPatterns("/sys/**")
                .addPathPatterns("/system/**")
                .addPathPatterns("/equipment/**")
                .addPathPatterns("/energy/**")
                .addPathPatterns("/inspection/**")
                .addPathPatterns("/cablingSystem/**")
                .addPathPatterns("/check/**")
                .addPathPatterns("/passage/**")
                .addPathPatterns("/knowledge/**")
                .addPathPatterns("/meeting/**")
                .addPathPatterns("/oa/**")
                .addPathPatterns("/sparePart/**")
                .addPathPatterns("/property/**")
                .addPathPatterns("/om/**")
                .addPathPatterns("/shifts/**")
                .addPathPatterns("/common/**")
                .addPathPatterns("/estate/**")
                .addPathPatterns("/transport/**")
                .addPathPatterns("/message/record/**")
                .addPathPatterns("/contingency/**")
                .addPathPatterns("/mgs/**")
                .addPathPatterns("/subSystem/**")
                .addPathPatterns("/intrusion-alarm/**")
                .addPathPatterns("/purchase/contract/**")
                .addPathPatterns("/ops/**")
                .addPathPatterns("/training/**")
                .addPathPatterns("/prevent/**")
                .addPathPatterns("/v1/chat-messages")
                .addPathPatterns("/v1/conversations")
                .addPathPatterns("/v1/messages")
                .addPathPatterns("/face/**")
                .addPathPatterns("/fire/**")
                .addPathPatterns("/device/**")
        ;
    }
}
