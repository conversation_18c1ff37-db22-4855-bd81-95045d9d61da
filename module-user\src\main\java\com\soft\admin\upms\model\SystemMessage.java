package com.soft.admin.upms.model;

import com.baomidou.mybatisplus.annotation.*;
import com.soft.admin.upms.vo.SystemMessageVO;
import com.soft.common.core.base.mapper.BaseModelMapper;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Date;

/**
 * 系统消息对象 sp_system_message
 *
 * <AUTHOR>
 * @date 2023-06-26
 */
@Data
@TableName(value = "sp_system_message")
public class SystemMessage {

    @TableId(value = "id")
    private Long id;

    /**
     * 系统消息标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 发布状态 0未发布 1已发布 2发送中
     */
    private Integer status;

    /**
     * 发布类型 0立即发布 1定时发布
     */
    private Integer sendType;

    /**
     * 是否全员发送 1全员 0部分人员
     */
    private Integer allUser;

    /**
     * 发布时间
     */
    private Date sendTime;


    /**
     * 系统消息类型
     */
    private String type;

    /**
     * 系统消息紧急程度
     */
    private Integer priority;

    /**
     * 业务id
     */
    private Long businessId;

    private String businessCode;

    /**
     * 创建时间。
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 创建者Id。
     */
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private Long createUserId;

    /** 删除标识，1正常；-1已删除 */
    @TableLogic(value = "1", delval = "-1")
    private Integer deletedFlag;

    @Mapper
    public interface SystemMessageModelMapper extends BaseModelMapper<SystemMessageVO, SystemMessage> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        SystemMessage toModel(SystemMessageVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        SystemMessageVO fromModel(SystemMessage entity);
    }

    public static final SystemMessageModelMapper INSTANCE = Mappers.getMapper(SystemMessageModelMapper.class);
}
