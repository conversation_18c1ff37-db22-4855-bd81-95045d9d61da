package com.rutong.medical.admin.service.station;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rutong.medical.admin.dto.station.DeviceBaseStationTypeDTO;
import com.rutong.medical.admin.dto.station.DeviceBaseStationTypeQueryDTO;
import com.rutong.medical.admin.entity.station.DeviceBaseStationType;

import cn.hutool.core.lang.tree.Tree;

/**
 * 基站分类Service接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface DeviceBaseStationTypeService extends IService<DeviceBaseStationType> {

    /**
     * 获取基站类型树结构列表
     * 
     * @param deviceBaseStationTypeQuery
     * @return
     */
    List<Tree<Long>> getTreeList(DeviceBaseStationTypeQueryDTO deviceBaseStationTypeQuery);

    /**
     * 新增基站类型
     * 
     * @param deviceBaseStationType
     */
    void save(DeviceBaseStationTypeDTO deviceBaseStationType);

    /**
     * 更新基站类型
     * 
     * @param deviceBaseStationType
     */
    void update(DeviceBaseStationTypeDTO deviceBaseStationType);

    /**
     * 删除基站类型
     * 
     * @param id
     */
    void delete(Long id);
}
