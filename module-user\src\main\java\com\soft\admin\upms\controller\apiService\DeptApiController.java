package com.soft.admin.upms.controller.apiService;

import com.soft.admin.upms.dto.SysDeptDto;
import com.soft.admin.upms.model.SysDept;
import com.soft.admin.upms.service.apiService.DeptApiService;
import com.soft.common.core.annotation.NoAuthInterface;
import com.soft.common.core.constant.ErrorCodeEnum;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.common.core.util.StringUtils;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;


@Api(tags = "部门对外提供API")
@RestController
@RequestMapping("/v1/dept/")
public class DeptApiController {

    @Autowired
    private DeptApiService deptApiService;
    /**
     * 获取用户信息
     * @return
     */
    @GetMapping("/getDeptList")
    @NoAuthInterface
    public ResponseResult<MyPageData<SysDept>> getDeptList(HttpServletRequest request, SysDeptDto sysDeptDto){
        String appKey    = request.getHeader("appKey");
        String appSecret = request.getHeader("appSecret");
        if (StringUtils.isBlank(appKey)||StringUtils.isBlank(appSecret)){
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "appKey或appSecret不能为空");
        }else{
            if (appKey.equals("bkluier9")&&appSecret.equals("OAxjImz4aMWek-0jq0gda550x")){
                MyPageData<SysDept> myPageData = deptApiService.list(sysDeptDto);
                return ResponseResult.success(myPageData);
            }
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "appKey或appSecret错误");
        }
    }
}
