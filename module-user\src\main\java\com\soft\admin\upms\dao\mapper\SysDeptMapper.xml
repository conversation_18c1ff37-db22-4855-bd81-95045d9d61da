<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.SysDeptMapper">
    <resultMap id="BaseResultMap" type="com.soft.admin.upms.model.SysDept">
        <id column="dept_id" jdbcType="BIGINT" property="deptId"/>
        <result column="dept_code" jdbcType="VARCHAR" property="deptCode" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="dept_path" jdbcType="VARCHAR" property="deptPath"/>
        <result column="dept_desc" jdbcType="VARCHAR" property="deptDesc"/>
        <result column="show_order" jdbcType="INTEGER" property="showOrder"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="deleted_flag" jdbcType="INTEGER" property="deletedFlag"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO common_sys_dept
            (dept_id,
            dept_code,
            dept_path,
            dept_name,
            dept_desc,
            show_order,
            parent_id,
            deleted_flag,
            create_user_id,
            update_user_id,
            create_time,
            update_time)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.deptId},
            #{item.deptCode},
            #{item.deptPath},
            #{item.deptName},
            #{item.deptDesc},
            #{item.showOrder},
            #{item.parentId},
            #{item.deletedFlag},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.createTime},
            #{item.updateTime})
        </foreach>
    </insert>
    <update id="batchUpdate">
        UPDATE `common_sys_dept`
        SET
            update_time = now()
            ,
            dept_name = CASE dept_id
            <foreach collection="list" item="dept" open="(" separator="," close=")">
                WHEN #{dept.deptId} THEN #{dept.deptName}
            </foreach>
            ,
            parent_id = CASE dept_id
            <foreach collection="list" item="dept" open="(" separator="," close=")">
                WHEN #{dept.deptId} THEN #{dept.parentId}
            </foreach>
        WHERE
            dept_id in
            <foreach collection="list" item="dept" open="(" separator="," close=")">
                #{dept.deptId}
            </foreach>

    </update>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.soft.admin.upms.dao.SysDeptMapper.inputFilterRef"/>
        AND common_sys_dept.deleted_flag = ${@com.soft.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="sysDeptFilter != null">
            <if test="sysDeptFilter.deptName != null and sysDeptFilter.deptName != ''">
                <bind name = "safeSysDeptDeptName" value = "'%' + sysDeptFilter.deptName + '%'" />
                AND common_sys_dept.dept_name LIKE #{safeSysDeptDeptName}
            </if>
            <if test="sysDeptFilter.parentId != null">
                AND common_sys_dept.parent_id = #{sysDeptFilter.parentId}
            </if>
        </if>
    </sql>

    <select id="getSysDeptList" resultMap="BaseResultMap" parameterType="com.soft.admin.upms.model.SysDept">
        SELECT * FROM common_sys_dept
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSysDeptApiList" resultMap="BaseResultMap" parameterType="com.soft.admin.upms.model.SysDept">
        SELECT dept_id, dept_code,detp_path, dept_name, dept_desc, parent_id, show_order, create_time FROM common_sys_dept
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>


    <select id="getDeptPath" resultMap="BaseResultMap" parameterType="java.util.List">

        SELECT
        d4.dept_id,
        d4.dept_name AS dept_name,
        CASE
        -- 当层级数 ≥2 时，截取第一级之后的路径
        WHEN (LENGTH(CONCAT_WS('/', d1.dept_name, d2.dept_name, d3.dept_name, d4.dept_name))
        - LENGTH(REPLACE(CONCAT_WS('/', d1.dept_name, d2.dept_name, d3.dept_name, d4.dept_name), '/', ''))) >= 1
        THEN
        SUBSTRING_INDEX(
        CONCAT_WS('/',
        NULLIF(d1.dept_name, ''),
        NULLIF(d2.dept_name, ''),
        NULLIF(d3.dept_name, ''),
        d4.dept_name
        ),
        '/',
        -(
        -- 直接使用斜杠数量（层级数-1）作为截取段数
        LENGTH(CONCAT_WS('/', d1.dept_name, d2.dept_name, d3.dept_name, d4.dept_name))
        - LENGTH(REPLACE(CONCAT_WS('/', d1.dept_name, d2.dept_name, d3.dept_name, d4.dept_name), '/', ''))
        )
        )
        -- 当只有1级时，直接显示部门名称
        ELSE
        d4.dept_name
        END AS dept_path
        FROM common_sys_dept d4
        LEFT JOIN common_sys_dept d3 ON d4.parent_id = d3.dept_id
        LEFT JOIN common_sys_dept d2 ON d3.parent_id = d2.dept_id
        LEFT JOIN common_sys_dept d1 ON d2.parent_id = d1.dept_id
        WHERE d4.dept_id in
        <foreach collection="list" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </select>

    <select id="getSecurityByUserId" resultMap="BaseResultMap">
        select b.* from common_sys_dept_user a,common_sys_dept b where a.dept_id=b.dept_id and user_id=#{userId} and b.dept_code=#{deptCode};
    </select>

    <select id="getSecurityAll" resultMap="BaseResultMap">
        select a.user_id,a.show_name as user_name,a.device_sn from common_sys_user a,common_sys_dept_user b,
        common_sys_dept c where a.user_id=b.user_id and b.dept_id=c.dept_id and c.dept_code=#{deptCode};
    </select>

</mapper>
