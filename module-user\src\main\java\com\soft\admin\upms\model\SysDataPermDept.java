package com.soft.admin.upms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

/**
 * 数据权限与部门关联实体对象。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
@Data
@ToString(of = {"deptId"})
@TableName(value = "common_sys_data_perm_dept")
public class SysDataPermDept {

    /**
     * 数据权限Id。
     */
    private Long dataPermId;

    /**
     * 关联部门Id。
     */
    private Long deptId;
}
