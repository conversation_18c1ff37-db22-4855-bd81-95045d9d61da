package com.rutong.medical.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName User
 * @Description
 * <AUTHOR>
 * @Date 2025/7/9 14:45
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Data
@TableName("test")
@ApiModel(value = "test", description = "test")
public class Test implements Serializable {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    private Integer userId;
    private String userName;
    private Integer age;
}
