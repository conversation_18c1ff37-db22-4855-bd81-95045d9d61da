package com.soft.admin.upms.dao;

import com.soft.admin.upms.model.SysPermModule;
import com.soft.common.core.base.dao.BaseDaoMapper;

import java.util.List;

/**
 * 权限资源模块数据访问操作接口。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
public interface SysPermModuleMapper extends BaseDaoMapper<SysPermModule> {

    /**
     * 获取整个权限模块和权限关联后的全部数据。
     *
     * @return 关联的权限模块和权限资源列表。
     */
    List<SysPermModule> getPermModuleAndPermList();
}
