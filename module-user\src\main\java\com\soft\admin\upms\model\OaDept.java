package com.soft.admin.upms.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * OA系统部门关系对象 sp_oa_dept
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sp_oa_dept")
public class OaDept extends BaseModel {

    @TableId(value = "dept_id")
    private Long deptId;

    private String oaDeptId;

    private String oaType;

}
