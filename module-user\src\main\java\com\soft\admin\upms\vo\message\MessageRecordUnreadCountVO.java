package com.soft.admin.upms.vo.message;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MessageRecordUnreadCountVO {

    @ApiModelProperty("通知数量")
    private Long noticeCount;

    @ApiModelProperty("通知数量")
    private Long todoCount;

    @ApiModelProperty("告警数量")
    private Long alarmCount;

    @ApiModelProperty("预警数量")
    private Long warningCount;

    @ApiModelProperty("总数")
    private Long total;

}
