<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.SysDeptLeaderMapper">
    <resultMap id="BaseResultMap" type="com.soft.admin.upms.model.SysDeptLeader">
        <id column="user_id" jdbcType="BIGINT" property="userId"/>
        <id column="dept_id" jdbcType="BIGINT" property="deptId"/>
        <id column="sync_version" jdbcType="BIGINT" property="syncVersion"/>
    </resultMap>
    <delete id="deleteBatch" parameterType="java.lang.Long">
        DELETE FROM common_sys_dept_leader WHERE sync_version &lt; #{syncVersion}
    </delete>

    <delete id="deleteByDeptId" parameterType="java.lang.Long">
        DELETE FROM common_sys_dept_leader WHERE dept_id =  #{deptId}
    </delete>

    <select id="selectSysDeptLeader" resultType="com.soft.admin.upms.model.SysDeptLeader">
        select * from  common_sys_dept_leader where user_id = #{userId}
        AND dept_id IN
        <foreach item="id" index="index" collection="deptIds"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectByDeptId" parameterType="java.lang.Long" resultType="com.soft.admin.upms.model.SysDeptLeader">
        select * from  common_sys_dept_leader where dept_id = #{deptId}
    </select>
</mapper>
