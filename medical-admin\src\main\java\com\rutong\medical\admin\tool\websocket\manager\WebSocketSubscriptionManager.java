package com.rutong.medical.admin.tool.websocket.manager;

import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class WebSocketSubscriptionManager {

    private final SimpMessagingTemplate messagingTemplate;

    // 维护所有主题的订阅关系
    private final Map<String, Map<String, Set<String>>> topicSubscriptions = new ConcurrentHashMap<>();

    public WebSocketSubscriptionManager(SimpMessagingTemplate messagingTemplate) {
        this.messagingTemplate = messagingTemplate;
    }

    /**
     * 添加订阅
     *
     * @param userId
     * @param topic  订阅的主题（如 "/topic/equipment"）
     * @param items  该用户订阅的具体 ID 列表（如设备 ID、事件类型）
     */
    public void addSubscription(String userId, String topic, Set<String> items) {
        topicSubscriptions.computeIfAbsent(topic, k -> new ConcurrentHashMap<>())
                .put(userId, items);
        log.info("用户 {} 订阅主题 {}，内容：{}", userId, topic, items);
    }

    /**
     * 取消订阅
     *
     * @param userId
     * @param topic  取消订阅的主题
     */
    public void removeSubscription(String userId, String topic) {
        if (topicSubscriptions.containsKey(topic)) {
            topicSubscriptions.get(topic).remove(userId);
            log.info("用户 {} 取消订阅 {}", userId, topic);
        }
    }

    /**
     * 获取订阅某个 ID（设备、事件等）的所有用户
     */
    public Set<String> getSubscribers(String topic, String itemId) {
        Set<String> subscribers = new HashSet<>();
        topicSubscriptions.getOrDefault(topic, Collections.emptyMap()).forEach((userId, items) -> {
            if (items.contains(itemId)) {
                subscribers.add(userId);
            }
        });
        return subscribers;
    }

    /**
     * 清理 WebSocket 断开时的订阅
     *
     * @param userId
     */
    public void removeSession(String userId) {
        // 遍历所有主题
        for (Map.Entry<String, Map<String, Set<String>>> topicEntry : topicSubscriptions.entrySet()) {
            String topic = topicEntry.getKey();
            Map<String, Set<String>> sessionSubscriptions = topicEntry.getValue();

            // 如果当前主题的订阅中包含该 sessionId，则移除
            if (sessionSubscriptions.containsKey(userId)) {
                sessionSubscriptions.remove(userId);
                log.info("清理订阅: 主题 = {},  userId = {}", topic, userId);

                // 如果当前主题的所有会话都移除完毕，清空该主题的订阅
                if (sessionSubscriptions.isEmpty()) {
                    topicSubscriptions.remove(topic);
                    log.info("移除空主题: {}", topic);
                }
            }
        }
    }


    /**
     * 推送数据到订阅某个 ID（设备、事件等）的所有用户
     */
    public void broadcastMessage(String topic, String itemId, Object data) {
        Set<String> subscribers = getSubscribers(topic, itemId);
        for (String userId : subscribers) {
            String userDestination = topic + "/" + userId; // 用户专属的推送路径
            messagingTemplate.convertAndSend(userDestination, data);
            log.info("向用户 {} 推送 {} 主题的数据: {}", userId, topic, data);

//            log.info("向用户 {} 推送 {} 主题的数据: {}", sessionId, topic, data);
        }
    }
}
