package com.soft.admin.upms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * ThreePartyRobotVO视图对象
 *
 * <AUTHOR>
 * @date 2023-05-23
 */
@ApiModel("ThreePartyRobotVO视图对象")
@Data
public class ThreePartyRobotVO {

    private Long id;

    @ApiModelProperty(value = "平台（QW企业微信，DINGDING钉钉）")
    private String platform;

    @ApiModelProperty(value = "机器人名称")
    private String name;


    @ApiModelProperty(value = "类型")
    private String type;


    @ApiModelProperty(value = "webhook地址")
    private String webHook;


    @ApiModelProperty(value = "密钥")
    private String secret;


    @ApiModelProperty(value = "备注")
    private String remark;


    @ApiModelProperty(value = "创建时间")
    private Date createTime;


    @ApiModelProperty(value = "创建者id")
    private Long createUserId;


    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


    @ApiModelProperty(value = "更新者id")
    private Long updateUserId;


}
