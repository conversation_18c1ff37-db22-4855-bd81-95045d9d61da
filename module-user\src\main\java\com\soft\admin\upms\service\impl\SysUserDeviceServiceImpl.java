package com.soft.admin.upms.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.soft.admin.upms.dao.SysUserDeviceMapper;
import com.soft.admin.upms.dto.SysUserDeviceDTO;
import com.soft.admin.upms.model.SysUserDevice;
import com.soft.admin.upms.service.SysUserDeviceService;
import com.soft.common.core.base.dao.BaseDaoMapper;
import com.soft.common.core.base.service.BaseService;
import com.soft.common.core.object.TokenData;
import com.soft.common.core.util.DateUtils;
import com.soft.common.core.util.MyModelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * @className: SysUserDeviceServiceImpl
 * @author: WangZeYu
 * @date: 2024/9/13 上午9:47
 * @description:
 */
@Service
@Slf4j
public class SysUserDeviceServiceImpl extends BaseService<SysUserDevice, Long> implements SysUserDeviceService {
    @Resource
    private SysUserDeviceMapper sysUserDeviceMapper;

    @Override
    protected BaseDaoMapper<SysUserDevice> mapper() {
        return sysUserDeviceMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertUserDevice(SysUserDeviceDTO sysUserDevice) {
        log.info("jiguang push insert device request: {}", JSONObject.toJSON(sysUserDevice));
        Long userId = TokenData.takeFromRequest().getUserId();
        sysUserDevice.setUserId(userId);
        LocalDateTime now = LocalDateTime.now(ZoneId.of("UTC+8"));
        sysUserDevice.setCreateTime(DateUtils.toDate(now));
        //删除用户之前的设备信息
        LambdaQueryWrapper<SysUserDevice> queryWrapper = Wrappers.lambdaQuery(SysUserDevice.class);
        queryWrapper.eq(SysUserDevice::getUserId,userId);
        sysUserDeviceMapper.delete(queryWrapper);
        SysUserDevice userDevice = MyModelUtil.copyTo(sysUserDevice, SysUserDevice.class);
        //新增设备信息
        sysUserDeviceMapper.insert(userDevice);
        log.info("jiguang push insert device success");
        return true;
    }
}
