package com.soft.common.mqtt;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;



/**
 * Date 09/07/17
 * Time 4:37 PM
 *
 * <AUTHOR>
 */
@Slf4j
public class SharedSubCallbackRouter implements MqttCallback {

    private Map<String, MQTTTopicMessageListener> topicFilterListeners;

    public SharedSubCallbackRouter(Map<String, MQTTTopicMessageListener> topicFilterListeners) {
        this.topicFilterListeners = topicFilterListeners;
    }

    public void addSubscriber(String topicFilter, MQTTTopicMessageListener listener) {
        if (this.topicFilterListeners == null) {
            this.topicFilterListeners = new HashMap<>();
        }
        this.topicFilterListeners.put(topicFilter, listener);
    }

    @Override
    public void connectionLost(Throwable throwable) {
        log.error("断开了MQTT连接 ：{}", throwable.getMessage());
        log.error(throwable.getMessage(), throwable);
    }


    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {
        for (Map.Entry<String, MQTTTopicMessageListener> listenerEntry : topicFilterListeners.entrySet()) {
            String topicFilter = listenerEntry.getKey();
            if (isMatched(topicFilter, topic)) {
                listenerEntry.getValue().messageArrived(topic, message);
            }
        }
    }

    @Override
    public void deliveryComplete(IMqttDeliveryToken token) {
        log.debug("deliveryComplete---------" + token.isComplete());
    }

    /**
     * 问题：Paho topic matcher does not work with shared subscription topic filter of emqttd
     * <p>
     * 描述：
     * 当 订阅的 topic为共享主题时:    $queue/GATEWAY/10001，
     * 往 mqtt broker 发送的 topic 依然是 $queue/GATEWAY/10001
     * 但是本地 注册 listener 时，保存的是主题就变成 GATEWAY/10001
     * 所以会出现本地消费不到共享主题的问题
     * <p>
     * 解决方案：
     * https://github.com/eclipse/paho.mqtt.java/issues/367#issuecomment-300100385
     * <p>
     * http://emqtt.io/docs/v2/advanced.html#shared-subscription
     * <p>
     * https://blog.csdn.net/weixin_34064653/article/details/91937396
     * <p>
     * https://github.com/yogin16/paho-shared-sub-example
     *
     * @param topicFilter the topicFilter for mqtt
     * @param topic       the topic
     * @return boolean for matched
     */
    private boolean isMatched(String topicFilter, String topic) {
        if (topicFilter.startsWith("$queue/")) {
            topicFilter = topicFilter.replaceFirst("\\$queue/", "");
        } else if (topicFilter.startsWith("$share/")) {
            topicFilter = topicFilter.replaceFirst("\\$share/", "");
            topicFilter = topicFilter.substring(topicFilter.indexOf('/'));
        }
        return MqttTopic.isMatched(topicFilter, topic);
    }
}