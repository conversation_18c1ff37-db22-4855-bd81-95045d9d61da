package com.soft.admin.upms.dao;

import com.soft.admin.upms.model.SysUserPost;
import com.soft.common.core.base.dao.BaseDaoMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户岗位数据操作访问接口。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
public interface SysUserPostMapper extends BaseDaoMapper<SysUserPost> {

    /**
     * 批量新增
     * @param sysUserPosts
     */
    void insertBatch(List<SysUserPost> sysUserPosts);

    /**
     * 查询某部门下的领导岗位
     * @param deptId
     * @return
     */
    List<SysUserPost> queryByDeptId(@Param("deptId") Long deptId);
}
