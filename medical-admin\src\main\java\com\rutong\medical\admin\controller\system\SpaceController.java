package com.rutong.medical.admin.controller.system;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.rutong.medical.admin.dto.system.SpaceQueryDTO;
import com.rutong.medical.admin.dto.system.SpaceSaveDTO;
import com.rutong.medical.admin.dto.system.SpaceUpdateDTO;
import com.rutong.medical.admin.service.system.SpaceService;
import com.rutong.medical.admin.vo.system.SpaceVO;
import com.soft.common.core.annotation.NoAuthInterface;
import com.soft.common.core.constant.ErrorCodeEnum;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;

import cn.hutool.core.lang.tree.Tree;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 空间控制器类
 *
 * <AUTHOR>
 * @date 2023-03-29
 */
@RestController
@Api(tags = "空间管理")
@RequestMapping("/core/space")
public class SpaceController {

    @Resource
    private SpaceService spaceService;

    /**
     * 空间列表查询
     *
     * @param spaceQueryDTO 查询条件
     * @return
     */
    @GetMapping("/pageList")
    public ResponseResult<MyPageData<SpaceVO>> pageList(@Validated SpaceQueryDTO spaceQueryDTO) {
        MyPageData<SpaceVO> pageData = spaceService.pageList(spaceQueryDTO);
        return ResponseResult.success(pageData);
    }

    /**
     * 新建空间
     *
     * @param spaceSave 空间新增
     * @return
     */
    @PostMapping("/add")
    public ResponseResult<Void> save(@RequestBody @Validated SpaceSaveDTO spaceSave) {
        int save = spaceService.save(spaceSave);
        return save > 0 ? ResponseResult.success() : ResponseResult.error(ErrorCodeEnum.NO_ERROR, "空间新增失败！");
    }

    /**
     * 更新空间
     *
     * @param spaceUpdateDTO 空间修改
     * @return
     */
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody @Validated SpaceUpdateDTO spaceUpdateDTO) {
        int update = spaceService.update(spaceUpdateDTO);
        return update > 0 ? ResponseResult.success() : ResponseResult.error("400", "空间修改失败！");
    }

    /**
     * 删除空间
     *
     * @param spaceId 空间ID
     * @return
     */
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@RequestParam Long spaceId) {
        int delete = spaceService.delete(spaceId);
        return delete > 0 ? ResponseResult.success() : ResponseResult.error(ErrorCodeEnum.NO_ERROR, "删除失败！");
    }

    /**
     * 查询空间树形列表结构
     *
     * @param deep 递归深度
     * @return
     */
    @GetMapping("/tree")
    public ResponseResult<List<Tree<String>>> tree(@RequestParam Integer deep,
        @RequestParam(required = false) boolean displayCode) {
        List<Tree<String>> tree = spaceService.tree(false, deep, displayCode);
        return ResponseResult.success(tree);
    }

    @GetMapping("/treeAll")
    public ResponseResult<List<Tree<String>>> treeAll(@RequestParam Integer deep,
        @RequestParam(required = false) boolean displayCode) {
        List<Tree<String>> tree = spaceService.tree(true, deep, displayCode);
        return ResponseResult.success(tree);
    }

    @ApiOperation("生成空间二维码")
    @GetMapping("/qrcode/{id}")
    public ResponseResult<String> qrcode(@PathVariable Long id) throws Exception {
        return ResponseResult.success(spaceService.generatorQrcode(id));
    }

    @ApiOperation("导出空间二维码文件")
    @GetMapping("/qrcode/export")
    public void exportQrcode(SpaceQueryDTO spaceQueryDTO) {
        spaceService.exportQrcode(spaceQueryDTO);
    }

    /**
     * <AUTHOR>
     * @Description 根据空间完整名称模糊查询
     * @Date 下午6:09 2024/9/10
     * @Param [spaceQueryDTO]
     * @return com.soft.common.core.object.ResponseResult<java.util.List<com.soft.sub.vo.system.SpaceVO>>
     **/
    @ApiOperation("根据空间完整名称模糊查询")
    @GetMapping("/listSpace")
    public ResponseResult<List<SpaceVO>> listSpace(@Validated SpaceQueryDTO spaceQueryDTO) {
        return ResponseResult.success(spaceService.listSpace(spaceQueryDTO));
    }

    /**
     * <AUTHOR>
     * @Description 根据id查询空间信息
     * @Date 下午1:31 2024/10/15
     * @Param [id]
     * @return com.soft.common.core.object.ResponseResult<com.soft.common.core.object.MyPageData<com.soft.sub.vo.system.SpaceVO>>
     **/
    @ApiOperation("根据id查询空间信息")
    @GetMapping("/getSpaceById/{id}")
    public ResponseResult<SpaceVO> getSpaceById(@PathVariable Long id) {
        return ResponseResult.success(spaceService.getSpaceById(id));
    }

    @ApiOperation("系统树")
    @GetMapping("/listTree/{equipmentTypeId}")
    public ResponseResult<List<Tree<String>>> listTree(@PathVariable("equipmentTypeId") Long equipmentTypeId) {
        return spaceService.listTree(equipmentTypeId);
    }

}
