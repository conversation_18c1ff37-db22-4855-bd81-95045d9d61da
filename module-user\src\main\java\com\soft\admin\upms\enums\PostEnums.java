package com.soft.admin.upms.enums;

import java.util.Objects;

public enum PostEnums {

    OPERATION_ADMIN("PT00001", "运维管理员"),

    SAFETY_INSPECTOR("PT00002", "安全检查员"),

    ;

    private String code;

    private String name;

    public String getCode() {
        return code;
    }

    PostEnums(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PostEnums getByCode(String code) {
        for (PostEnums postEnums : PostEnums.values()) {
            if (Objects.equals(postEnums.getCode(), code)) {
                return postEnums;
            }
        }
        return null;
    }
}
