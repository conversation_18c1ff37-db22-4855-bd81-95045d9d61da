package com.rutong.medical.admin.service.device;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rutong.medical.admin.dto.device.DeviceTerminalSaveDTO;
import com.rutong.medical.admin.dto.station.DevicePageQueryDTO;
import com.rutong.medical.admin.entity.device.Device;
import com.rutong.medical.admin.vo.device.DeviceVO;
import com.soft.common.core.object.MyPageData;

/**
 * 设备Service接口
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
public interface DeviceService extends IService<Device> {

    /**
     * 删除设备
     * 
     * @param id
     */
    void delete(Long id);

    /**
     * 设备分页查询
     * 
     * @param devicePageQueryDTO
     * @return
     */
    MyPageData<DeviceVO> page(DevicePageQueryDTO devicePageQueryDTO);

    /**
     * 新增或者修改设备
     * 
     * @param deviceTerminalSaveDTO
     */
    void saveOrUpdate(DeviceTerminalSaveDTO deviceTerminalSaveDTO);

    /**
     * 查询系统名称
     * 
     * @return
     */
    List<Map<String, String>> listSystem();

    /**
     * 设备详情接口
     * 
     * @param id
     * @return
     */
    DeviceVO detail(Long id);

    /**
     * 设备查询列表
     * 
     * @param devicePageQueryDTO
     * @return
     */
    List<DeviceVO> list(DevicePageQueryDTO devicePageQueryDTO);


}
