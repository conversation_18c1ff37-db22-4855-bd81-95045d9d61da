CREATE TABLE `sm_device_base_station` (
`id` bigint(20) NOT NULL COMMENT '信标表ID',
`device_base_station_code` varchar(30) DEFAULT NULL COMMENT '基站编号',
`device_base_station_type` bigint(20) DEFAULT NULL COMMENT '基站分类表ID',
`device_base_station_name` varchar(50) DEFAULT NULL COMMENT '基站名称',
`protocol` varchar(40) DEFAULT NULL COMMENT '支持协议',
`ip` varchar(30) DEFAULT NULL COMMENT 'IP地址',
`space_id` bigint(20) DEFAULT NULL COMMENT '安装位置',
`space_path` varchar(255) DEFAULT NULL COMMENT '所属楼层路径',
`space_full_name` varchar(255) DEFAULT NULL COMMENT '所属楼层全名称',
`x` float DEFAULT NULL COMMENT 'X',
`y` float DEFAULT NULL COMMENT 'Y',
`z` float DEFAULT NULL COMMENT 'Z',
`longitude` float DEFAULT NULL COMMENT '经度',
`latitude` float DEFAULT NULL COMMENT '纬度',
`is_online` tinyint(1) DEFAULT NULL COMMENT '在线状态(1:在线,0:离线)',
`create_user_id` bigint(20) DEFAULT NULL COMMENT '创建人',
`create_time` datetime DEFAULT NULL COMMENT '创建时间',
`update_user_id` bigint(20) DEFAULT NULL COMMENT '修改人',
`update_time` datetime DEFAULT NULL COMMENT '修改时间',
`is_delete` tinyint(1) DEFAULT NULL COMMENT '是否删除',
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='基站表';




CREATE TABLE `sm_device_base_station_type` (
`id` bigint(20) NOT NULL COMMENT '基站分类表ID',
`type_code` varchar(30) DEFAULT NULL COMMENT '编号',
`type_name` varchar(30) DEFAULT NULL COMMENT '名称',
`parent_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '父级id',
`path_id` varchar(255) DEFAULT NULL COMMENT 'id路径',
`create_user_id` bigint(20) DEFAULT NULL COMMENT '创建人',
`create_time` datetime DEFAULT NULL COMMENT '创建时间',
`update_user_id` bigint(20) DEFAULT NULL COMMENT '修改人',
`update_time` datetime DEFAULT NULL COMMENT '修改时间',
`is_delete` tinyint(1) DEFAULT NULL COMMENT '是否删除',
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='基站分类表';



CREATE TABLE `sm_device_terminal_type` (
`id` bigint(20) NOT NULL COMMENT '设备分类表ID',
`type_code` varchar(30) DEFAULT NULL COMMENT '编号',
`type_name` varchar(30) DEFAULT NULL COMMENT '名称',
`parent_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '父级id',
`path_id` varchar(255) DEFAULT NULL COMMENT 'id路径',
`create_user_id` bigint(20) DEFAULT NULL COMMENT '创建人',
`create_time` datetime DEFAULT NULL COMMENT '创建时间',
`update_user_id` bigint(20) DEFAULT NULL COMMENT '修改人',
`update_time` datetime DEFAULT NULL COMMENT '修改时间',
`is_delete` tinyint(1) DEFAULT NULL COMMENT '是否删除',
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备分类表';

ALTER TABLE smart_medical.common_sys_user ADD `device_sn` varchar(64) DEFAULT NULL COMMENT '绑定终端信息';
ALTER TABLE smart_medical.sm_device ADD `device_sn` varchar(64) DEFAULT NULL COMMENT '绑定终端信息';




ALTER TABLE smart_medical.common_sys_user ADD `photo_url` varchar(255)  DEFAULT NULL COMMENT '头像';



ALTER TABLE smart_medical.common_sys_user ADD
    `employee_number` varchar(25) DEFAULT NULL COMMENT '工号';

ALTER TABLE smart_medical.common_sys_user ADD
    `extension_number` varchar(25) DEFAULT NULL COMMENT '分机号';

---------------------TDengine----------------------

create STABLE IF NOT EXISTS medical_iot.device (
  create_time TIMESTAMP,
  is_online TINYINT,
  update_time TIMESTAMP,
	  id BIGINT,
  device_terminal_type_id BIGINT,
  device_code VARCHAR(30),
  device_name VARCHAR(50),
  business_code VARCHAR(30),
  space_id BIGINT,
  x FLOAT,
  y FLOAT,
  z FLOAT,
  longitude FLOAT,
  latitude FLOAT,
  create_user_id BIGINT,
  update_user_id BIGINT
) TAGS (
  device_sn VARCHAR(30)
);

CREATE STABLE IF NOT EXISTS medical_iot.alarm_location_detail (
    alarm_date TIMESTAMP,
    is_alarm TINYINT,
    new_locator_sn VARCHAR(30),
    old_locator_sn VARCHAR(30),
    user_id BIGINT,
    user_name VARCHAR(40),
    base_station_sn VARCHAR(30),
    buildingid BIGINT,
    floorid BIGINT,
    pointid BIGINT,
    buildingname VARCHAR(60),
    floorsame VARCHAR(60),
    pointname VARCHAR(60),
    alarm_detail_id VARCHAR(30),
    alarm_type TINYINT
) TAGS (
    alarm_way TINYINT,
    device_sn VARCHAR(30)
);

