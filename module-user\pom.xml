<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.soft</groupId>
        <artifactId>smart-medical</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <!-- 用户模块  -->
    <artifactId>module-user</artifactId>
    <packaging>jar</packaging>
    <name>module-user</name>
    <version>1.0.0</version>
    <!--    <modules>-->
    <!--       <module>application-user</module>-->
    <!--    </modules>-->

    <dependencies>
<!--        <dependency>-->
<!--            <groupId>com.soft</groupId>-->
<!--            <artifactId>common-ffmpeg-operate</artifactId>-->
<!--            <version>1.0.0</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-ext</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-redis</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-online-api</artifactId>
            <version>1.0.0</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.soft</groupId>-->
<!--            <artifactId>common-flow-online</artifactId>-->
<!--            <version>1.0.0</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-log</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-minio</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-sequence</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-datafilter</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-swagger</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-dict</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-app-push</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>shardingsphere-jdbc-core-spring-boot-starter</artifactId>
            <version>5.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-dbcp</artifactId>
            <version>10.0.16</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingtalk</artifactId>
            <version>1.1.96</version>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-executor-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dingtalk.open</groupId>
            <artifactId>app-stream-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-messaging</artifactId>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>
</project>
