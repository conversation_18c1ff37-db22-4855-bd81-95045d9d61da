<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rutong.medical.admin.mapper.device.DeviceTDMapper">


    <insert id="insertToSubTable" parameterType="map">
        INSERT INTO ${tbName}
        <trim prefix="USING device TAGS (" suffix=")" suffixOverrides=",">
            <if test="entity.id != null">#{entity.id},</if>
            <if test="entity.device_terminal_type_id != null">#{entity.device_terminal_type_id},</if>
            <if test="entity.device_code != null">#{entity.device_code},</if>
            <if test="entity.device_name != null">#{entity.device_name},</if>
            <if test="entity.business_code != null">#{entity.business_code},</if>
            <if test="entity.space_id != null">#{entity.space_id},</if>
            <if test="entity.x != null">#{entity.x},</if>
            <if test="entity.y != null">#{entity.y},</if>
            <if test="entity.z != null">#{entity.z},</if>
            <if test="entity.longitude != null">#{entity.longitude},</if>
            <if test="entity.latitude != null">#{entity.latitude},</if>
            <if test="entity.create_user_id != null">#{entity.create_user_id},</if>
            <if test="entity.create_time != null">#{entity.create_time},</if>
            <if test="entity.update_user_id != null">#{entity.update_user_id}</if>
        </trim>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="entity.ts != null">ts,</if>
            <if test="entity.is_online != null">is_online,</if>
            <if test="entity.update_time != null">update_time</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="entity.ts != null">#{entity.ts},</if>
            <if test="entity.is_online != null">#{entity.is_online},</if>
            <if test="entity.update_time != null">#{entity.update_time}</if>
        </trim>
    </insert>


    <!-- 插入设备数据 -->
    <insert id="insert" parameterType="com.rutong.medical.admin.entity.device.DeviceTD">
        INSERT INTO device (ts, is_online, update_time,
                            id, device_terminal_type_id, device_code, device_name,
                            business_code, space_id, x, y, z,
                            longitude, latitude, create_user_id,
                            create_time, update_user_id)
        VALUES (#{ts}, #{is_online}, #{update_time},
                #{id}, #{device_terminal_type_id}, #{device_code}, #{device_name},
                #{business_code}, #{space_id}, #{x}, #{y}, #{z},
                #{longitude}, #{latitude}, #{create_user_id},
                #{create_time}, #{update_user_id})
    </insert>

    <!-- 批量插入设备数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO device (
        ts, is_online, update_time,
        id, device_terminal_type_id, device_code, device_name,
        business_code, space_id, x, y, z,
        longitude, latitude, create_user_id,
        create_time, update_user_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.ts}, #{item.is_online}, #{item.update_time},
            #{item.id}, #{item.device_terminal_type_id}, #{item.device_code}, #{item.device_name},
            #{item.business_code}, #{item.space_id}, #{item.x}, #{item.y}, #{item.z},
            #{item.longitude}, #{item.latitude}, #{item.create_user_id},
            #{item.create_time}, #{item.update_user_id}
            )
        </foreach>
    </insert>

    <!-- 根据ID查询设备 -->
    <select id="selectById" resultType="com.rutong.medical.admin.entity.device.DeviceTD">
        SELECT *
        FROM device
        WHERE id = #{id}
    </select>

    <!-- 根据设备编码查询 -->
    <select id="selectByDeviceCode" resultType="com.rutong.medical.admin.entity.device.DeviceTD">
        SELECT *
        FROM device
        WHERE device_code = #{deviceCode}
    </select>

    <!-- 根据空间ID查询设备 -->
    <select id="selectBySpaceId" resultType="com.rutong.medical.admin.entity.device.DeviceTD">
        SELECT *
        FROM device
        WHERE space_id = #{spaceId}
    </select>

    <!-- 根据时间范围查询设备 -->
    <select id="selectByTimeRange" resultType="com.rutong.medical.admin.entity.device.DeviceTD">
        <![CDATA[
        SELECT *
        FROM device
        WHERE ts >= #{start}
          AND ts <= #{end}
        ]]>
    </select>

    <!-- 删除设备 -->
    <delete id="deleteByDeviceSn">
        DROP TABLE #{deviceSn}
    </delete>


    <insert id="insertIntoDeviceTable" parameterType="map">
        INSERT INTO ${params.tableName}
        USING device TAGS (#{params.deviceData.deviceSn})  <!-- 只包含一个标签 -->
        VALUES (
        #{params.deviceData.createTime},
        #{params.deviceData.isOnline},
        #{params.deviceData.updateTime},
        #{params.id},
        #{params.deviceData.deviceTerminalTypeId},
        #{params.deviceData.deviceCode},
        #{params.deviceData.deviceName},
        #{params.deviceData.businessCode},
        #{params.deviceData.spaceId},
        #{params.deviceData.x},
        #{params.deviceData.y},
        #{params.deviceData.z},
        #{params.deviceData.longitude},
        #{params.deviceData.latitude},
        #{params.deviceData.createUserId},
        #{params.deviceData.updateUserId}
        )
    </insert>

    <!-- 根据时间范围查询设备 -->
    <select id="getSpaceFullName" resultType="com.rutong.medical.admin.entity.device.AlarmLocationDetailTD">
        SELECT *
        from ${tableName}
        where device_sn = #{deviceSn}
        and device_type_code = #{deviceTypeCode}
        order by create_time desc limit 1
    </select>
</mapper>