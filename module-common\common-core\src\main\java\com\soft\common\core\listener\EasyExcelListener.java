package com.soft.common.core.listener;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.google.common.collect.Lists;
import com.soft.common.core.util.easyexcel.*;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description:easyExcel监听器
 * @author: duanyashu
 * @time: 2020-07-10 09:13
 */
@Slf4j
public class EasyExcelListener<T> extends AnalysisEventListener<T> {

    /**
     * 成功数据结果集
     */
    private List<T> successList = Lists.newArrayList();

    /**
     * 错误数据结果集
     */
    private List<ExcelImportErrDto> errList = Lists.newArrayList();

    /**
     * 错误数据结果集
     */
    private List<ExcelImportErrDto> allList = Lists.newArrayList();

    //处理逻辑service
    private ExcelCheckManager excelCheckManager;

    /**
     * 每隔5条存储数据库，实际使用中可以3000条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 100;

    /**
     * 存放解析的临时对象
     */
    private List<T> list = new ArrayList<>();

    /**
     * excel对应的实体对象的反射类
     */
    private Class<T> clazz;

    /**
     * 是否处理导入错误数据
     */
    private boolean isErrorExport = true;

    private Long batchId;

    /**
     * 头行数量
     */
    private Integer headRowNumber  = 0 ;

    private HttpServletResponse response;

    public EasyExcelListener(HttpServletResponse response, ExcelCheckManager excelCheckManager, Class clazz, Long batchId) {
        this.excelCheckManager = excelCheckManager;
        this.clazz = clazz;
        this.response = response;
        this.batchId = batchId;
    }

    public EasyExcelListener(HttpServletResponse response, ExcelCheckManager customCheckService, Class clazz, Long batchId, Integer headRowNumber) {
        this.excelCheckManager = customCheckService;
        this.clazz = clazz;
        this.response = response;
        this.batchId = batchId;
        this.headRowNumber = headRowNumber;
    }

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param t               one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param analysisContext
     */
    @Override
    public void invoke(T t, AnalysisContext analysisContext) {
        Map<Integer, String> resultMap = null;
        try {
            //根据excel数据实体中的javax.validation + 正则表达式来校验excel数据
            resultMap = EasyExcelValiHelper.validateEntity(t);
        } catch (NoSuchFieldException e) {
            throw new ExcelAnalysisException("第" + analysisContext.readRowHolder().getRowIndex() + "行解析数据出错");
        }

        if (excelCheckManager != null) {
            resultMap = excelCheckManager.checkImportExcel(t,resultMap,batchId);
        }

        if (resultMap != null && resultMap.size() > 0) {
            ExcelImportErrDto excelImportErrObjectDto = new ExcelImportErrDto(t, resultMap);
            errList.add(excelImportErrObjectDto);
        }

        ExcelImportErrDto excelImportErrObjectDto = new ExcelImportErrDto(t, resultMap);
        allList.add(excelImportErrObjectDto);

        //每批次处理一次
        if (allList.size() % BATCH_COUNT == 0 || allList.size() == analysisContext.getTotalCount()  - (this.headRowNumber == 0 ? 1 : this.headRowNumber)){
            Integer size = allList.size() - (allList.size() % BATCH_COUNT==0?BATCH_COUNT : allList.size() % BATCH_COUNT);
            //自主校验
            if (excelCheckManager!=null){
                ExcelCheckResult result = excelCheckManager.checkImportExcel(allList.subList(size,allList.size()), batchId);
                errList.addAll(result.getErrDtos());
                successList.addAll(result.getSuccessDtos());
            }
        }
    }

    /**
     * 所有数据解析完成了 都会来调用
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if (isErrorExport) {
            try {
                //如果校验的总行数和excel的行数不一致，则导重新添加
                if(allList.size() < analysisContext.readRowHolder().getRowIndex()){
                    Integer size = allList.size() - (allList.size() % BATCH_COUNT==0?BATCH_COUNT : allList.size() % BATCH_COUNT);
                    //自主校验
                    if (excelCheckManager!=null){
                        ExcelCheckResult result = excelCheckManager.checkImportExcel(allList.subList(size,allList.size()), batchId);
                        errList.addAll(result.getErrDtos());
                        successList.addAll(result.getSuccessDtos());
                    }
                }

                exportErrorExcel();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        allList.clear();
    }


    /**
     * @param headMap 传入excel的头部（第一行数据）数据的index,name
     * @description: 校验excel头部格式，必须完全匹配
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        super.invokeHeadMap(headMap, context);
        if (clazz != null) {
            //判断 表头和内容数量是否一致
            try {
                Map<Integer, String> indexNameMap = getIndexNameMap(clazz);
                if (headMap.size() != indexNameMap.size()) {
//                    throw new ExcelAnalysisException("解析excel出错，请传入正确格式的excel");
                }
            } catch (NoSuchFieldException e) {
                log.error("解析excel出错，表头和内容不对应,请传入正确格式的excel");
            }
        }
    }

    /**
     * @param clazz
     * @return 错误数据的坐标和提示  （eg:0-不能为空）
     * @throws
     * @description: 获取注解里ExcelProperty的value，用作校验excel
     */
    public Map<Integer, String> getIndexNameMap(Class clazz) throws NoSuchFieldException {
        Map<Integer, String> result = new HashMap<>();
        Field field;
        Field[] fields = clazz.getDeclaredFields();
        for (int i = 0; i < fields.length; i++) {
            field = clazz.getDeclaredField(fields[i].getName());
            field.setAccessible(true);
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            if (excelProperty != null) {
                int index = excelProperty.index();
                index = index == -1 ? i : index;
                String[] values = excelProperty.value();
                StringBuilder value = new StringBuilder();
                for (String v : values) {
                    value.append(v);
                }
                result.put(index, value.toString());
            }
        }
        return result;
    }

    /**
     * 获取错误数据
     *
     * @return
     */
    public List<ExcelImportErrDto> getErrList() {
        return errList;
    }

    /**
     * 获取正确数据
     *
     * @return
     */
    public List<T> getSuccessList() {
        return successList;
    }

    /**
     * 错误数据导出
     *
     * @throws IOException
     */
    private void exportErrorExcel() throws IOException {
        if (errList.size() == 0) {
            return;
        }

        //错误结果集
        List<T> userResultList = allList.stream().map(excelImportErrDto -> {
            return (T) excelImportErrDto.getObject();
        }).collect(Collectors.toList());

        List<Map<Integer, String>> errMsgList = allList.stream().map(excelImportErrDto -> {
            return excelImportErrDto.getCellMap();
        }).collect(Collectors.toList());

        // 智慧运维->排班考勤->排版管理->排班管理 修改（下载后打不开xlsx文件问题）
        try {
            // 清除之前的响应内容
            response.reset();
            // 设置正确的响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode("error.xlsx", "UTF-8"));
            
            //导出excel
            EasyExcelUtils.webWriteExcel(response, userResultList, clazz, errMsgList, "error");
        } catch (Exception e) {
            log.error("导出错误Excel文件失败", e);
            throw new IOException("导出错误Excel文件失败", e);
        }
    }
}