package com.soft.common.online.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import com.soft.common.core.base.dao.BaseDaoMapper;
import com.soft.common.core.base.service.BaseService;
import com.soft.common.core.config.CoreProperties;
import com.soft.common.core.config.DataSourceContextHolder;
import com.soft.common.core.object.MyRelationParam;
import com.soft.common.sequence.wrapper.IdGeneratorWrapper;
import com.soft.common.online.config.OnlineProperties;
import com.soft.common.online.dao.OnlineDblinkMapper;
import com.soft.common.online.model.OnlineDblink;
import com.soft.common.online.object.SqlTable;
import com.soft.common.online.object.SqlTableColumn;
import com.soft.common.online.service.OnlineDblinkService;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据库链接数据操作服务类。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
@Slf4j
@Service("onlineDblinkService")
public class OnlineDblinkServiceImpl extends BaseService<OnlineDblink, Long> implements OnlineDblinkService {

    @Autowired
    private OnlineDblinkMapper onlineDblinkMapper;
    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private OnlineProperties onlineProperties;

    private Map<Serializable, OnlineDblink> dblinkMap;

    @PostConstruct
    public void loadAllDblink() {
        List<OnlineDblink> dblinkList = super.getAllList();
        this.dblinkMap = dblinkList.stream().collect(Collectors.toMap(OnlineDblink::getDblinkId, c -> c));
    }

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<OnlineDblink> mapper() {
        return onlineDblinkMapper;
    }

    @Override
    public OnlineDblink getById(Serializable dblinkId) {
        return dblinkMap.get(dblinkId);
    }

    @Override
    public List<OnlineDblink> getOnlineDblinkList(OnlineDblink filter, String orderBy) {
        return onlineDblinkMapper.getOnlineDblinkList(filter, orderBy);
    }

    @Override
    public List<OnlineDblink> getOnlineDblinkListWithRelation(OnlineDblink filter, String orderBy) {
        List<OnlineDblink> resultList = onlineDblinkMapper.getOnlineDblinkList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    @Override
    public List<SqlTable> getDblinkTableList(OnlineDblink dblink) {
        Integer originalType = DataSourceContextHolder.setDataSourceType(dblink.getDblinkConfigConstant());
        try {
            List<Map<String, Object>> resultList =
                    onlineDblinkMapper.getTableListWithPrefix(onlineProperties.getTablePrefix());
            List<SqlTable> tableList = new LinkedList<>();
            resultList.forEach(r -> {
                SqlTable sqlTable = BeanUtil.mapToBean(r, SqlTable.class, false, null);
                sqlTable.setDblinkId(dblink.getDblinkId());
                tableList.add(sqlTable);
            });
            return tableList;
        } finally {
            DataSourceContextHolder.unset(originalType);
        }
    }

    @Override
    public SqlTable getDblinkTable(OnlineDblink dblink, String tableName) {
        Integer originalType = DataSourceContextHolder.setDataSourceType(dblink.getDblinkConfigConstant());
        try {
            Map<String, Object> result = onlineDblinkMapper.getTableByName(tableName);
            if (result == null) {
                return null;
            }
            SqlTable sqlTable = BeanUtil.mapToBean(result, SqlTable.class, false, null);
            sqlTable.setDblinkId(dblink.getDblinkId());
            sqlTable.setColumnList(getDblinkTableColumnList(dblink, tableName));
            return sqlTable;
        } finally {
            DataSourceContextHolder.unset(originalType);
        }
    }

    @Override
    public List<SqlTableColumn> getDblinkTableColumnList(OnlineDblink dblink, String tableName) {
        Integer originalType = DataSourceContextHolder.setDataSourceType(dblink.getDblinkConfigConstant());
        try {
            List<Map<String, Object>> resultList = onlineDblinkMapper.getTableColumnList(tableName);
            List<SqlTableColumn> columnList = new LinkedList<>();
            resultList.forEach(r -> {
                SqlTableColumn sqlTableColumn =
                        BeanUtil.mapToBean(r, SqlTableColumn.class, false, null);
                this.makeupSqlTableColumn(sqlTableColumn);
                columnList.add(sqlTableColumn);
            });
            return columnList;
        } finally {
            DataSourceContextHolder.unset(originalType);
        }
    }

    @Override
    public SqlTableColumn getDblinkTableColumn(OnlineDblink dblink, String tableName, String columnName) {
        Integer originalType = DataSourceContextHolder.setDataSourceType(dblink.getDblinkConfigConstant());
        try {
            Map<String, Object> result = onlineDblinkMapper.getTableColumnByName(tableName, columnName);
            if (result == null) {
                return null;
            }
            SqlTableColumn sqlTableColumn = BeanUtil.mapToBean(
                    result, SqlTableColumn.class, false, CopyOptions.create().ignoreCase());
            this.makeupSqlTableColumn(sqlTableColumn);
            return sqlTableColumn;
        } finally {
            DataSourceContextHolder.unset(originalType);
        }
    }

    private void makeupSqlTableColumn(SqlTableColumn sqlTableColumn) {
        switch (onlineProperties.getDatabaseType()) {
            case CoreProperties.POSTGRESQL_TYPE:
                if (StrUtil.equalsAny(sqlTableColumn.getColumnType(), "char", "varchar")) {
                    sqlTableColumn.setFullColumnType(
                            sqlTableColumn.getColumnType() + "(" + sqlTableColumn.getStringPrecision() + ")");
                } else {
                    sqlTableColumn.setFullColumnType(sqlTableColumn.getColumnType());
                }
                break;
            case CoreProperties.MYSQL_TYPE:
                sqlTableColumn.setAutoIncrement("auto_increment".equals(sqlTableColumn.getExtra()));
                break;
            case CoreProperties.ORACLE_TYPE:
                if (StrUtil.equalsAny(sqlTableColumn.getColumnType(), "VARCHAR2", "NVARCHAR2", "CHAR", "NCHAR")) {
                    sqlTableColumn.setFullColumnType(
                            sqlTableColumn.getColumnType() + "(" + sqlTableColumn.getStringPrecision() + ")");
                } else if (StrUtil.equals(sqlTableColumn.getColumnType(), "NUMBER")) {
                    sqlTableColumn.setFullColumnType(sqlTableColumn.getColumnType() +
                            "(" + sqlTableColumn.getNumericPrecision() + "," + sqlTableColumn.getNumericScale() + ")");
                } else {
                    sqlTableColumn.setFullColumnType(sqlTableColumn.getColumnType());
                }
                break;
            default:
                break;
        }
    }
}
