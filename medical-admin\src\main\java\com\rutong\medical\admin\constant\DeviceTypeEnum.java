package com.rutong.medical.admin.constant;

import lombok.Getter;

/**
 * @ClassName AlarmWayConstant
 * @Description 设备类型
 * <AUTHOR>
 * @Date 2025/7/15 9:23
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Getter
public enum DeviceTypeEnum {

    /**
     * lora版报警器
     */
    SIREN_LORA(7,SystemTypeEnum.MEDICAL_SECURITY),
    /**
     * lora工卡
     */
    CARD_LORA(9,SystemTypeEnum.MEDICAL_SECURITY),
    /**
     * lora版红外探测
     */
    INFRARED_INTRUSION(8,SystemTypeEnum.WIRELESS_ALARM);

    private Integer code;
    private SystemTypeEnum systemType;

    DeviceTypeEnum(int code, SystemTypeEnum systemType) {
        this.code = code;
        this.systemType = systemType;
    }

}
