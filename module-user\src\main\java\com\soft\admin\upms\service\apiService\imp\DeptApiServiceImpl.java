package com.soft.admin.upms.service.apiService.imp;

import com.github.pagehelper.PageHelper;
import com.soft.admin.upms.dao.SysDeptMapper;
import com.soft.admin.upms.dto.SysDeptDto;
import com.soft.admin.upms.model.SysDept;
import com.soft.admin.upms.service.apiService.DeptApiService;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.util.MyPageUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DeptApiServiceImpl implements DeptApiService {

    @Autowired
    private SysDeptMapper sysDeptMapper;


    @Override
    public MyPageData<SysDept> list(SysDeptDto sysDeptDto) {
        Integer pageNum  = 1;
        Integer pageSize = 99999;
        if (pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize).setReasonable(false);;
        }
        SysDept sysDept = new SysDept();
        sysDept.setDeletedFlag(1);
        List<SysDept> userList = sysDeptMapper.getSysDeptApiList(sysDept,"create_time desc");
        return MyPageUtil.makeResponseData(userList);
    }
}
