package com.rutong.medical.admin;

import org.junit.jupiter.api.Test;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.redisson.client.RedisClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @ClassName RedisTest
 * @Description
 * <AUTHOR>
 * @Date 2025/7/17 11:09
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@SpringBootTest
public class RedisTest {

    @Autowired
    private RedissonClient redissonClient;

    @Test
    public void testMap() {
        RMap<String, String> map = redissonClient.getMap("mymap");
        String value = map.get("key1");

        map.put("key1", "value1");
        map.put("key2", "value2");

        System.out.println(map.get("key1"));
    }
}
