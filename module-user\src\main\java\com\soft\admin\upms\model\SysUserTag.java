package com.soft.admin.upms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.admin.upms.vo.SysUserTagVO;
import com.soft.common.core.base.mapper.BaseModelMapper;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 用户标签关系对象 common_sys_user_tag
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
@Data
@TableName(value = "common_sys_user_tag")
public class SysUserTag {

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 标签Id
     */
    private Long tagId;


    @Mapper
    public interface SysUserTagModelMapper extends BaseModelMapper<SysUserTagVO, SysUserTag> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        SysUserTag toModel(SysUserTagVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        SysUserTagVO fromModel(SysUserTag entity);
    }

    public static final SysUserTagModelMapper INSTANCE = Mappers.getMapper(SysUserTagModelMapper.class);
}
