package com.rutong.medical.admin.vo.system;

import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SpaceVO视图对象
 *
 * <AUTHOR>
 * @date 2023-03-29
 */
@ApiModel("SpaceVO视图对象")
@Data
public class SpaceVO {
    @ApiModelProperty(value = "空间完整名称")
    private Long id;

    @ApiModelProperty(value = "上级id")
    private Long parentId;

    @ApiModelProperty(value = "空间路径（使用/分割id）")
    private String path;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "空间类型（AREA区域，BUILDING楼栋，FLOOR楼层，POINT点位）")
    private String type;

    @ApiModelProperty(value = "空间名称")
    private String name;

    @ApiModelProperty(value = "空间完整名称（使用/分割名称）")
    private String fullName;

    @ApiModelProperty(value = "坐标")
    private String coordinate;

    @ApiModelProperty(value = "删除（0否，1是）")
    private Integer deleted;

    @ApiModelProperty(value = "创建者id")
    private Long createUserId;

    @ApiModelProperty(value = "创建者名称")
    private String createUsername;

    @ApiModelProperty(value = "创建时间")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private Date createTime;

    @ApiModelProperty(value = "更新者id")
    private Long updateUserId;

    @ApiModelProperty(value = "最后更新时间")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private Date updateTime;
    /**
     *是否公共区域 0否1是
     */
    @ApiModelProperty(value = "是否公共区域 0否1是")
    private String commonArea;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Long sort;

    @ApiModelProperty(value = "二维模型文件地址")
    private String modelTwoDimensional;

    @ApiModelProperty(value = "三维模型文件地址")
    private String modelThreeDimensional;
}
