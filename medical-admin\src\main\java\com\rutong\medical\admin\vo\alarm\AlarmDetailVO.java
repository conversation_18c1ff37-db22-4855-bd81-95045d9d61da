package com.rutong.medical.admin.vo.alarm;

import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * @ClassName AlarmDetailVO
 * @Description
 * <AUTHOR>
 * @Date 2025/7/16 14:39
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@ApiModel("AlarmDetailVO视图对象")
@Data
public class AlarmDetailVO {

    @ApiModelProperty(value = "报警记录表ID")
    private Long id;

    @ApiModelProperty(value = "基站表ID")
    private Long deviceBaseStationId;

    @ApiModelProperty(value = "用户表ID")
    private Long userId;

    @ApiModelProperty(value = "设备ID")
    private Long deviceId;

    @ApiModelProperty(value = "设备sn")
    private String deviceSn;

    @ApiModelProperty(value = "业务系统编号")
    private String businessCode;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "设备分类表code")
    private String deviceTypeCode;

    @ApiModelProperty(value = "报警类型(1-低电压, 2-按键, 3-防拆, 4-红外入侵)")
    private Integer alarmType;

    @ApiModelProperty(value = "处理状态(0:未确认,1:已确认)")
    private Integer disposeState;

    @ApiModelProperty(value = "上报位置")
    private Long spaceId;

    @ApiModelProperty(value = "上报时间")
    private Date reportTime;

    @ApiModelProperty(value = "处理时间")
    private Date disposeTime;

}
