package com.soft.admin.upms.controller;

import com.soft.admin.upms.enums.OaTypeEnums;
import com.soft.admin.upms.service.OaSyncService;
import com.soft.common.core.object.ResponseResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Description
 * @Date 0017, 2023年8月17日 15:09
 * <AUTHOR>
 **/
@RequestMapping("/admin/upms/oa")
@RestController
public class OaController {

    @Value("${oa.type}")
    private String oaType;
    @Resource
    private OaSyncService oaSyncService;

    // 同步用户入口
    @PostMapping("/syncAll")
    public ResponseResult<Void> syncAll() {
        oaSyncService.syncAll(OaTypeEnums.valueOf(oaType));
        return ResponseResult.success();
    }

//    @PostMapping("/pushDeptToHikVision")
//    public ResponseResult<Void> pushDeptToHikVision() {
//        oaSyncService.pushDeptToHikVision();
//        return ResponseResult.success();
//    }
//
//    @PostMapping("/pushUserToHikVision")
//    public ResponseResult<Void> pushUserToHikVision() {
//        oaSyncService.pushUserToHikVision();
//        return ResponseResult.success();
//    }
//
//    @PostMapping("/pushAllToHikVision")
//    public ResponseResult<Void> pushAllToHikVision() {
//        oaSyncService.pushDeptToHikVision();
//        oaSyncService.pushUserToHikVision();
//        oaSyncService.pushUserFacePicToHikVision();
//        return ResponseResult.success();
//    }

}
