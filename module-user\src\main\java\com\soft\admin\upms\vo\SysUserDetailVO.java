package com.soft.admin.upms.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.soft.admin.upms.model.SysDeptPost;
import com.soft.admin.upms.model.SysRole;
import com.soft.admin.upms.model.SysTag;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SysUserDetailVO {

    /**
     * 用户Id。
     */
    @ApiModelProperty(value = "用户Id")
    private Long userId;

    /**
     * 登录用户名。
     */
    @ApiModelProperty(value = "登录用户名")
    private String loginName;

    /**
     * 用户显示名称。
     */
    @ApiModelProperty(value = "用户显示名称")
    private String showName;

    /**
     * 用户手机号码
     */
    @ApiModelProperty(value = "用户手机号码")
    private String phone;

    /**
     * 手机短号
     */
    @ApiModelProperty(value = "手机短号")
    private String shortPhone;

    @ApiModelProperty(value = "身份证号")
    private String cardNo;


    @ApiModelProperty(value = "性别，1男，2女，3未知")
    private Integer sex;


    @ApiModelProperty(value = "用户状态(0: 正常 1: 锁定)")
    private Integer userStatus;


    @ApiModelProperty(value = "用户类型(0: 管理员 1: 系统管理用户 2: 系统业务用户)")
    private Integer userType;


    @ApiModelProperty(value = "创建时间")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private Date createTime;

    // 最后登录时间
    private Date lastLoginTime;

    // 部门信息
    private SysDeptVo sysDeptVO;

    @ApiModelProperty(value = "部门信息列表")
    private List<SysDeptVo> sysDeptVOs;

    // 角色信息
    private List<SysRole> sysRoles;

    // 岗位信息
    private List<SysDeptPost> sysDeptPosts;

    // 用户标签信息
    private List<SysTag> sysTags;

    @ApiModelProperty("终端信息")
    private String deviceSn;
    /**
     * 头像
     */
    @ApiModelProperty(value = "头像")
    private String photoUrl;
    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String employeeNumber;

    /**
     * 分机号
     */
    @ApiModelProperty(value = "分机号")
    private String extensionNumber;

}
