package com.rutong.medical.admin.vo.system;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SpaceVO视图对象
 *
 * <AUTHOR>
 * @date 2023-03-29
 */
@ApiModel("SpaceVO视图对象")
@Data
public class SpaceQueryVO {
    @ApiModelProperty(value = "楼栋名称")
    private String buildingName;

    @ApiModelProperty(value = "楼层名称")
    private String flowName;

    @ApiModelProperty(value = "点位名称")
    private String pointName;

}
