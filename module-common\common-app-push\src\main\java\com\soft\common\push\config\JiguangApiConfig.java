package com.soft.common.push.config;

import cn.jiguang.sdk.api.PushApi;
import feign.Logger;
import feign.okhttp.OkHttpClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Slf4j
@ConditionalOnProperty(prefix = "jiguang-push", name = "switch-flag", havingValue = "true")
@Configuration
public class JiguangApiConfig {
    @Value("${jiguang-push.app-key}")
    private String appKey;

    @Value("${jiguang-push.master-secret}")
    private String masterSecret;

    @Value("${jiguang-push.proxy-url:https://api.jpush.cn}")
    private String jgProxyUrl;

    // sdk默认使用的feign-okhttp，下面是设置示例
    // 更多okhttp配置请参考：https://square.github.io/okhttp/5.x/okhttp/okhttp3/-ok-http-client/-builder/index.html
    @Bean("okHttpClient")
    public OkHttpClient okHttpClient() {
        okhttp3.OkHttpClient okHttpClient = new okhttp3.OkHttpClient().newBuilder()
                //设置代理，如果有需要
                //.proxy()
                // 设置连接超时
                .connectTimeout(5, TimeUnit.SECONDS).build();
        OkHttpClient okHttpClient1 = new OkHttpClient(okHttpClient);
        log.info("okHttpClient1:{}", okHttpClient1);
        return okHttpClient1;
    }

    @Bean
    public PushApi pushApi(@Qualifier("okHttpClient") OkHttpClient okHttpClient) {
        // 如果不配置client，则使用默认的okHttpClient
        return new PushApi.Builder().setClient(okHttpClient).setAppKey(appKey).setMasterSecret(masterSecret).setHost(jgProxyUrl).setLoggerLevel(Logger.Level.FULL).build();
    }
}
