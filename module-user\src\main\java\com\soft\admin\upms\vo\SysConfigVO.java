package com.soft.admin.upms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * SysConfigVO视图对象
 *
 * <AUTHOR>
 * @date 2023-09-14
 */
@ApiModel("SysConfigVO视图对象")
@Data
public class SysConfigVO {

    @ApiModelProperty(value = "键名称")
    private String configKey;

    @ApiModelProperty(value = "值")
    private String keyValue;

    @ApiModelProperty(value = "值类型(select:下拉框、input输入框、date:单时间选择器、datetimerange:双时间选择器、json)")
    private String valueType;

    @ApiModelProperty(value = "枚举值(如下拉[{key:'1',value:'值1'},{key:'2',value:'值2'}])")
    private String enumValue;

    @ApiModelProperty(value = "说明")
    private String explainInit;

    @ApiModelProperty(value = "是否显示 0显示 1不显示")
    private String isShow;

    @ApiModelProperty(value = "是否可编辑 0不可编辑 1可编辑")
    private String isEdit;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建者id")
    private Long createUserId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新者id")
    private Long updateUserId;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

}
