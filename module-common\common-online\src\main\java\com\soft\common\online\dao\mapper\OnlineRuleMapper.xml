<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.common.online.dao.OnlineRuleMapper">
    <resultMap id="BaseResultMap" type="com.soft.common.online.model.OnlineRule">
        <id column="rule_id" jdbcType="BIGINT" property="ruleId"/>
        <result column="rule_name" jdbcType="VARCHAR" property="ruleName"/>
        <result column="rule_type" jdbcType="INTEGER" property="ruleType"/>
        <result column="builtin" jdbcType="BOOLEAN" property="builtin"/>
        <result column="pattern" jdbcType="VARCHAR" property="pattern"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="deleted_flag" jdbcType="INTEGER" property="deletedFlag"/>
    </resultMap>

    <resultMap id="BaseResultMapWithOnlineColumnRule" type="com.soft.common.online.model.OnlineRule" extends="BaseResultMap">
        <association property="onlineColumnRule" column="rule_id" foreignColumn="rule_id"
                     notNullColumn="rule_id" resultMap="com.soft.common.online.dao.OnlineColumnRuleMapper.BaseResultMap" />
    </resultMap>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        AND zz_online_rule.deleted_flag = ${@com.soft.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <select id="getOnlineRuleList" resultMap="BaseResultMap" parameterType="com.soft.common.online.model.OnlineRule">
        SELECT * FROM zz_online_rule
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getOnlineRuleListByColumnId" resultMap="BaseResultMapWithOnlineColumnRule">
        SELECT
            zz_online_rule.*,
            zz_online_column_rule.*
        FROM
            zz_online_rule,
            zz_online_column_rule
        <where>
            AND zz_online_column_rule.column_id = #{columnId}
            AND zz_online_column_rule.rule_id = zz_online_rule.rule_id
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getNotInOnlineRuleListByColumnId" resultMap="BaseResultMap">
        SELECT
            zz_online_rule.*
        FROM
            zz_online_rule
        <where>
            AND NOT EXISTS (SELECT * FROM zz_online_column_rule
                WHERE zz_online_column_rule.column_id = #{columnId} AND zz_online_column_rule.rule_id = zz_online_rule.rule_id)
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
