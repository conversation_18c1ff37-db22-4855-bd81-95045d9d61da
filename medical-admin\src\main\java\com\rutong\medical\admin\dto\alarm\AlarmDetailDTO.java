package com.rutong.medical.admin.dto.alarm;

import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * @ClassName AlarmDetailDTO
 * @Description
 * <AUTHOR>
 * @Date 2025/7/16 14:42
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@ApiModel("AlarmDetailDTO对象")
@Data
public class AlarmDetailDTO {

    @ApiModelProperty(value = "报警记录表ID")
    @NotNull(message = "数据验证失败，报警记录表ID不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "基站表ID")
    private Long deviceBaseStationId;

    @ApiModelProperty(value = "用户表ID")
    private Long userId;

    @ApiModelProperty(value = "设备ID")
    private Long deviceId;

    @ApiModelProperty(value = "设备sn")
    private String deviceSn;

    @ApiModelProperty(value = "业务系统编号")
    private String businessCode;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "报警方式(7:报警器,8:红外探测,9:工卡)")
    private Integer alarmAwy;

    @ApiModelProperty(value = "报警类型(1:呼叫报警,2:低电量)")
    private Integer alarmType;

    @ApiModelProperty(value = "处理状态(0:未确认,1:已确认)")
    private Integer disposeState;

    @ApiModelProperty(value = "上报位置")
    private Long spaceId;

    @ApiModelProperty(value = "上报时间")
    private Date reportTime;

    @ApiModelProperty(value = "处理时间")
    private Date disposeTime;

}
