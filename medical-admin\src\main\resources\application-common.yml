common-core:
  # 可选值为 mysql / postgresql / oracle
  databaseType: mysql

common-online:
  # 可选值为 mysql / postgresql / oracle
  databaseType: mysql
  # 注意不要以反斜杠(/)结尾。
  operationUrlPrefix: /admin/online
  # 在线表单业务数据上传资源路径
  uploadFileBaseDir: ./zz-resource/upload-files/online
  # 如果为false，OnlineOperationController中的接口将不能使用。
  operationEnabled: true
  # 1: minio 2: aliyun-oss 3: qcloud-cos。
  distributeStoreType: 1
  # 下面的url列表，请保持反斜杠(/)结尾。
  viewUrlList:
    - ${common-online.operationUrlPrefix}/onlineOperation/viewByDatasourceId/
    - ${common-online.operationUrlPrefix}/onlineOperation/viewByOneToManyRelationId/
    - ${common-online.operationUrlPrefix}/onlineOperation/listByDatasourceId/
    - ${common-online.operationUrlPrefix}/onlineOperation/listByOneToManyRelationId/
    - ${common-online.operationUrlPrefix}/onlineOperation/exportByDatasourceId/
    - ${common-online.operationUrlPrefix}/onlineOperation/exportByOneToManyRelationId/
    - ${common-online.operationUrlPrefix}/onlineOperation/downloadDatasource/
    - ${common-online.operationUrlPrefix}/onlineOperation/downloadOneToManyRelation/
  editUrlList:
    - ${common-online.operationUrlPrefix}/onlineOperation/addDatasource/
    - ${common-online.operationUrlPrefix}/onlineOperation/addOneToManyRelation/
    - ${common-online.operationUrlPrefix}/onlineOperation/updateDatasource/
    - ${common-online.operationUrlPrefix}/onlineOperation/updateOneToManyRelation/
    - ${common-online.operationUrlPrefix}/onlineOperation/deleteDatasource/
    - ${common-online.operationUrlPrefix}/onlineOperation/deleteOneToManyRelation/
    - ${common-online.operationUrlPrefix}/onlineOperation/deleteBatchDatasource/
    - ${common-online.operationUrlPrefix}/onlineOperation/deleteBatchOneToManyRelation/
    - ${common-online.operationUrlPrefix}/onlineOperation/uploadDatasource/
    - ${common-online.operationUrlPrefix}/onlineOperation/uploadOneToManyRelation/

common-online-api:
  # 注意不要以反斜杠(/)结尾。
  urlPrefix: /admin/online

common-flow:
  # 请慎重修改urlPrefix的缺省配置，注意不要以反斜杠(/)结尾。如必须修改其他路径，请同步修改数据库脚本。
  urlPrefix: /admin/flow

common-log:
  # 操作日志配置，对应配置文件common-log/OperationLogProperties.java
  operation-log:
    enabled: true