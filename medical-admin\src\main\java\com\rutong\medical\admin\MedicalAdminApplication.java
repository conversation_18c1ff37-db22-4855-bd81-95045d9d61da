package com.rutong.medical.admin;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.rutong.medical.admin.config.SwaggerAutoConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 */

@EnableAsync
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, DruidDataSourceAutoConfigure.class})
@EnableAspectJAutoProxy(exposeProxy = true)
@ComponentScan(basePackages = {"com.rutong", "com.soft"})
@MapperScan(basePackages = { "com.soft.admin.upms.dao.**", "com.rutong.medical.admin.mapper.**"})
public class MedicalAdminApplication {

    public static void main(String[] args) {
        SpringApplication.run(MedicalAdminApplication.class, args);
    }

}
