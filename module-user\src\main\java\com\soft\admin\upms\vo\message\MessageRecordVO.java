package com.soft.admin.upms.vo.message;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import cn.hutool.core.collection.CollectionUtil;
import com.soft.admin.upms.enums.MessageRecordBusiTypeEnums;
import com.soft.admin.upms.enums.MessageRecordTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * SpMessageRecordVO视图对象
 *
 * <AUTHOR>
 * @date 2024-02-27
 */
@ApiModel("SpMessageRecordVO视图对象")
@Data
public class MessageRecordVO {

    @ApiModelProperty(value = "消息id")
    private Long id;

    @ApiModelProperty(value = "消息标题")
    private String title;

    @ApiModelProperty(value = "消息级别：普通、紧急、特急")
    private String level;

    @ApiModelProperty(value = "消息类型")
    private MessageRecordTypeEnums type;

    @ApiModelProperty(value = "消息内容")
    private String content;

    @ApiModelProperty(value = "PC消息内容")
    private String browserContent;

    @ApiModelProperty(value = "PC超链接")
    private MessageRecordBrowserVO browserHyperlink;

    @ApiModelProperty(value = "业务id")
    private Long busiId;

    @ApiModelProperty(value = "业务类型")
    private MessageRecordBusiTypeEnums busiType;

    @ApiModelProperty(value = "超链接")
    private String hyperlink;

    @ApiModelProperty(value = "发送日期")
    private Date sendDate;

    @ApiModelProperty(value = "发送时间")
    private Date sendTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "发送人id")
    private Long sendUserId;

    @ApiModelProperty(value = "接收人id")
    private Long receiveUserId;

    @ApiModelProperty(value = "接收时间")
    private Date receiveTime;

    @ApiModelProperty(value = "阅读时间")
    private Date readTime;

    @ApiModelProperty(value = "是否已读：0未读；1已读")
    private Integer isRead;

    @ApiModelProperty(value = "发布")
    private String releaseTime;
    @ApiModelProperty(value = "方法")
    private String releaseStatus;
    @ApiModelProperty(value = "接收用户")
    private List<Long> receiveList;
    private Integer receive = 0;

    @ApiModelProperty(value = "图片")
    private String img;

    @ApiModelProperty(value = "事件处理状态")
    private Integer status;

    @ApiModelProperty(value = "预警表主键")
    private Long earlyWarningId;

    @ApiModelProperty(value = "位置")
    private String spaceFullName;

    @ApiModelProperty(value = "子系统类型")
    private String subType;
}
