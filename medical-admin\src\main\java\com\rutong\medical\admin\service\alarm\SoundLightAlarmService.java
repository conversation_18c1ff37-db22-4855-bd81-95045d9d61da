package com.rutong.medical.admin.service.alarm;

import com.rutong.medical.admin.dto.alarm.SoundLightAlarmDTO;
import com.rutong.medical.admin.vo.alarm.SoundLightAlarmVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-07-15
 * 声光报警设置服务接口
 */
public interface SoundLightAlarmService {
    
    /**
     * 更新或查询报警配置（带三级缓存）
     * 如果DTO为空，则返回所有报警配置
     * 如果DTO不为空，则根据configCode更新对应的配置，并返回所有报警配置
     *
     * @param soundLightAlarmDTO 报警配置DTO（可选）
     * @return 所有报警配置列表
     */
    List<SoundLightAlarmVO> updateAlarmConfig(SoundLightAlarmDTO soundLightAlarmDTO);
} 