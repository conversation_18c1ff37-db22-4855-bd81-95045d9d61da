package com.rutong.medical.admin.dto.station;

import lombok.Data;

/**
 * @ClassName BaseStationDataDTO
 * @Description 基站数据
 * <AUTHOR>
 * @Date 2025/7/14 18:59
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Data
public class BaseStationDataDTO {

    /**
     * 标签类别
     */
    private String deviceType;
    /**
     * 标签ID
     */
    private String deviceSn;
    /**
     * 信号强度
     */
    private String rssi;
    /**
     * 新定位器Sn
     */
    private String newLocatorSn;
    /**
     * 旧定位器Sn
     */
    private String oldLocatorSn;
    /**
     * 低电压报警状态(1:报警,0:未报警)
     */
    private Byte lowVoltageStatus;
    /**
     * 按键报警状态(1:报警,0:未报警)
     */
    private Byte keyStatus;
    /**
     * 防拆报警状态(1:报警,0:未报警)
     */
    private Byte takeStatus;
    /**
     * 红外、烟雾报警状态(1:报警,0:未报警)
     */
    private Byte infraredStatus;
    /**
     * 在过滤时间类设备收到的次数
     */
    private Integer count;
    /**
     * 数据创建时间
     */
    private Long timestamp;
    /**
     * 当前发送消息基站Sn
     */
    private String baseStationSn;
}
