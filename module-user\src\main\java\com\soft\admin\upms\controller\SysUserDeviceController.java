package com.soft.admin.upms.controller;

import com.soft.admin.upms.dto.SysUserDeviceDTO;
import com.soft.admin.upms.service.SysUserDeviceService;
import com.soft.common.core.object.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @className: SysUserDeviceController
 * @author: WangZeYu
 * @date: 2024/9/13 上午9:42
 * @description:
 */
@Api(tags = "用户设备接口")
@Slf4j
@RestController
@RequestMapping("/admin/upms/sysUserDevice")
public class SysUserDeviceController {
    @Resource
    private SysUserDeviceService sysUserDeviceService;

    @ApiOperation("新增用户设备信息")
    @PostMapping("/insert")
    public ResponseResult<Boolean> insertUserDevice(@Valid @RequestBody SysUserDeviceDTO sysUserDevice) {
        return ResponseResult.success(sysUserDeviceService.insertUserDevice(sysUserDevice));
    }
}
