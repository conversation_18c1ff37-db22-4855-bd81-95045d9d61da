package com.soft.common.core.object;

import com.alibaba.fastjson.JSONArray;
import com.soft.common.core.constant.ErrorCodeEnum;
import com.soft.common.core.exception.MyRuntimeException;
import com.soft.common.core.util.ContextUtil;
import lombok.Data;
import lombok.ToString;

import javax.servlet.http.HttpServletRequest;
import java.security.Principal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 基于Jwt，用于前后端传递的令牌对象。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
@Data
@ToString
public class TokenData implements Principal {

    /**
     * 在HTTP Request对象中的属性键。
     */
    public static final String REQUEST_ATTRIBUTE_NAME = "tokenData";
    /**
     * 用户Id。
     */
    private Long userId;
    /**
     * 用户所属角色。多个角色之间逗号分隔。
     */
    private String roleIds;

    /**
     * 组织 id
     */
    private Long orgId;

    /**
     * 用户所在部门Id。
     * 仅当系统支持uaa时可用，否则可以直接忽略该字段。保留该字段是为了保持单体和微服务通用代码部分的兼容性。
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 用户部门
     */
    private List<Long> deptIds;
    private JSONArray departments;
    /**
     * 用户所属岗位Id。多个岗位之间逗号分隔。仅当系统支持岗位时有值。
     */
    private String postIds;
    /**
     * 岗位
     */
    private JSONArray posts;
    /**
     * 用户的部门岗位Id。多个岗位之间逗号分隔。仅当系统支持岗位时有值。
     */
    private String deptPostIds;
    /**
     * 租户Id。
     * 仅当系统支持uaa时可用，否则可以直接忽略该字段。保留该字段是为了保持单体和微服务通用代码部分的兼容性。
     */
    private Long tenantId;
    /**
     * 是否为超级管理员。
     */
    private Boolean isAdmin;
    /**
     * 用户登录名。
     */
    private String loginName;
    /**
     * 用户显示名称。
     */
    private String showName;
    /**
     * 手机号码
     */
    private String phone;
    /**
     * 身份证号
     */
    private String cardNo;
    /**
     * 性别
     */
    private Integer sex;
    /**
     * 设备类型。参考 AppDeviceType。
     */
    private Integer deviceType;
    /**
     * 标识不同登录的会话Id。
     */
    private String sessionId;
    /**
     * 访问uaa的授权token。
     * 仅当系统支持uaa时可用，否则可以直接忽略该字段。保留该字段是为了保持单体和微服务通用代码部分的兼容性。
     */
    private String uaaAccessToken;
    /**
     * 数据库路由键(仅当水平分库时使用)。
     */
    private Integer databaseRouteKey;
    /**
     * 登录IP。
     */
    private String loginIp;
    /**
     * 登录时间。
     */
    private Date loginTime;
    /**
     * 登录头像地址。
     */
    private String headImageUrl;
    /**
     * 原始的请求Token。
     */
    private String token;

    private String facePicture;

    // 是否忽略权限组拦截
    private Boolean ignorePowerGroup = false;

    /**
     * openID
     */
    private String openId;
    /**
     * 工号
     */
    private String jobNumber;
    /**
     * 手机短号
     */
    private String shortPhone;

    /**
     * 将令牌对象添加到Http请求对象。
     *
     * @param tokenData 令牌对象。
     */
    public static void addToRequest(TokenData tokenData) {
        HttpServletRequest request = ContextUtil.getHttpRequest();
        Objects.requireNonNull(request).setAttribute(TokenData.REQUEST_ATTRIBUTE_NAME, tokenData);
    }

    /**
     * 从Http Request对象中获取令牌对象。
     *
     * @return 令牌对象。
     */
    public static TokenData takeFromRequest() {
        HttpServletRequest request = ContextUtil.getHttpRequest();
        return (TokenData) Objects.requireNonNull(request).getAttribute(REQUEST_ATTRIBUTE_NAME);
    }

    @Override
    public String getName() {
        return this.userId.toString();
    }
}
