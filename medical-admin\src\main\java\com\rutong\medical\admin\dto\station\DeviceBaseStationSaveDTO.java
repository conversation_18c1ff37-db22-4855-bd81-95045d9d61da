package com.rutong.medical.admin.dto.station;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("DeviceBaseStationSaveDTO")
@Data
public class DeviceBaseStationSaveDTO {

    @ApiModelProperty(value = "位置")
    private Long id;


    @ApiModelProperty(value = "基站编号")
    private String deviceBaseStationCode;


    @ApiModelProperty(value = "基站分类表ID")
    private Long deviceBaseStationType;


    @ApiModelProperty(value = "基站名称")
    private String deviceBaseStationName;


    @ApiModelProperty(value = "支持协议")
    private String protocol;

    @ApiModelProperty(value = "IP地址")
    private String ip;

    /**
     * 所属楼层id
     */
    private Long spaceId;

    /**
     * 所属楼层路径
     */
    private String spacePath;

    /**
     * 所属楼层全名称
     */
    private String spaceFullName;

    /**
     * X
     */
    private Long x;

    /**
     * Y
     */
    private Long y;

    /**
     * Z
     */
    private Long z;

    /**
     * 经度
     */
    private Float longitude;

    /**
     * 纬度
     */
    private Long latitude;

    /**
     * 在线状态(1:在线,0:离线)
     */
    private Integer isOnline;

    /**
     * 是否删除
     */
    private Integer isDelete;


    /**
     * 视频监控表ID
     */
    private List<Long> deviceMonitorIdList;
}
