package com.soft.admin.upms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.soft.common.core.base.dao.BaseDaoMapper;
import com.soft.common.core.base.service.BaseService;
import com.soft.common.core.exception.ServiceException;
import com.soft.common.core.object.MyPageData;
import com.soft.admin.upms.dao.SystemMessageMapper;
import com.soft.admin.upms.dao.SystemMessageUserRelationMapper;
import com.soft.admin.upms.dto.SystemMessagePageDTO;
import com.soft.admin.upms.model.SystemMessage;
import com.soft.admin.upms.model.SystemMessageUserRelation;
import com.soft.admin.upms.vo.SystemMessageVO;
import com.soft.admin.upms.service.SystemMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 系统消息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-06-26
 */
@Service
public class SystemMessageServiceImpl extends BaseService<SystemMessage, Long> implements SystemMessageService {

    @Autowired
    private SystemMessageMapper systemMessageMapper;
    @Autowired
    private SystemMessageUserRelationMapper systemMessageUserRelationMapper;

    @Override
    protected BaseDaoMapper<SystemMessage> mapper() {
        return systemMessageMapper;
    }


    @Override
    public MyPageData<SystemMessageVO> page(SystemMessagePageDTO param) {
        // 查询用户系统消息关联表
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        Page<SystemMessageUserRelation> relationPage = (Page<SystemMessageUserRelation>) systemMessageUserRelationMapper
                .selectList(new LambdaQueryWrapper<SystemMessageUserRelation>()
                        .eq(SystemMessageUserRelation::getUserId, param.getUserId())
                        .eq(Objects.nonNull(param.getIsRead()), SystemMessageUserRelation::getIsRead, param.getIsRead())
                        .orderByDesc(SystemMessageUserRelation::getCreateTime));
        if(CollectionUtils.isEmpty(relationPage)) {
            return new MyPageData<>();
        }
        // 转换成系统消息id列表查询系统消息
        Map<Long, Boolean> messageRelationMap = relationPage.stream()
                .collect(Collectors.toMap(SystemMessageUserRelation::getMessageId, SystemMessageUserRelation::getIsRead));
        List<SystemMessage> messageList = systemMessageMapper.selectByIds(messageRelationMap.keySet());
        // 组装分页结果
        MyPageData<SystemMessageVO> messagePage = new MyPageData<>();
        messagePage.setDataList(messageList.stream().map(message ->
                this.parseSystemMessageToVO(message, messageRelationMap)).collect(Collectors.toList()));
        messagePage.setTotalCount(relationPage.getTotal());
        return messagePage;
    }

    @Override
    public void batchRead( Long userId, List<Long> idList) {
        if(CollectionUtils.isEmpty(idList)) {
            throw new ServiceException("系统消息id列表不能为空");
        }
        systemMessageUserRelationMapper.batchRead( userId, idList);
    }

    @Override
    public void removeMessageUserRelation( Long userId, Long messageId) {
        systemMessageUserRelationMapper.deleteByMessageId( userId, messageId);
    }

    /**
     * 系统消息转VO
     * @param systemMessage
     * @param messageRelationMap
     * @return
     */
    private SystemMessageVO parseSystemMessageToVO(SystemMessage systemMessage, Map<Long, Boolean> messageRelationMap) {
        SystemMessageVO systemMessageVO = SystemMessage.INSTANCE.fromModel(systemMessage);
        systemMessageVO.setIsRead(messageRelationMap.get(systemMessageVO.getId()));
        return systemMessageVO;
    }


}
