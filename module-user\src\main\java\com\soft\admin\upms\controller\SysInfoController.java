package com.soft.admin.upms.controller;

import com.soft.admin.upms.dto.SysInfoDTO;
import com.soft.admin.upms.service.SysInfoService;
import com.soft.admin.upms.vo.SysInfoVO;
import com.soft.common.core.annotation.NoAuthInterface;
import com.soft.common.core.object.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName SysInfoController
 * @description:
 * @date 2025年05月19日
 */
@Api(tags = "系统信息配置管理")
@RestController
@RequestMapping("/system/info")
public class SysInfoController {

    @Autowired
    private SysInfoService sysInfoService;

    @ApiOperation("获取系统配置信息")
    @GetMapping("/getList")
    @NoAuthInterface
    public ResponseResult<List<SysInfoVO>> getSysInfoList() {
        return ResponseResult.success(sysInfoService.getSysInfoList());
    }

    @ApiOperation("更新系统配置信息")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody @Valid SysInfoDTO updateDTO) {
        sysInfoService.update(updateDTO);
        return ResponseResult.success();
    }

}
