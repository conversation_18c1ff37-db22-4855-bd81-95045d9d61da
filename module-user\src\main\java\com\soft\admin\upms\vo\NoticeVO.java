package com.soft.admin.upms.vo;

import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * NoticeVO视图对象
 *
 * <AUTHOR>
 * @date 2023-07-03
 */
@ApiModel("NoticeVO视图对象")
@Data
public class NoticeVO {

    @ApiModelProperty(value = "${column.columnComment}")
    private Long id;


    @ApiModelProperty(value = "类型")
    private String type;


    @ApiModelProperty(value = "标题")
    private String title;


    @ApiModelProperty(value = "内容")
    private String content;


    @ApiModelProperty(value = "状态：发布1，未发布0")
    private Integer status;


    @ApiModelProperty(value = "有效期：开始时间")
    @JSONField(format = "yyyy-MM-dd")
    private Date beginTime;


    @ApiModelProperty(value = "有效期：结束时间")
    @JSONField(format = "yyyy-MM-dd")
    private Date endTime;


    @ApiModelProperty(value = "排序")
    private Integer showOrder;


    @ApiModelProperty(value = "创建者id")
    private Long createUserId;


    @ApiModelProperty(value = "创建时间")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private Date createTime;


    @ApiModelProperty(value = "更新者id")
    private Long updateUserId;


    @ApiModelProperty(value = "更新时间")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private Date updateTime;


    @ApiModelProperty(value = "用户id")
    private String userIds;

}
