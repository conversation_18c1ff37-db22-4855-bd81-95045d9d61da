package com.soft.admin.upms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.admin.upms.model.SysAnnexRelation;

import java.util.List;

/**
 * 业务附件关联关系Service接口
 *
 * <AUTHOR>
 * @date 2023-09-18
 */
public interface SysAnnexRelationService extends IService<SysAnnexRelation> {

    /**
     * 保存附件的关联关系
     * @param annexIdList
     * @param busiId
     * @param clazz
     */
    void saveOrUpdate(List<Long> annexIdList, Long busiId, Class clazz);

}
