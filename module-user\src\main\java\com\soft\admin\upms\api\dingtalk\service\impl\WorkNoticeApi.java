package com.soft.admin.upms.api.dingtalk.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.request.OapiMessageCorpconversationStatusBarUpdateRequest;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import com.dingtalk.api.response.OapiMessageCorpconversationStatusBarUpdateResponse;
import com.soft.admin.upms.api.dingtalk.DingTalkConfig;
import com.soft.admin.upms.api.dingtalk.enums.DingTalkMessageType;
import com.soft.admin.upms.api.dingtalk.service.IOaUserApi;
import com.soft.admin.upms.api.dingtalk.service.IWorkNoticeApi;
import com.soft.admin.upms.dao.DingTalkTodoMapper;
import com.soft.admin.upms.dto.dintalk.OaSendMessageDTO;
import com.soft.admin.upms.model.DingTalkTodo;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description 钉钉用户Api
 * @Date 0009, 2023年8月9日 14:20
 * <AUTHOR>
 **/
@Slf4j
@Service
public class WorkNoticeApi implements IWorkNoticeApi {
    @Resource
    private DingTalkOauth2Api dingTalkOauth2Api;
    @Resource
    private IOaUserApi oaUserApi;
    @Resource
    private DingTalkTodoMapper dingTalkTodoMapper;

    @Resource
    private DingTalkConfig dingTalkConfig;

    // 钉钉统一跳转协议
    public static final String path = "dingtalk://dingtalkclient/action/open_platform_link?mobileLink=";
    public static final String sendUrl = "/topapi/message/corpconversation/asyncsend_v2";
    public static final String updateUrl = "/topapi/message/corpconversation/status_bar/update";

    public void sendOAWorkNotice(OaSendMessageDTO dto) {
        try {
            DingTalkClient client = new DefaultDingTalkClient(dingTalkConfig.getServerUrl() + sendUrl);
            OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
            request.setAgentId(dingTalkConfig.getAgentId());
            String userIdList = StringUtils.join(dto.getReceiverSet(), ",");
            request.setUseridList(userIdList);
            request.setToAllUser(false);

            OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            msg.setMsgtype(DingTalkMessageType.OA.getInfo());
            msg.setOa(new OapiMessageCorpconversationAsyncsendV2Request.OA());
            //跳转链接
            msg.getOa().setMessageUrl(dingTalkConfig.getAppUrl() + dto.getAppUrl());
            msg.getOa().setPcMessageUrl(dto.getPcUrl());
            //设置head
            msg.getOa().setHead(new OapiMessageCorpconversationAsyncsendV2Request.Head());
            msg.getOa().getHead().setText(dto.getHead());
            msg.getOa().getHead().setBgcolor("FFBBBBBB");
            //设置body
            msg.getOa().setBody(new OapiMessageCorpconversationAsyncsendV2Request.Body());
            msg.getOa().getBody().setTitle(dto.getTitle());
//            msg.getOa().getBody().setContent(StringUtils.join(dto.getContentList(), "\n"));
            List<OapiMessageCorpconversationAsyncsendV2Request.Form> formList = Lists.newArrayList();

            //根据content组装form模板
            List<OaSendMessageDTO.Form> contentList = dto.getContentList();

            for (OaSendMessageDTO.Form form : contentList) {
                OapiMessageCorpconversationAsyncsendV2Request.Form from = new OapiMessageCorpconversationAsyncsendV2Request.Form();
                from.setKey(form.getKey() + "：");
                from.setValue(form.getValue());
                formList.add(from);
            }

            msg.getOa().getBody().setForm(formList);

            msg.getOa().setStatusBar(new OapiMessageCorpconversationAsyncsendV2Request.StatusBar());
            msg.getOa().getStatusBar().setStatusBg("FFF65E5E");
            msg.getOa().getStatusBar().setStatusValue("待审批");

            request.setMsg(msg);

            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(request, dingTalkOauth2Api.getAccessToken());
            log.info("sendOAWorkNotice:{}", rsp.getBody());
            dto.setTaskId(rsp.getTaskId());

            DingTalkTodo dingTalkTodo = new DingTalkTodo();
            dingTalkTodo.setBusiId(dto.getBusiId());
            dingTalkTodo.setBusiType(dto.getBusiType());
            dingTalkTodo.setMessageId(rsp.getTaskId());
            dingTalkTodo.setCreateTime(new Date());
            dingTalkTodoMapper.insert(dingTalkTodo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public void sendMarkdownWorkNotice(OaSendMessageDTO dto) {
        try {
            DingTalkClient client = new DefaultDingTalkClient(dingTalkConfig.getServerUrl() + sendUrl);
            OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
            request.setAgentId(dingTalkConfig.getAgentId());
            String userIdList = StringUtils.join(dto.getReceiverSet(), ",");
            request.setUseridList(userIdList);
            request.setToAllUser(false);
            OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            msg.setMsgtype(DingTalkMessageType.MARKDOWN.getInfo());
            msg.setMarkdown(new OapiMessageCorpconversationAsyncsendV2Request.Markdown());

            OapiMessageCorpconversationAsyncsendV2Request.Link link = new OapiMessageCorpconversationAsyncsendV2Request.Link();
            link.setMessageUrl(dingTalkConfig.getAppUrl() + dto.getAppUrl());
            link.setText("查看");
            msg.setLink(link);
            String content = "## " + dto.getTitle() + "   \n   ";
            if (CollectionUtil.isNotEmpty(dto.getContentList())) {
                content = content + StringUtils.join(dto.getContentList(), "   \n   ");
            } else {
                content = content + dto.getContent();
            }
            msg.getMarkdown().setText(content);
            msg.getMarkdown().setTitle(dto.getTitle());
            request.setMsg(msg);
            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(request, dingTalkOauth2Api.getAccessToken());
            log.info("sendMarkdownWorkNotice:{}", rsp.getBody());
            dto.setTaskId(rsp.getTaskId());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public void sendActionCardWorkNotice(OaSendMessageDTO dto) {
        try {
            DingTalkClient client = new DefaultDingTalkClient(dingTalkConfig.getServerUrl() + sendUrl);
            OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
            request.setAgentId(dingTalkConfig.getAgentId());
            String userIdList = StringUtils.join(dto.getReceiverSet(), ",");
            request.setUseridList(userIdList);
            request.setToAllUser(false);

            OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            msg.setMsgtype(DingTalkMessageType.ACTION_CARD.getInfo());

            msg.setActionCard(new OapiMessageCorpconversationAsyncsendV2Request.ActionCard());
            //跳转链接
            msg.getActionCard().setSingleUrl(dto.getAppUrl());

            msg.getActionCard().setTitle(dto.getTitle());
            String content = "## " + dto.getTitle() + "   \n   ";
            if (CollectionUtil.isNotEmpty(dto.getContentList())) {
                content = content + StringUtils.join(dto.getContentList(), "   \n   ");
            } else {
                content = content + dto.getContent();
            }
            msg.getActionCard().setMarkdown(content);
            msg.getActionCard().setSingleTitle(StringUtils.isBlank(dto.getSingleTitle()) ? "查看详情" : dto.getSingleTitle());
            msg.getActionCard().setSingleUrl(dingTalkConfig.getAppUrl() + dto.getAppUrl());

            request.setMsg(msg);

            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(request, dingTalkOauth2Api.getAccessToken());
            log.info("sendActionCardWorkNotice:{}", rsp.getBody());
            dto.setTaskId(rsp.getTaskId());


        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public void updateWorkNotice(Long taskId, boolean status) {
        DingTalkClient client = new DefaultDingTalkClient(dingTalkConfig.getServerUrl() + updateUrl);
        OapiMessageCorpconversationStatusBarUpdateRequest request = new OapiMessageCorpconversationStatusBarUpdateRequest();
        request.setAgentId(dingTalkConfig.getAgentId());

        if (status) {
            request.setStatusValue("已同意");
            request.setStatusBg("FF78C06E");
        } else {
            request.setStatusValue("已拒绝");
            request.setStatusBg("0XFF0000CC");
        }

        request.setTaskId(taskId);
        OapiMessageCorpconversationStatusBarUpdateResponse response = null;
        try {
            response = client.execute(request, dingTalkOauth2Api.getAccessToken());
        } catch (ApiException e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public Long getTaskId(Long busiId) {
        Long messageId = 0L;
        DingTalkTodo dingTalkTodo = dingTalkTodoMapper.selectOne(Wrappers.lambdaQuery(DingTalkTodo.class).eq(DingTalkTodo::getBusiId, busiId).orderByAsc(DingTalkTodo::getCreateTime).last("LIMIT 1"));
        if (dingTalkTodo != null) {
            messageId = dingTalkTodo.getMessageId();
        }
        return messageId;
    }
}
