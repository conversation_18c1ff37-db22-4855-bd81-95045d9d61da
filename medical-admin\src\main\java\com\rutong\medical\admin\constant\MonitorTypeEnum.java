package com.rutong.medical.admin.constant;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public enum MonitorTypeEnum {
    GUN_CAMERA(1, "枪机"), DOME_CAMERA(2, "球机");

    private static final Map<Integer, String> CODE_TO_NAME_MAP = new HashMap<>();
    private static final Map<String, Integer> NAME_TO_CODE_MAP = new HashMap<>();

    static {
        for (MonitorTypeEnum type : values()) {
            CODE_TO_NAME_MAP.put(type.code, type.name);
            NAME_TO_CODE_MAP.put(type.name, type.code);
        }
    }

    private final Integer code;
    private final String name;

    MonitorTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据 code 获取对应的 name
     *
     * @param code 枚举值的 code
     * @return 对应的 name，如果未找到则返回 null
     */
    public static String getNameByCode(Integer code) {
        return CODE_TO_NAME_MAP.get(code);
    }

    /**
     * 根据 name 获取对应的 code
     *
     * @param name 枚举值的 name
     * @return 对应的 code，如果未找到则返回 null
     */
    public static Integer getCodeByName(String name) {
        return NAME_TO_CODE_MAP.get(name);
    }

    /**
     * 获取所有 name 值的集合
     *
     * @return 所有 name 的集合
     */
    public static Map<String, Integer> getAllNames() {
        return NAME_TO_CODE_MAP;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static List<String> getAllNamesAsList() {
        return Arrays.stream(values()).map(MonitorTypeEnum::getName).collect(Collectors.toList());
    }
}
