package com.rutong.medical.admin.service.device.impl;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rutong.medical.admin.dto.device.DeviceTerminalTypeDTO;
import com.rutong.medical.admin.dto.device.DeviceTerminalTypeQueryDTO;
import com.rutong.medical.admin.entity.device.Device;
import com.rutong.medical.admin.entity.device.DeviceTerminalType;
import com.rutong.medical.admin.mapper.device.DeviceMapper;
import com.rutong.medical.admin.mapper.device.DeviceTerminalTypeMapper;
import com.rutong.medical.admin.service.device.DeviceTerminalTypeService;
import com.soft.common.core.constant.GlobalDeletedFlag;
import com.soft.common.core.exception.ServiceException;
import com.soft.common.core.object.TokenData;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;

/**
 * 设备分类Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-12
 */
@Service
public class DeviceTerminalTypeServiceImpl extends ServiceImpl<DeviceTerminalTypeMapper, DeviceTerminalType>
    implements DeviceTerminalTypeService {

    @Autowired
    private DeviceTerminalTypeMapper smDeviceTerminalTypeMapper;
    @Resource
    private DeviceMapper deviceMapper;

    @Autowired
    private RedissonClient redissonClient;

    public static final String smartMedicalTypeCode = "smart:medical:device:typeCode:";

    @Override
    public List<Tree<Long>> getTreeList(DeviceTerminalTypeQueryDTO deviceTerminalTypeQuery) {
        // 根据条件查询设备类型列表
        List<DeviceTerminalType> typeList = smDeviceTerminalTypeMapper.selectList(
            new LambdaQueryWrapper<DeviceTerminalType>().eq(DeviceTerminalType::getIsDelete, GlobalDeletedFlag.NORMAL)
                .eq(StringUtils.isNotEmpty(deviceTerminalTypeQuery.getTypeCode()), DeviceTerminalType::getTypeCode,
                    deviceTerminalTypeQuery.getTypeCode())
                .like(StringUtils.isNotBlank(deviceTerminalTypeQuery.getTypeName()), DeviceTerminalType::getTypeName,
                    deviceTerminalTypeQuery.getTypeName())
                .orderByDesc(DeviceTerminalType::getCreateTime));

        // 组装成树结构
        TreeNodeConfig config = new TreeNodeConfig();
        return TreeUtil.build(typeList, 0L, config, ((type, tree) -> {
            tree.setId(type.getId());
            tree.setName(type.getTypeName());
            tree.setParentId(type.getParentId());
            tree.setWeight(0);
            tree.putExtra("typeCode", type.getTypeCode());
        }));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(DeviceTerminalTypeDTO deviceTerminalTypeDTO) {
        String typeName = deviceTerminalTypeDTO.getTypeName();
        // 校验：type_name 是否已存在
        boolean exists = smDeviceTerminalTypeMapper
            .selectCount(new LambdaQueryWrapper<DeviceTerminalType>().eq(DeviceTerminalType::getTypeName, typeName)
                .eq(DeviceTerminalType::getIsDelete, GlobalDeletedFlag.NORMAL)) > 0;

        if (exists) {
            throw new ServiceException("分类名称 [" + typeName + "] 已存在，请勿重复添加");
        }

        DeviceTerminalType entity = new DeviceTerminalType();

        // 设置 type_code
        String nextCode = generateNextTypeCode();
        entity.setTypeCode(nextCode);
        entity.setTypeName(deviceTerminalTypeDTO.getTypeName());

        // 先保存 entity，获取生成的 id
        try {
            entity.setCreateUserId(TokenData.takeFromRequest().getUserId());
            entity.setCreateTime(new Date());
            entity.setIsDelete(GlobalDeletedFlag.NORMAL);
            smDeviceTerminalTypeMapper.insert(entity);
        } catch (Exception e) {
            throw new ServiceException("添加设备类型失败");
        }

        // 处理父级路径
        if (Objects.nonNull(deviceTerminalTypeDTO.getParentId()) && deviceTerminalTypeDTO.getParentId() != 0L) {
            DeviceTerminalType parentType = smDeviceTerminalTypeMapper.selectById(deviceTerminalTypeDTO.getParentId());
            Assert.notNull(parentType, "上级设备类型不存在");

            // 拼接 path_id
            String parentPathId = parentType.getPathId();
            if (parentPathId == null) {
                parentPathId = "";
            }
            entity.setPathId(parentPathId + "/" + entity.getId());
            entity.setUpdateTime(new Date());
            entity.setParentId(parentType.getId());
            entity.setUpdateUserId(TokenData.takeFromRequest().getUserId());
            // 更新 path_id 字段
            smDeviceTerminalTypeMapper.updateById(entity);
        } else {
            // 如果是根节点，path_id 就是自己的 id
            entity.setPathId(entity.getId().toString());
            // 更新 path_id 字段
            entity.setUpdateTime(new Date());
            entity.setUpdateUserId(TokenData.takeFromRequest().getUserId());
            smDeviceTerminalTypeMapper.updateById(entity);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(DeviceTerminalTypeDTO deviceTerminalType) {
        // 检查目标分类是否存在
        DeviceTerminalType stationType = smDeviceTerminalTypeMapper.selectById(deviceTerminalType.getId());
        Assert.notNull(stationType, "分类不存在");

        // 检查上级分类是否存在（如果提供了上级分类ID）
        if (deviceTerminalType.getParentId() != null) {
            DeviceTerminalType parentType = smDeviceTerminalTypeMapper.selectById(deviceTerminalType.getParentId());
            Assert.notNull(parentType, "上级分类不存在");

            // 防止循环引用（分类不能成为自己的上级）
            if (deviceTerminalType.getId().equals(deviceTerminalType.getParentId())) {
                throw new ServiceException("分类不能设置自己为上级");
            }
        }

        // 更新分类信息
        stationType.setTypeName(deviceTerminalType.getTypeName());
        stationType.setParentId(deviceTerminalType.getParentId());

        try {
            smDeviceTerminalTypeMapper.updateById(stationType);
        } catch (Exception e) {
            throw new ServiceException("修改设备类型失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        DeviceTerminalType deviceTerminalType = smDeviceTerminalTypeMapper.selectById(id);
        if (deviceTerminalType == null) {
            throw new ServiceException("该分类不存在");
        }

        Long countDevice = deviceMapper.selectCount(new LambdaQueryWrapper<Device>()
            .eq(Device::getDeviceTerminalTypeId, id).eq(Device::getIsDelete, GlobalDeletedFlag.NORMAL));
        if (countDevice > 0) {
            throw new ServiceException("该分类有设备,无法删除");
        }

        DeviceTerminalType parentType = smDeviceTerminalTypeMapper.selectById(deviceTerminalType.getParentId());
        if (parentType == null) {
            boolean exists = new LambdaQueryChainWrapper<>(smDeviceTerminalTypeMapper)
                .eq(DeviceTerminalType::getIsDelete, GlobalDeletedFlag.NORMAL).eq(DeviceTerminalType::getParentId, id)
                .last("limit 1").exists();
            if (exists)
                throw new ServiceException("存在下级分类无法删除");
        }
        try {
            deviceTerminalType.setIsDelete(GlobalDeletedFlag.DELETED);
            smDeviceTerminalTypeMapper.updateById(deviceTerminalType);
        } catch (Exception e) {
            throw new ServiceException("删除设备类型失败");
        }
    }

    /**
     * 获取基站分类编号
     *
     * @return
     */
    private String generateNextTypeCode() {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(smartMedicalTypeCode);
        long nextValue = atomicLong.incrementAndGet();
        return String.format("%06d", nextValue);
    }
}
