<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rutong.medical.admin.mapper.system.SpaceMapper">
    <resultMap type="com.rutong.medical.admin.entity.system.Space" id="SpaceResult">
        <result property="id" column="id" />
        <result property="parentId" column="parent_id" />
        <result property="path" column="path" />
        <result property="code" column="code" />
        <result property="type" column="type" />
        <result property="name" column="name" />
        <result property="fullName" column="full_name" />
        <result property="deleted" column="deleted" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
        <result property="sort" column="sort" />
        <result property="commonArea" column="common_area" />
        <result property="modelTwoDimensional" column="model_two_dimensional" />
        <result property="modelThreeDimensional" column="model_three_dimensional" />
    </resultMap>

    <sql id="selectSpaceVo">
        select id, parent_id, path, code, type, name, full_name, deleted, create_user_id, create_time, update_user_id, update_time,sort,common_area,model_two_dimensional,model_three_dimensional from sp_space
    </sql>

    <select id="queryAll" resultType="com.rutong.medical.admin.entity.system.Space">
        <include refid="selectSpaceVo" /> where deleted = 1
    </select>


    <insert id="insertBatch">
        insert into sp_space(id, parent_id, path, code, type, name, full_name, coordinate, deleted, create_user_id, create_time, update_user_id, update_time,sort,common_area,model_two_dimensional,model_three_dimensional)
        values 
        <foreach collection="spaces" item="space" separator=",">
            (#{space.id}, #{space.parentId}, #{space.path}, #{space.code}, #{space.type},
             #{space.name}, #{space.fullName}, #{space.coordinate}, #{space.deleted}, #{space.createUserId},
             #{space.createTime}, #{space.updateUserId}, #{space.updateTime},#{space.sort},#{space.commonArea},#{space.modelTwoDimensional},#{space.modelThreeDimensional})
        </foreach>
    </insert>
    <select id="queryAllOnlyType" resultType="com.rutong.medical.admin.entity.system.Space">
        <include refid="selectSpaceVo" />
        <where>
            type = #{spaceType}
            <if test="spaceIds != null and spaceIds.size > 0">
                and
                <foreach collection="spaceIds" item="spaceId" separator="OR" open="(" close=")">
                    `path` like concat('%', #{spaceId},'%')
                </foreach>
            </if>
        </where>
    </select>
</mapper>