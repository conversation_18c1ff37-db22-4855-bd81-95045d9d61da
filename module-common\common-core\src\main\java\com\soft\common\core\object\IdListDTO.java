package com.soft.common.core.object;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description id参数
 * @Date 0005, 2023年5月5日 14:30
 * <AUTHOR>
 **/
@Data
public class IdListDTO {

    @NotNull(message = "id列表不能为空")
    private List<Long> idList;

    public IdListDTO() {
    }

    public IdListDTO(List<Long> idList) {
        this.idList = idList;
    }
    public static IdListDTO buildDTO(List<Long> idList){
        return new IdListDTO(idList);
    }
}
