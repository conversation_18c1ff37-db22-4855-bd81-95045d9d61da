package com.rutong.medical.admin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.rutong.medical.admin.entity.AlarmDetailTD;
import com.rutong.medical.admin.entity.Test;
import com.rutong.medical.admin.mapper.TDMapper;
import com.rutong.medical.admin.mapper.TestMapper;
import com.rutong.medical.admin.service.TestService;
import com.rutong.medical.common.mybatis.base.BaseServiceImpl;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.TDenginePageResult;
import com.soft.common.core.util.MyPageUtil;
import lombok.AllArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName UserServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/7/9 14:43
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Service
@AllArgsConstructor
public class TestServiceImpl extends BaseServiceImpl<TestMapper, Test> implements TestService {

   private TDMapper tdMapper;

    @Override
    public IPage<Test> selectTestPage(IPage<Test> page, Test test) {
        return page.setRecords(baseMapper.selectTestPage(page, test));
    }

    @Override
    public List<AlarmDetailTD> selectByTimeRange() {
        Timestamp startTime = Timestamp.valueOf("2025-07-08 00:00:00");
        Timestamp endTime = Timestamp.valueOf("2025-07-12 23:59:59");

        return tdMapper.selectByTimeRange(startTime,endTime);
    }

    @Override
    public TDenginePageResult<AlarmDetailTD> selectByTimeRangePage() {

        Timestamp startTime = Timestamp.valueOf("2025-07-14 00:00:00");
        Timestamp endTime = Timestamp.valueOf("2025-07-17 23:59:59");
        return  new TDenginePageResult<AlarmDetailTD>(1000L,tdMapper.selectByTimeRange(startTime,endTime));
    }

    @Override
    public void addTD(){
        AlarmDetailTD alarmDetailTD = new AlarmDetailTD();
        alarmDetailTD.setAlarmDetailId("1");
        alarmDetailTD.setAlarmDate(Timestamp.valueOf("2025-07-08 00:00:00"));
        alarmDetailTD.setIsAlarm((byte) 1);
        alarmDetailTD.setIsKey((byte) 1);
        tdMapper.insert(alarmDetailTD);
    }


    @Cacheable(value = "user1:user2", key = "#id")
    public Test selectTest(Integer id){
        return baseMapper.selectById(id);
    }

}
