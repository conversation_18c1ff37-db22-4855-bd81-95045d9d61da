package com.rutong.medical.admin.entity.station;

import com.rutong.medical.admin.vo.station.DeviceBaseStationVO;
import lombok.Data;
import com.soft.common.core.base.model.BaseModel;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.EqualsAndHashCode;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.io.Serializable;

/**
 * 基站对象 device_base_station
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sm_device_base_station")
public class DeviceBaseStation extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 基站表ID */
    @TableId(value = "id")
    private Long id;

    /** 基站编号 */
    private String deviceBaseStationCode;

    /** 基站分类表ID */
    private Long deviceBaseStationType;

    /** 基站名称 */
    private String deviceBaseStationName;

    /** 支持协议 */
    private String protocol;

    /** IP地址 */
    private String ip;

    /**
     * 所属楼层id
     */
    private Long spaceId;

    /**
     * 所属楼层路径
     */
    private String spacePath;

    /**
     * 所属楼层全名称
     */
    private String spaceFullName;

    /** X */
    private Long x;

    /** Y */
    private Long y;

    /** Z */
    private Long z;

    /** 经度 */
    private Long longitude;

    /** 纬度 */
    private Long latitude;

    /** 在线状态(1:在线,0:离线) */
    private Integer isOnline;

    /** 是否删除 */
    private Integer isDelete;


    @Mapper
    public interface DeviceBaseStationModelMapper extends BaseModelMapper<DeviceBaseStationVO, DeviceBaseStation> {
        /**
        * 转换Vo对象到实体对象。
        *
        * @param entityVo 域对象。
        * @return 实体对象。
        */
        @Override
        DeviceBaseStation toModel(DeviceBaseStationVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        DeviceBaseStationVO fromModel(DeviceBaseStation entity);
    }

    public static final DeviceBaseStationModelMapper INSTANCE = Mappers.getMapper(DeviceBaseStationModelMapper.class);
}
