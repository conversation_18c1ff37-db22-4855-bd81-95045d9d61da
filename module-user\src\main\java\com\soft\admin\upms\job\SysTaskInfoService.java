package com.soft.admin.upms.job;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.soft.admin.upms.dao.SysTaskInfoMapper;
import com.soft.admin.upms.model.SysTaskInfo;
import com.xxl.job.core.model.XxlJobInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: gaoyh
 * @Date: 2023/6/5 10:34
 */
@Slf4j
@Service
public class SysTaskInfoService {

    @Resource
    private SysTaskInfoMapper taskInfoMapper;

    @Resource
    private XxlJobInfoService jobInfoService;

    public boolean addBatchTask(SysTaskInfo taskInfo, List<XxlJobInfo> jobInfos) {
        List<Integer> jobIds = jobInfoService.batchInsertJob(jobInfos);
        taskInfo.setJobIds(jobIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        taskInfoMapper.insert(taskInfo);
        return true;
    }

    /**
     * 创建定时任务
     * @param executorHandler 执行器处理方法
     * @return
     */
    public void stopTask(String executorHandler) {
        //查询是否已经存在相同任务
        SysTaskInfo taskInfo = taskInfoMapper.getModelByParams(executorHandler, executorHandler);
        if(taskInfo != null){
            boolean flag = jobInfoService.stop(Integer.valueOf(taskInfo.getJobIds()));
            if(flag){
                taskInfo.setTriggerStatus(0);
                taskInfoMapper.updateById(taskInfo);
            }
        }
    }

    /**
     * 定时任务
     * @param jobDesc 任务描述
     * @param syncTaskCron cron表达式
     * @param executorHandler 执行器处理方法
     * @param params 任务参数
     * @return
     */
    public void addTask(String jobDesc, String syncTaskCron, String executorHandler, JSONObject params) {
        //查询是否已经存在相同任务
        SysTaskInfo taskInfo = taskInfoMapper.getModelByParams(executorHandler, executorHandler);
        if(taskInfo == null){
            String jobId = jobInfoService.createJob(jobDesc, syncTaskCron, executorHandler, params);
            taskInfo = new SysTaskInfo();
            taskInfo.setJobDesc(jobDesc);
            taskInfo.setJobIds(jobId);
            taskInfo.setCreateTime(new Date());
            taskInfo.setJobType(2);
            taskInfo.setTriggerStatus(1);
            taskInfo.setRefId(executorHandler);
            taskInfo.setRefTable(executorHandler);
            taskInfoMapper.insert(taskInfo);
        }else{
            XxlJobInfo xxlJobInfo = jobInfoService.getXxlJobInfo(taskInfo.getJobIds());

            if(xxlJobInfo == null){
               String jobId = jobInfoService.createJob(jobDesc, syncTaskCron, executorHandler, params);
               taskInfo.setJobIds(jobId);
            }else{
                xxlJobInfo.setJobDesc(jobDesc);
                xxlJobInfo.setScheduleConf(syncTaskCron);
                xxlJobInfo.setTriggerStatus(0);
                // 定时任务参数
                if(params != null) {
                    xxlJobInfo.setExecutorParam(params.toJSONString());
                }
                jobInfoService.updateJobInfo(xxlJobInfo);
                jobInfoService.start(xxlJobInfo.getId());
            }
            taskInfo.setJobDesc(jobDesc);
            taskInfoMapper.updateById(taskInfo);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public boolean triggerStatus(Integer[] taskInfoIds, Integer triggerStatus) {
        List<SysTaskInfo> taskInfos = taskInfoMapper.getListByIds(taskInfoIds);
        if (taskInfos.size() > 0) {
            StringBuffer jobIdStrs = new StringBuffer();
            for (int i = 0; i < taskInfos.size(); i++) {
                if (i == 0) {
                    jobIdStrs.append(taskInfos.get(i).getJobIds());
                } else {
                    jobIdStrs.append("," + taskInfos.get(i).getJobIds());
                }
            }
            int[] jobIds =
                    Arrays.asList(jobIdStrs.toString().split(",")).stream().mapToInt(Integer::parseInt).toArray();
            if (triggerStatus == 0) {
                jobInfoService.batchStart(jobIds);
            } else {
                jobInfoService.batchStop(jobIds);
            }

            taskInfoMapper.updateTriggerStatus(taskInfoIds, triggerStatus);
        } else {
            log.error("未找到启动任务记录");
        }
        return true;
    }

    public boolean delTask(String refTable, String refId) {
        SysTaskInfo taskInfo = taskInfoMapper.getModelByParams(refTable, refId);
        if (taskInfo != null) {
            int[] jobIds =
                    Arrays.asList(taskInfo.getJobIds().split(",")).stream().mapToInt(Integer::parseInt).toArray();
            jobInfoService.batchDel(jobIds);
            Long[] ids = new Long[1];
            ids[0] = taskInfo.getId();
            taskInfoMapper.delByIds(ids);
        }
        return true;
    }

}
