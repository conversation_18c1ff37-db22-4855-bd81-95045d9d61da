package com.soft.common.core.util;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.time.DateFormatUtils;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {
    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";
    public static String YYYY年MM月 = "yyyy年MM月";

    public static String YYYY_MM_DD = "yyyy-MM-dd";
    public static String YYYYMMDD = "yyyyMMdd";

    public static String HH_MM_SS = "HH:mm:ss";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";

    private static String[] parsePatterns =
        {"yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM", "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss",
            "yyyy/MM/dd HH:mm", "yyyy/MM", "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String yearMonthCn(final Date date) {
        return parseDateToStr(YYYY年MM月, date);
    }

    public static final Date mergeDateTime(Date date, Date time) {
        return parseDate(dateTime(date) + " " + parseDateToStr(HH_MM_SS, time));
    }

    public static final String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算相差天数
     */
    public static int differentDaysByMillisecond(Date date1, Date date2) {
        return Math.abs((int)((date2.getTime() - date1.getTime()) / (1000 * 3600 * 24)));
    }

    /**
     * 计算相差秒数
     */
    public static int differentSecond(Date date1, Date date2) {
        return Math.abs((int)((date2.getTime() - date1.getTime()) / 1000));
    }

    /**
     * 计算时间差
     *
     * @param endTime 最后时间
     * @param startTime 开始时间
     * @return 时间差（天/小时/分钟）
     */
    public static String timeDistance(Date endDate, Date startTime) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - startTime.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 增加 LocalDateTime ==> Date
     */
    public static Date toDate(LocalDateTime temporalAccessor) {
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 增加 LocalDate ==> Date
     */
    public static Date toDate(LocalDate temporalAccessor) {
        LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    public static Map<String, Long> subtractionDate(Date date1, Date date2) {
        HashMap<String, Long> map = new HashMap();
        try {
            // 毫秒ms
            long diff = date1.getTime() - date2.getTime();

            // 秒
            long diffSeconds = diff / 1000 % 60;
            // 分钟
            long diffMinutes = diff / (60 * 1000) % 60;
            // 小时
            long diffHours = diff / (60 * 60 * 1000) % 24;
            // 天
            long diffDays = diff / (24 * 60 * 60 * 1000);
            map.put("diffDays", diffDays);
            map.put("diffHours", diffHours);
            map.put("diffMinutes", diffMinutes);
            map.put("diffSeconds", diffSeconds);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return map;
    }

    public static Integer getYear(Object o) {
        Calendar c = Calendar.getInstance();
        if (o instanceof Date) {
            c.setTime((Date)o);
            return c.get(Calendar.YEAR);
        }
        c.setTime(parseDate(o));
        return c.get(Calendar.YEAR);
    }

    /**
     * 获取月中的天
     * 
     * @param d
     * @return
     */
    public static Integer getDayOfMonth(Date d) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        return c.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取周中的天
     * 
     * @param d
     * @return
     */
    public static Integer getDayOfWeek(Date d) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.add(Calendar.DATE, -1);
        return c.get(Calendar.DAY_OF_WEEK);
    }

    /**
     * 获取周中的天
     * 
     * @param d
     * @return
     */
    public static String getDayOfWeekCn(Date d) {
        int dayOfWeek = getDayOfWeek(d);
        switch (dayOfWeek) {
            case 7:
                return "日";
            case 1:
                return "一";
            case 2:
                return "二";
            case 3:
                return "三";
            case 4:
                return "四";
            case 5:
                return "五";
            case 6:
                return "六";
            default:
                return "无效的星期";
        }
    }

    /**
     * 获取当月的最后一天
     * 
     * @param time
     * @return
     */
    public static Date lastMonthDay(Date time) {
        // 通过Calendar.getInstance()方法创建一个Calendar实例对象。
        Calendar calendar = Calendar.getInstance();
        // 使用calendar.setTime(time)方法将指定的时间设置到该Calendar对象中。
        calendar.setTime(time);
        // 使用calendar.set(Calendar.DAY_OF_MONTH, 1)方法将日期设置为当月的第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        // 使用calendar.add(Calendar.MONTH, 1)方法将月份加1，即切换到下一个月
        calendar.add(Calendar.MONTH, 1);
        // 再使用calendar.add(Calendar.DATE, -1)方法将日期减去1天，即得到当前月份的最后一天
        calendar.add(Calendar.DATE, -1);
        // 返回calendar.getTime()获取最后一天的时间并返回
        return calendar.getTime();
    }

    /**
     * <AUTHOR>
     * @Description 计算两个日期之间的时间差
     * @Date 上午11:26 2024/9/27
     * @Param [date1, date2]
     * @return void
     **/
    public static String getTimeInterval(Date date1, Date date2) {
        long lDate1 = date1.getTime();
        long lDate2 = date2.getTime();
        long diff = (lDate1 < lDate2) ? (lDate2 - lDate1) : (lDate1 - lDate2);
        long day = diff / (24 * 60 * 60 * 1000);
        long hour = diff / (60 * 60 * 1000) - day * 24;
        long min = diff / (60 * 1000) - day * 24 * 60 - hour * 60;
        /*long sec = diff / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60;*/
        StringBuilder sb = new StringBuilder();
        sb.append(day).append("天 ").append(hour).append("小时 ").append(min).append("分");
        return sb.toString();
    }

    /**
     * LocalDate转date
     * 
     * @param localDate
     * @return
     */
    public static Date localDateToDate(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        // 将 LocalDate 转换为 Instant
        Instant instant = localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant();
        // 将 Instant 转换为 java.util.Date
        return Date.from(instant);
    }

    /**
     * date 转 LocalDate
     * 
     * @param date
     * @return
     */
    public static LocalDate convertDateToLocalDate(Date date) {
        // 将Date对象转换为Instant对象
        return date.toInstant().atZone(ZoneId.systemDefault()) // 使用系统默认时区
            .toLocalDate(); // 转换为LocalDate对象
    }

    public static void main(String[] args) {
        System.out.println(lastMonthDay(new Date()));
    }
}
