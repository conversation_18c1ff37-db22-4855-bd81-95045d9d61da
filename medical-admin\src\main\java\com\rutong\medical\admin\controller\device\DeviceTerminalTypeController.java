package com.rutong.medical.admin.controller.device;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.rutong.medical.admin.dto.device.DeviceTerminalTypeDTO;
import com.rutong.medical.admin.dto.device.DeviceTerminalTypeQueryDTO;
import com.rutong.medical.admin.service.device.DeviceTerminalTypeService;
import com.soft.common.core.object.ResponseResult;

import cn.hutool.core.lang.tree.Tree;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 设备分类控制器类
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Api(tags = "设备分类")
@RestController
@RequestMapping("/device/terminal/type")
public class DeviceTerminalTypeController {

    @Autowired
    private DeviceTerminalTypeService smDeviceTerminalTypeService;

    /**
     * 获取设备类型树结构列表
     *
     * @param deviceTerminalTypeQuery
     * @return
     */
    @ApiOperation(value = "获取设备类型树结构列表")
    @GetMapping("/list/tree")
    public ResponseResult<List<Tree<Long>>> getTreeList(DeviceTerminalTypeQueryDTO deviceTerminalTypeQuery) {
        return ResponseResult.success(smDeviceTerminalTypeService.getTreeList(deviceTerminalTypeQuery));
    }

    /**
     * 新增设备类型
     *
     * @param deviceTerminalTypeDTO
     */
    @ApiOperation(value = "新增设备类型")
    @PostMapping("/save")
    public ResponseResult save(@RequestBody @Validated DeviceTerminalTypeDTO deviceTerminalTypeDTO) {
        smDeviceTerminalTypeService.save(deviceTerminalTypeDTO);
        return ResponseResult.success();
    }

    /**
     * 更新设备类型
     *
     * @param deviceTerminalType
     */
    @ApiOperation(value = "更新设备类型")
    @PostMapping("/update")
    public ResponseResult update(@RequestBody @Validated DeviceTerminalTypeDTO deviceTerminalType) {
        smDeviceTerminalTypeService.update(deviceTerminalType);
        return ResponseResult.success();
    }

    /**
     * 删除设备类型
     *
     * @param id
     */
    @ApiOperation(value = "删除设备类型")
    @PostMapping("/delete")
    public ResponseResult delete(@RequestParam Long id) {
        smDeviceTerminalTypeService.delete(id);
        return ResponseResult.success();
    }
}
