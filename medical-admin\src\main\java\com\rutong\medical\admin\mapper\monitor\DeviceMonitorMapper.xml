<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rutong.medical.admin.mapper.monitor.DeviceMonitorMapper">
    <resultMap type="com.rutong.medical.admin.entity.monitor.DeviceMonitor" id="SmDeviceMonitorResult">
        <result property="id" column="id" />
        <result property="monitorCode" column="monitor_code" />
        <result property="monitorName" column="monitor_name" />
        <result property="monitorType" column="monitor_type" />
        <result property="spaceId" column="space_id" />
        <result property="ip" column="ip" />
        <result property="port" column="port" />
        <result property="channelNum" column="channel_num" />
        <result property="userCode" column="user_code" />
        <result property="password" column="password" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
        <result property="isDelete" column="is_delete" />
    </resultMap>

    <sql id="selectSmDeviceMonitorVo">
        select id, monitor_code, monitor_name, monitor_type, space_id, ip, port, channel_num, user_code, password, create_user_id, create_time, update_user_id, update_time, is_delete from sm_device_monitor
    </sql>



    <select id="list" resultType="com.rutong.medical.admin.entity.monitor.DeviceMonitor" parameterType="com.rutong.medical.admin.dto.monitor.DeviceMonitorQueryDTO">
        select * from sm_device_monitor
        <where>
          and is_delete = 1
            <if test="keyWord != null and keyWord != ''">
                and (monitor_name like concat('%', #{keyWord}, '%')
                or ip like concat('%', #{keyWord}, '%')
                or monitor_code like concat('%', #{keyWord}, '%')
                )
            </if>

            <if test="spacePath != null and spacePath != ''">
                and space_path like concat('%', #{spacePath}, '%')
            </if>
            <if test="ip != null and ip != ''">
                and ip like concat('%', #{ip}, '%')
            </if>
            <if test="monitorType != null">
                and monitor_type = #{monitorType}
            </if>
        </where>
        order by create_time desc
    </select>



    <select id="existsByDeviceNumberOrName" resultType="int">
        SELECT COUNT(*) FROM sm_device_monitor
        WHERE (monitor_code = #{param1}  OR monitor_name = #{param2}) and is_delete = 1
    </select>


    <select id="existsByDeviceNumberOrNameExcludingId" resultType="int">
        SELECT COUNT(*) FROM sm_device_monitor
        WHERE (monitor_code = #{param1}  OR monitor_name = #{param2} )
          AND id != #{param3}   and is_delete = 1
    </select>



    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            update sm_device_monitor
            <set>
                <if test="item.monitorName != null and item.monitorName != ''">
                    monitor_name = #{item.monitorName},
                </if>
                <if test="item.monitorType != null and item.monitorType != ''">
                    monitor_type = #{item.monitorType},
                </if>
                <if test="item.monitorCode != null and item.monitorCode != ''">
                    monitor_code = #{item.monitorCode},
                </if>
                <if test="item.spaceId != null">
                    space_id = #{item.spaceId},
                </if>
                <if test="item.spacePath != null and item.spacePath != ''">
                    space_path = #{item.spacePath},
                </if>
                <if test="item.spaceFullName != null and item.spaceFullName != ''">
                    space_full_name = #{item.spaceFullName},
                </if>
                <if test="item.ip != null and item.ip != ''">
                    ip = #{item.ip},
                </if>
                <if test="item.port != null">
                    port = #{item.port},
                </if>
                <if test="item.channelNum != null and item.channelNum != ''">
                    channel_num = #{item.channelNum},
                </if>
                <if test="item.userCode != null and item.userCode != ''">
                    user_code = #{item.userCode},
                </if>
                <if test="item.password != null and item.password != ''">
                    password = #{item.password},
                </if>
                <if test="item.factory != null and item.factory != ''">
                    factory = #{item.factory},
                </if>
                <if test="item.isDelete != null">
                    is_delete = #{item.isDelete},
                </if>
                update_user_id = #{item.updateUserId},
                update_time = #{item.updateTime}
            </set>
            where id = #{item.id}
        </foreach>
    </update>

    <update id="updateById">
        UPDATE sm_device_monitor
        <set>
            <!-- 监控名称 -->
            <if test="monitorName != null and monitorName != ''">
                monitor_name = #{monitorName},
            </if>

            <!-- 监控类型 -->
            <if test="monitorType != null and monitorType != ''">
                monitor_type = #{monitorType},
            </if>

            <!-- 监控编号 -->
            <if test="monitorCode != null and monitorCode != ''">
                monitor_code = #{monitorCode},
            </if>

            <!-- 所属空间ID -->
            <if test="spaceId != ''">
                space_id = #{spaceId},
            </if>

            <!-- 所属空间路径 -->
            <if test="spacePath != '' ">
                space_path = #{spacePath},
            </if>

            <!-- 所属空间全名称 -->
            <if test="spaceFullName != '' ">
                space_full_name = #{spaceFullName},
            </if>

            <!-- IP地址 -->
            <if test="ip != null and ip != ''">
                ip = #{ip},
            </if>

            <!-- 端口 -->
            <if test="port != null">
                port = #{port},
            </if>

            <!-- 通道号 -->
            <if test="channelNum != null and channelNum != ''">
                channel_num = #{channelNum},
            </if>

            <!-- 用户名 -->
            <if test="userCode != null and userCode != ''">
                user_code = #{userCode},
            </if>

            <!-- 密码 -->
            <if test="password != null and password != ''">
                password = #{password},
            </if>

            <!-- 厂家 -->
            <if test="factory != null and factory != ''">
                factory = #{factory},
            </if>

            <!-- 是否删除 -->
            <if test="isDelete != null">
                is_delete = #{isDelete},
            </if>

            <!-- 更新人ID -->
            <if test="updateUserId != null">
                update_user_id = #{updateUserId},
            </if>

            <!-- 更新时间 -->
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        WHERE id = #{id}
    </update>

</mapper>