package com.rutong.medical.admin.mapper.defence;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rutong.medical.admin.dto.defence.DefenceManageDTO;
import com.rutong.medical.admin.entity.defence.SmInvadeDefence;
import com.rutong.medical.admin.vo.defence.DetailVO;
import com.rutong.medical.admin.vo.defence.SmInvadeDefenceVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-07-21
 */
public interface SmInvadeDefenceMapper extends BaseMapper<SmInvadeDefence> {

    /**
     * 分页查询防区列表
     * @param defenceManageDTO 查询条件
     * @return 防区列表
     */
    List<SmInvadeDefenceVO> selectDefencePage(DefenceManageDTO defenceManageDTO);

//    /**
//     * 根据防区编号查询防区详情
//     * @param defenceCode 防区编号
//     * @return 防区详情
//     */
//    DefenceDetailVO selectDefenceDetailByCode(String defenceCode);
//
//    /**
//     * 根据防区ID查询关联的设备列表
//     * @param defenceId 防区ID
//     * @return 设备列表
//     */
//    List<DefenceDetailVO.DefenceDeviceVO> selectDefenceDevices(Long defenceId);

    List<DetailVO>selectDevicePage(DefenceManageDTO defenceManageDTO);

    /**
     * 解除关联
     * @param id
     * @return
     */
    Boolean deleteConnect(Long id);


}
