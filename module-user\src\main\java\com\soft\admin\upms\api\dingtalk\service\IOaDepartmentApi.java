package com.soft.admin.upms.api.dingtalk.service;

import com.soft.admin.upms.dto.dintalk.OaDepartmentDTO;

import java.util.List;

/**
 * @Description OA部门接口
 * @Date 0009, 2023年8月9日 9:02
 * <AUTHOR>
 **/
public interface IOaDepartmentApi {

    /**
     * 获取下级部门列表
     *
     * @param oaDeptId
     * @return
     */
    List<OaDepartmentDTO> getDeptSubList(String oaDeptId);

    /**
     * 获取部门详情
     *
     * @param oaDeptId
     * @return
     */
    OaDepartmentDTO getDeptById(String oaDeptId);

    /**
     * 根据id列表获取部门列表
     *
     * @param oaDeptIds
     * @return
     */
    List<OaDepartmentDTO> getDeptListByIds(List<String> oaDeptIds);

}
