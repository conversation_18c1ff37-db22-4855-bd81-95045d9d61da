package com.soft.admin.upms.api.dingtalk.utils;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.soft.admin.upms.api.dingtalk.DingTalkConfig;
import com.soft.admin.upms.api.dingtalk.utils.request.DingTodoRequest;
import com.soft.common.core.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;


@Slf4j
public class DingUtil {
    private static String APP_ID = "";
    private static String APP_SECRET = "";
    private static String accessToken = null;
    private static Date tokenExpireTime = null;
    private static String BASE_URL = "";
    private static final Lock lock = new ReentrantLock();

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // 令牌
    private static final String DING_TALK_ACCESS_TOKEN_URL = "%s/v1.0/oauth2/accessToken";
    // 待办创建
    private static final String DING_TALK_TODO_URL = "%s/v1.0/todo/users/%s/tasks";
    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


    /////////////////////////////////////////////////发送待办/////////////////////////////////////////////////////////////
    public static String sendTodo(String sendId, String acceptId, String title, String content, String pcUrl, String appUrl) {
        log.info("新钉钉待办接口准备发送: {} -- {}", sendId, acceptId);
        DingTodoRequest dingTodoRequest = DingTodoRequest.simpleTodo(sendId, acceptId, title, content, pcUrl, appUrl);
        String requestUrl = String.format(DING_TALK_TODO_URL, BASE_URL, sendId);
        return sendPostDing(requestUrl, JSONUtil.toJsonStr(dingTodoRequest));
    }
    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


    /////////////////////////////////////////////////封装的post请求///////////////////////////////////////////////////////
    private static String sendPostDing(String url, String body) {
        int maxAttempts = 2; // 最大尝试次数，包括初次尝试和一次重试
        int attemptCount = 0;

        while (attemptCount < maxAttempts) {
            attemptCount++;

            HttpRequest post = HttpUtil.createPost(url);
            post.body(body);
            post.header("Content-Type", "application/json");
            post.header("x-acs-dingtalk-access-token", getAccessToken());

            try (HttpResponse execute = post.execute()) {
                if (execute.isOk()) {
                    String r = execute.body();
                    log.info("New Ding Ding TODO: {} -- {} -- {} -- {}", post, url, body, r);
                    String requestId = new JSONObject(r).getStr("requestId");
                    return StringUtils.isEmpty(requestId) ? "" : r;
                } else {
                    log.error("New Ding Ding failure，{} -- {} -- {}", url, body, execute.body());
                }
            } catch (Exception e) {
                log.error("New Ding Ding exception，{} -- {}", url, body, e);
            }

            if (attemptCount < maxAttempts) {
                log.info("New Ding Ding Try again a {} time...", attemptCount);
//                ThreadUtil.sleep(1000);
            }
        }
        return null;
    }
    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////




    //////////////////////////////////////////////////获取token//////////////////////////////////////////////////////////
    private static String getAccessToken() {
        lock.lock();

        try {
            // 项目第一次调用时初始化
            DingTalkConfig bean = SpringUtil.getBean(DingTalkConfig.class);
            if (StringUtils.isEmpty(APP_ID) || StringUtils.isEmpty(APP_SECRET) || StringUtils.isEmpty(BASE_URL)) {
                APP_ID = bean.getAppKey();
                APP_SECRET = bean.getAppSecret();
                BASE_URL = bean.getServerApiUrl() == null ? "https://api.dingtalk.com" : "http://".concat(bean.getServerApiUrl());
            }

            // 检查是否需要重新获取token
            if (StringUtils.isEmpty(accessToken) || tokenExpireTime.before(new Date(System.currentTimeMillis() - 2 * 60 * 60 * 1000))) {
                String token = fetchAccessToken(2);
                if (!token.isEmpty()) {
                    accessToken = token;
                    tokenExpireTime = new Date(System.currentTimeMillis() + 2 * 60 * 60 * 1000); // 设置token过期时间为2小时后
                }
            }
        } finally {
            lock.unlock();
        }
        return accessToken;
    }


    /**
     * 获取登录token
     *
     * @param maxRetries 重试次数两次
     * @return 返回token
     */
    private static String fetchAccessToken(int maxRetries) {
        String token = "";
        int retries = 0;

        while (retries < maxRetries) {
            try {
                Map<String, String> bodyMap = new HashMap<>();
                bodyMap.put("appKey", APP_ID);
                bodyMap.put("appSecret", APP_SECRET);
                String post = HttpUtil.post(String.format(DING_TALK_ACCESS_TOKEN_URL, BASE_URL), JSONUtil.toJsonStr(bodyMap));
                log.info("新钉钉登录接口获取: {} - {}", post, bodyMap);
                token = new JSONObject(post).getStr("accessToken");
                break;
            } catch (Exception e) {
                retries++;
                log.error("新钉钉登录接口返回异常或token为空，正在重试... {}", e.getMessage());
                if (retries >= maxRetries) {
                    log.error("新钉钉登录接口达到最大重试次数，放弃重试。");
                    break;
                }
            }
        }
        return token;
    }
    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}


