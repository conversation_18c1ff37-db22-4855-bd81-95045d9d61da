package com.rutong.medical.admin.dto.monitor;

import com.soft.common.core.object.MyPageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("DeviceMonitorQueryDTO")
@Data
public class DeviceMonitorQueryDTO extends MyPageParam {

    @ApiModelProperty(value = "监控名称/编号")
    private String keyWord;

    @ApiModelProperty(value = "位置")
    private String spacePath;

    @ApiModelProperty(value = "ip")
    private String ip;

    @ApiModelProperty(value = "监控类型(1:枪机,2:球机)")
    private Integer monitorType;

}
