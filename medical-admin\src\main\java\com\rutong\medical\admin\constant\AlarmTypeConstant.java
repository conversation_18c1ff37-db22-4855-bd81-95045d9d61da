package com.rutong.medical.admin.constant;

import lombok.Getter;

/**
 * @ClassName AlarmTypeEnum
 * @Description 报警类型
 * <AUTHOR>
 * @Date 2025/7/15 9:02
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
public interface AlarmTypeConstant {


    /**
     * 没有任何报警
     */
    Byte NORMAL = 0;
    /**
     * 低电压报警
     */
    Byte LOW_VLTAGE_STATUS = 1;
    /**
     * 按键报警
     */
    Byte KEY_STATUS = 2;
    /**
     * 防拆报警
     */
    Byte TAKE_STATUS = 3;
    /**
     * 红外入侵报警
     */
    Byte INFRARED_INVADE_STATUS = 4;
}
