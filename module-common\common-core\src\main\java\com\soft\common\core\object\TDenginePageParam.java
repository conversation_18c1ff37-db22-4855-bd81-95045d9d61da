package com.soft.common.core.object;

import lombok.Data;

/**
 * @ClassName TDenginePageParam
 * @Description TDengine专用分页
 * <AUTHOR>
 * @Date 2025/7/12 15:09
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Data
public class TDenginePageParam {

    private Integer pageNum = 1;
    private Integer pageSize = 10;
    private String orderBy;
    private Long startTime;
    private Long endTime;

    public long getOffset() {
        return (pageNum - 1L) * pageSize;
    }
}
