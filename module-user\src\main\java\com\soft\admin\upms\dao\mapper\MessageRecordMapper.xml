<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.MessageRecordMapper">
    <resultMap type="com.soft.admin.upms.model.message.MessageRecord" id="MessageRecordResult">
        <result property="id" column="id" />
        <result property="title" column="title" />
        <result property="level" column="level" />
        <result property="type" column="type" />
        <result property="content" column="content" />
        <result property="busiId" column="busi_id" />
        <result property="busiType" column="busi_type" />
        <result property="hyperlink" column="hyperlink" />
        <result property="sendDate" column="send_date" />
        <result property="sendTime" column="send_time" />
        <result property="createTime" column="create_time" />
        <result property="sendUserId" column="send_user_id" />
        <result property="receiveUserId" column="receive_user_id" />
        <result property="receiveTime" column="receive_time" />
        <result property="readTime" column="read_time" />
        <result property="isRead" column="is_read" />
        <result property="deletedFlag" column="deleted_flag" />
    </resultMap>

    <sql id="selectSpMessageRecordVo">
        select id, title, level, type, content, busi_id, busi_type, hyperlink, send_date, send_time, create_time, send_user_id, receive_user_id, receive_time, read_time, is_read, deleted_flag from sp_message_record
    </sql>


    <select id="countMessageRecord" resultType="int">
        SELECT
            count(1)
        FROM
            sp_message_record
        WHERE
            busi_type = 'FIRE'
          AND title = '科室自查'
          AND receive_user_id = #{receiveUserId}
          AND busi_id = #{busiId}
          AND deleted_flag = 1
          AND create_time >= NOW() - INTERVAL 30 MINUTE
    </select>
</mapper>