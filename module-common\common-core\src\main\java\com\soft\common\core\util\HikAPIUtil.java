package com.soft.common.core.util;

import com.hikvision.artemis.sdk.ArtemisHttpUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName HikAPIUtil
 * @Description 海康接口公共类
 * <AUTHOR>
 * @Date 2021/4/22 11:37
 * @Version V1.0
 **/
public class HikAPIUtil {
    /*
    *
     *
     * @Description  海康post方法
     * <AUTHOR>
     * @Date 2021/4/22
     * @param url
     * @param body
     * @return java.lang.String
     **/
    public static String doPost(String url,String body){
        String result = ArtemisHttpUtil.doPostStringArtemis(getPath(url), body, null,
                null,"application/json");
        return result;
    }

    /*
     *
     *
     * @Description 获取海康请求地址
     * <AUTHOR>
     * @Date 2021/4/22
     * @param url
     * @return java.util.Map<java.lang.String,java.lang.String>
     **/
    private static Map<String, String> getPath(String url){
        Map<String, String> path = new HashMap<String, String>(2) {{put("https://", url);}};
        return path;
    }
}
