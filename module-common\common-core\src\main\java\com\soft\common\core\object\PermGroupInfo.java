package com.soft.common.core.object;

import lombok.Data;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Collection;

/**
 * 权限资源Dto。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
@Data
public class PermGroupInfo implements Serializable {
    private static final long serialVersionUID = 3483556305598036508L;
    /**
     * 空间权限
     */
    private Collection<String> groupSpacePermList;
    /**
     * 设备权限
     */
    private Collection<String> groupEquipmentPermList;

    /**
     * 工单权限
     */
    private Collection<String> groupWorkorderPermList;

    /**
     * 是否全部空间
     */
    private boolean allSpace = false;

    /**
     * 是否全部设备
     */
    private boolean allEqipment = false;

    /**
     * 是否全部工单
     */
    private boolean allWorkorder = false;

    public Collection<String> getGroupWorkorderPermList() {
        if (allWorkorder) return Arrays.asList("OPERATIONS", "CLEANING", "TRANSPORT", "PROPERTY");
        return groupWorkorderPermList;
    }
}