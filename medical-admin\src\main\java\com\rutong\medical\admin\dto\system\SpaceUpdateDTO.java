package com.rutong.medical.admin.dto.system;


import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SpaceUpdateDTO {

    @NotNull(message = "数据验证失败，空间id不能为空！")
    private Long id;

    @ApiModelProperty(value = "上级id")
    private Long parentId;

    @ApiModelProperty(value = "编码")
    @NotBlank(message = "空间编码不能为空！")
    private String code;

    @ApiModelProperty(value = "空间名称")
    @NotBlank(message = "空间名称不能为空！")
    private String name;

    @ApiModelProperty(value = "坐标")
    private String coordinate;
    /**
     *是否公共区域 0否1是
     */
    @ApiModelProperty(value = "是否公共区域 0否1是")
    private String commonArea;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Long sort;

    @ApiModelProperty(value = "二维模型文件地址")
    private String modelTwoDimensional;

    @ApiModelProperty(value = "三维模型文件地址")
    private String modelThreeDimensional;
}
