package com.soft.admin.upms.model.message;

import cn.hutool.core.date.DateUtil;
import com.soft.admin.upms.enums.MessageRecordBusiTypeEnums;
import com.soft.admin.upms.enums.MessageRecordTypeEnums;

import java.util.Date;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class MessageRecordContentNoticeEstateTemplate extends MessageRecordContentNoticeTemplate {

    private static final MessageRecordBusiTypeEnums BUSI_TYPE = MessageRecordBusiTypeEnums.ESTATE;

    private static final String TITLE = "${type}考核评分";

    //# 请您对#月份#家政人员#的服务满意度进行评分！
//    private static final String CONTENT = "请您对${monthDate}家政/工程人员的服务满意度进行评分！";
    private static final String CONTENT = "请您对%s%s人员的服务满意度进行评分！（本次考核为模拟推送，请配合打分，如有疑问请咨询夏老师 663939）";

    private static final String HYPERLINK = "/operation/assess/assess?type=${type}";


    public MessageRecordContent push(Integer type, Date monthDate, Long busiId, String level, Long sendUserId, Long... receiveUserIds) {
        messageRecordContent.setTitle(TITLE.replace("${type}", Objects.equals(type, 1) ? "家政" : Objects.equals(type, 2) ? "工程" : ""));
        messageRecordContent.setBusiId(busiId);
        messageRecordContent.setLevel(level);
//        messageRecordContent.setContent(CONTENT.replace("${monthDate}", DateUtil.format(monthDate, "yyyy-MM")));
        messageRecordContent.setContent(String.format(CONTENT,DateUtil.format(monthDate, "yyyy-MM"),Objects.equals(type, 1) ? "家政" : Objects.equals(type, 2) ? "工程" : ""));
        messageRecordContent.setSendUserId(sendUserId);
        messageRecordContent.setReceiveUserIds(Stream.of(receiveUserIds).collect(Collectors.toList()));
        messageRecordContent.setHyperlink(HYPERLINK.replace("${type}", String.valueOf(type)));
        messageRecordContent.setType(MessageRecordTypeEnums.TODO);
        messageRecordContent.setBusiType(BUSI_TYPE);
        pushJiGuangMessage(Boolean.FALSE);
        push();
        callback(messageRecordContent);
        return messageRecordContent;
    }

}
