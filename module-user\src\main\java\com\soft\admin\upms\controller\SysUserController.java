package com.soft.admin.upms.controller;


import com.soft.admin.upms.dto.SysUserQueryDTO;
import com.soft.admin.upms.dto.SysUserSaveOrUpdateDTO;
import com.soft.admin.upms.service.SysUserService;
import com.soft.admin.upms.vo.SysUserDetailVO;
import com.soft.admin.upms.vo.SysUserListVO;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.common.core.object.TokenData;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/system/user")
public class SysUserController {

    @Resource
    private SysUserService sysUserService;


    @ApiOperation("新增或修改用户信息")
    @PostMapping("/saveOrUpdate")
    public ResponseResult<Long> saveOrUpdate(@RequestBody SysUserSaveOrUpdateDTO sysUserSaveOrUpdateDTO) {
        return ResponseResult.success(sysUserService.saveOrUpdate(sysUserSaveOrUpdateDTO));
    }

    @ApiOperation("上传头像")
    @PostMapping("/updateImg")
    public ResponseResult updateImg(@RequestBody SysUserSaveOrUpdateDTO sysUserSaveOrUpdateDTO) {
        Long userId = TokenData.takeFromRequest().getUserId();
        sysUserSaveOrUpdateDTO.setUserId(userId);
        sysUserService.updateImg(sysUserSaveOrUpdateDTO);
        return ResponseResult.success();
    }

    @ApiOperation("查询用户列表")
    @GetMapping("/list")
    public ResponseResult<MyPageData<SysUserListVO>> list(SysUserQueryDTO sysUserQueryDTO) {
        MyPageData<SysUserListVO> pageData = sysUserService.list(sysUserQueryDTO);
        return ResponseResult.success(pageData);
    }

    @ApiOperation("修改用户状态")
    @GetMapping("/updateStatus/{userId}")
    public ResponseResult<Void> updateStatus(@PathVariable Long userId) {
        sysUserService.updateStatus(userId);
        return ResponseResult.success();
    }

    @ApiOperation("用户详情")
    @GetMapping("/detail/{userId}")
    public ResponseResult<SysUserDetailVO> detail(@PathVariable Long userId) {
        SysUserDetailVO sysUserDetailVO = sysUserService.detail(userId);
        return ResponseResult.success(sysUserDetailVO);
    }

    @ApiOperation("查询部门下的人员")
    @GetMapping("/listByDeptId")
    public ResponseResult<MyPageData<SysUserListVO>> listByDeptId(SysUserQueryDTO sysUserQueryDTO) {
        MyPageData<SysUserListVO> pageData = sysUserService.listByDeptId(sysUserQueryDTO);
        return ResponseResult.success(pageData);
    }

    @ApiOperation("反转用户身份")
    @GetMapping("/flipManager")
    public ResponseResult<Void> flipManager(@RequestParam Long userId,@RequestParam Long deptId) {
        sysUserService.flipManager(userId,deptId);
        return  ResponseResult.success();
    }

    @ApiOperation("区分部门查询用户列表")
    @GetMapping("/listOfDiffDept")
    public ResponseResult<MyPageData<SysUserListVO>> listOfDiffDept(SysUserQueryDTO sysUserQueryDTO) {
        MyPageData<SysUserListVO> pageData = sysUserService.listOfDiffDept(sysUserQueryDTO);
        return ResponseResult.success(pageData);
    }
}
