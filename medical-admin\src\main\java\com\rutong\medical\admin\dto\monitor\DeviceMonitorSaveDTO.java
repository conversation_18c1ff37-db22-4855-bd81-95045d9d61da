package com.rutong.medical.admin.dto.monitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("DeviceMonitorSaveDTO")
@Data
public class DeviceMonitorSaveDTO {

    @ApiModelProperty(value = "视频监控表ID")
    private Long id;

    @ApiModelProperty(value = "监控编号")
    private String monitorCode;

    @ApiModelProperty(value = "监控名称")
    private String monitorName;

    @ApiModelProperty(value = "监控类型(1:枪机,2:球机)")
    private Integer monitorType;

    @ApiModelProperty(value = "安装位置")
    private Long spaceId;

    @ApiModelProperty(value = "ip地址")
    private String ip;

    @ApiModelProperty(value = "端口")
    private Long port;

    @ApiModelProperty(value = "通道号")
    private String channelNum;

    @ApiModelProperty(value = "用户名")
    private String userCode;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "生产厂家")
    private String factory;
}
