package com.soft.admin.upms.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 通行人员类型
 * @date 2023-07-11  09:41:02
 */
public enum PassageUserTypeEnums {
    PROPERTY("物业", "1"),
    ENTERPRISES("企业", "2"),
    MERCHANT("商家", "3"),
    VISITOR("访客", "4"),
    OTHERS("其他人", "5"),
    ;

    private final String name;

    private final String value;

    PassageUserTypeEnums(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public String getValue() {
        return value;
    }
}
