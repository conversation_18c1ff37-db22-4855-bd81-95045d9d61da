package com.rutong.medical.admin.mapper.system;

import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.rutong.medical.admin.constant.SpaceTypeEnum;
import com.rutong.medical.admin.entity.system.Space;
import com.soft.common.core.annotation.EnablePermGroup;
import com.soft.common.core.base.dao.BaseDaoMapper;

/**
 * 空间Mapper接口
 *
 * <AUTHOR>
 * @date 2023-03-29
 */
@EnablePermGroup(excluseMethodName = {"insertBatch", "queryAll", "selectById"})
public interface SpaceMapper extends BaseDaoMapper<Space> {

    List<Space> queryAll();

    int insertBatch(@Param("spaces") List<Space> spaces);

    /**
     * 查询指定类型的所有空间
     *
     * @param spaceType
     * @param spaceIds
     * @return
     */
    List<Space> queryAllOnlyType(@Param("spaceType") SpaceTypeEnum spaceType,
        @Param("spaceIds") Collection<Long> spaceIds);

    /**
     * 查询详情
     * 
     * @param id
     * @return
     */
    Space selectById(@Param("id") Long id);
}
