package com.rutong.medical.admin.dto.system;

import com.rutong.medical.admin.constant.SpaceTypeEnum;
import com.soft.common.core.object.MyPageParam;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SpaceQueryDTO extends MyPageParam {

    private SpaceTypeEnum type;

    private String name;

    private String path;

    @ApiModelProperty(value = "空间ID")
    private Long spaceId;

    @ApiModelProperty(value = "空间完整名称")
    private String fullName;

    private Integer deep;
}
