package com.soft.common.core.http;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.concurrent.FutureCallback;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StopWatch;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.net.URLConnection;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * http client 业务逻辑处理类
 */
@Slf4j
public class HttpClientUtil {

    private static Logger LOG = LoggerFactory.getLogger(HttpClientUtil.class);

    private static String utf8Charset = "utf-8";

    /*
     *
     * @Description 向指定的url发送一次post请求,参数是List
     * <AUTHOR>
     * @Date 2021/4/20
     * @param baseUrl 请求地址
     * @param list 请求参数,格式是List<NameValuePair>
     * @return java.lang.String
     **/
    public static String httpSyncPost(String baseUrl, List<BasicNameValuePair> list) {
        List<BasicNameValuePair> headerList = new ArrayList<BasicNameValuePair>();
        return httpSyncPost(baseUrl, list, headerList);
    }

    /**
     * 向指定的url发送一次post请求,参数是List<NameValuePair>
     *
     * @param baseUrl    请求地址
     * @param list       请求参数,格式是List<NameValuePair>
     * @param headerList 请求头
     * @return 返回结果, 请求失败时返回null
     * @apiNote http接口处用 @RequestParam接收参数
     */
    public static String httpSyncPost(String baseUrl, List<BasicNameValuePair> list, List<BasicNameValuePair> headerList) {

        CloseableHttpClient httpClient = HttpClientFactory.getInstance().getHttpSyncClientPool().getHttpClient();
        HttpPost httpPost = new HttpPost(baseUrl);
        for (BasicNameValuePair pair : headerList) {
            httpPost.setHeader(pair.getName(), pair.getValue());
        }
        // Parameters
        LOG.warn("==== Parameters ======" + list);
        CloseableHttpResponse response = null;
        try {
            httpPost.setEntity(new UrlEncodedFormEntity(list));
            // httpPost.setHeader("Connection","close");
            response = httpClient.execute(httpPost);
            LOG.warn("========HttpResponseProxy：========" + response.getStatusLine());
            HttpEntity entity = response.getEntity();
            String result = null;
            if (entity != null) {
                result = EntityUtils.toString(entity, "UTF-8");
                LOG.warn("========Response=======" + result);
            }
            EntityUtils.consume(entity);
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    log.error(e.getMessage(),e);
                }
            }
        }
        return null;
    }

    /*
     *
     *
     * @Description 向指定的url发送一次post请求,参数是字符串
     * <AUTHOR>
     * @Date 2021/4/20
     * @param baseUrl 请求地址
     * @param postString 请求参数,格式是json.toString()
     * @return java.lang.String
     **/
    public static String httpSyncPost(String baseUrl, String postString) {
        List<BasicNameValuePair> headerList = new ArrayList<BasicNameValuePair>();
        return httpSyncPost(baseUrl, postString, headerList);
    }

    public static JSONObject httpPostJson(String baseUrl,String bodys, List<BasicNameValuePair> headerList){
        CloseableHttpClient httpClient = HttpClients.createDefault();
        ResponseHandler<String> responseHandler = new BasicResponseHandler();
        try {
            httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(baseUrl);
            for (BasicNameValuePair pair : headerList) {
                httpPost.setHeader(pair.getName(), pair.getValue());
            }
            //给httpPost设置JSON格式的参数
            StringEntity requestEntity = new StringEntity(bodys,"utf-8");
            requestEntity.setContentEncoding("UTF-8");
            requestEntity.setContentType("application/json");
            httpPost.setEntity(requestEntity);
            String execute = httpClient.execute(httpPost , responseHandler);
            //返回结果解析为Map
            JSONObject jsonObject = JSONObject.parseObject(execute);
            return jsonObject;
        }catch (Exception e){
            log.error(e.getMessage(),e);
        }
        return null;
    }

    /**
     * 向指定的url发送一次post请求,参数是字符串
     *
     * @param baseUrl    请求地址
     * @param postString 请求参数,格式是json.toString()
     * @param headerList 请求头内容
     * @return 返回结果, 请求失败时返回null
     * @apiNote http接口处用 @RequestBody接收参数
     */
    public static String httpSyncPost(String baseUrl, String postString, List<BasicNameValuePair> headerList) {

        CloseableHttpClient httpClient = HttpClientFactory.getInstance().getHttpSyncClientPool().getHttpClient();
        HttpPost httpPost = new HttpPost(baseUrl);
        for (BasicNameValuePair pair : headerList) {
            httpPost.setHeader(pair.getName(), pair.getValue());
        }
        // parameters
        LOG.warn("==== Parameters ======" + postString);
        CloseableHttpResponse response = null;
        try {
            if (postString == null || "".equals(postString)) {
                throw new Exception("missing post String");
            }

            StringEntity stringEntity = new StringEntity(postString.toString(), utf8Charset);
            stringEntity.setContentEncoding("UTF-8");
            stringEntity.setContentType("application/json");
            httpPost.setEntity(stringEntity);

            response = httpClient.execute(httpPost);
            LOG.warn("========HttpResponseProxy：========" + response.getStatusLine());
            HttpEntity entity = response.getEntity();
            String result = null;
            if (entity != null) {
                result = EntityUtils.toString(entity, "UTF-8");
                LOG.warn("========Response=======" + result);
            }
            EntityUtils.consume(entity);
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    log.error(e.getMessage(),e);
                }
            }
        }
        return null;
    }


    /*
     *
     *
     * @Description  向指定的url发送一次get请求
     * <AUTHOR>
     * @Date 2021/4/20
     * @param baseUrl 请求地址
     * @param paramlist 请求参数,格式是List
     * @return java.lang.String
     **/
    public static String httpSyncGet(String baseUrl, List<BasicNameValuePair> paramlist) {
        List<BasicNameValuePair> headerList = new ArrayList<BasicNameValuePair>();
        return httpSyncGet(baseUrl, paramlist, headerList);
    }

    /**
     * 向指定的url发送一次get请求,参数是List<NameValuePair>
     *
     * @param baseUrl    请求地址
     * @param paramlist  请求参数,格式是List<NameValuePair>
     * @param headerList 请求头内容
     * @return 返回结果, 请求失败时返回null
     * @apiNote http接口处用 @RequestParam接收参数
     */
    public static String httpSyncGet(String baseUrl, List<BasicNameValuePair> paramlist, List<BasicNameValuePair> headerList) {

        CloseableHttpClient httpClient = HttpClientFactory.getInstance().getHttpSyncClientPool().getHttpClient();
        HttpGet httpGet = new HttpGet(baseUrl);
        for (BasicNameValuePair pair : headerList) {
            httpGet.setHeader(pair.getName(), pair.getValue());
        }
        // Parameters
        LOG.warn("==== Parameters ======" + paramlist);
        CloseableHttpResponse response = null;
        try {

            if (paramlist != null) {
                String getUrl = EntityUtils.toString(new UrlEncodedFormEntity(paramlist));
                httpGet.setURI(new URI(httpGet.getURI().toString() + "?" + getUrl));
            } else {
                httpGet.setURI(new URI(httpGet.getURI().toString()));
            }

            response = httpClient.execute(httpGet);
            LOG.warn("========HttpResponseProxy：========" + response.getStatusLine());
            HttpEntity entity = response.getEntity();
            String result = null;
            if (entity != null) {
                result = EntityUtils.toString(entity, "UTF-8");
                LOG.warn("========Response=======" + result);
            }
            EntityUtils.consume(entity);
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    log.error(e.getMessage(),e);
                }
            }
        }
        return null;
    }

    /**
     * 向指定的url发送一次get请求,参数是List<NameValuePair>
     *
     * @param baseUrl    请求地址
     * @param paramlist  请求参数,格式是List<NameValuePair>  会拼接到url
     * @param headerList 请求头内容
     * @return 返回结果, 请求失败时返回null
     * @apiNote http接口处用 @RequestParam接收参数
     */
    public static String httpSyncPut(String baseUrl, List<BasicNameValuePair> paramlist, List<BasicNameValuePair> headerList) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("httpSyncPut");


        CloseableHttpClient httpClient = HttpClientFactory.getInstance().getHttpSyncClientPool().getHttpClient();

        HttpPut httpPut = new HttpPut(baseUrl);
        for (BasicNameValuePair pair : headerList) {
            httpPut.setHeader(pair.getName(), pair.getValue());
        }
        // Parameters
        LOG.warn("==== Parameters ======" + paramlist);
        CloseableHttpResponse response = null;
        try {

            if (paramlist != null) {
                String getUrl = EntityUtils.toString(new UrlEncodedFormEntity(paramlist));
                httpPut.setURI(new URI(httpPut.getURI().toString() + "?" + getUrl));
            } else {
                httpPut.setURI(new URI(httpPut.getURI().toString()));
            }

            response = httpClient.execute(httpPut);
            LOG.warn("========HttpResponseProxy：========" + response.getStatusLine());
            HttpEntity entity = response.getEntity();
            String result = null;
            if (entity != null) {
                result = EntityUtils.toString(entity, "UTF-8");
                LOG.warn("========Response=======" + result);
            }
            EntityUtils.consume(entity);

            stopWatch.stop();

            double totalTimeSeconds = stopWatch.getTotalTimeSeconds();
            if (totalTimeSeconds > 2) {
                log.warn("========Time spend ======= \n" + stopWatch.getTotalTimeSeconds() + " s");
            }


            return result;
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    log.error(e.getMessage(),e);
                }
            }
        }
        return null;
    }


    /**
     * 向指定的url发送一次put请求,参数是字符串
     *
     * @param baseUrl       请求地址
     * @param putJsonString 请求参数,格式是json.toString()
     * @param headerList    请求头内容
     * @return 返回结果, 请求失败时返回null
     * @apiNote http接口处用 @RequestBody接收参数
     */
    public static String httpSyncPut(String baseUrl, String putJsonString, List<BasicNameValuePair> headerList) {

        CloseableHttpClient httpClient = HttpClientFactory.getInstance().getHttpSyncClientPool().getHttpClient();
        HttpPut http = new HttpPut(baseUrl);
        for (BasicNameValuePair pair : headerList) {
            http.setHeader(pair.getName(), pair.getValue());
        }
        // parameters
        LOG.warn("==== Parameters ======" + putJsonString);
        CloseableHttpResponse response = null;
        try {
            if (putJsonString == null || "".equals(putJsonString)) {
                throw new Exception("missing post String");
            }

            StringEntity stringEntity = new StringEntity(putJsonString, utf8Charset);
            stringEntity.setContentEncoding("UTF-8");
            stringEntity.setContentType("application/json");
            http.setEntity(stringEntity);

            response = httpClient.execute(http);
            LOG.warn("========HttpResponseProxy：========" + response.getStatusLine());
            HttpEntity entity = response.getEntity();
            String result = null;
            if (entity != null) {
                result = EntityUtils.toString(entity, "UTF-8");
                LOG.warn("========Response=======" + result);
            }
            EntityUtils.consume(entity);
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    log.error(e.getMessage(),e);
                }
            }
        }
        return null;
    }


    /**
     *
     *
     * @Description
     * <AUTHOR>
     * @Date 2021/4/20
     * @param baseUrl 请求地址
     * @param urlParams  请求参数,格式是String
     * @return java.lang.String
     **/
    public static String httpSyncGet(String baseUrl, String urlParams) {
        List<BasicNameValuePair> headerList = new ArrayList<BasicNameValuePair>();
        return httpSyncGet(baseUrl, urlParams, headerList);
    }

    /**
     * 向指定的url发送一次get请求,参数是字符串
     *
     * @param baseUrl    请求地址
     * @param urlParams  请求参数,格式是String
     * @param headerList 头字段内容
     * @return 返回结果, 请求失败时返回null
     * @apiNote http接口处用 @RequestParam接收参数
     */
    public static String httpSyncGet(String baseUrl, String urlParams, List<BasicNameValuePair> headerList) {

        CloseableHttpClient httpClient = HttpClientFactory.getInstance().getHttpSyncClientPool().getHttpClient();
        HttpGet httpGet = new HttpGet(baseUrl);
        for (BasicNameValuePair pair : headerList) {
            httpGet.setHeader(pair.getName(), pair.getValue());
        }
        // Parameters
        LOG.warn("==== Parameters ======" + urlParams);
        CloseableHttpResponse response = null;
        try {

            if (null != urlParams || "".equals(urlParams)) {
                httpGet.setURI(new URI(httpGet.getURI().toString() + "?" + urlParams));
            } else {
                httpGet.setURI(new URI(httpGet.getURI().toString()));
            }

            response = httpClient.execute(httpGet);
            LOG.warn("========HttpResponseProxy：========" + response.getStatusLine());
            HttpEntity entity = response.getEntity();
            String result = null;
            if (entity != null) {
                result = EntityUtils.toString(entity, "UTF-8");
                LOG.warn("========Response=======" + result);
            }
            EntityUtils.consume(entity);
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    log.error(e.getMessage(),e);
                }
            }
        }
        return null;
    }

    /**
     * 向指定的url发送一次get请求,参数是字符串
     *
     * @param baseUrl 请求地址
     * @return 返回结果, 请求失败时返回null
     * @apiNote http接口处用 @RequestParam接收参数
     */
    public static String httpSyncGet(String baseUrl) {

        CloseableHttpClient httpClient = HttpClientFactory.getInstance().getHttpSyncClientPool().getHttpClient();
        HttpGet httpGet = new HttpGet(baseUrl);

        CloseableHttpResponse response = null;
        try {
            httpGet.setURI(new URI(httpGet.getURI().toString()));
            response = httpClient.execute(httpGet);
            LOG.warn("========HttpResponseProxy：========" + response.getStatusLine());
            HttpEntity entity = response.getEntity();
            String result = null;
            if (entity != null) {
                result = EntityUtils.toString(entity, "UTF-8");
                LOG.warn("========Response=======" + result);
            }
            EntityUtils.consume(entity);
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    log.error(e.getMessage(),e);
                }
            }
        }
        return null;
    }

    /**
     * 向指定的url发送一次异步post请求,参数是字符串
     *
     * @param baseUrl    请求地址
     * @param postString 请求参数,格式是json.toString()
     * @param urlParams  请求参数,格式是String
     * @param callback   回调方法,格式是FutureCallback
     * @return 返回结果, 请求失败时返回null
     * @apiNote http接口处用 @RequestParam接收参数
     */
    public static void httpAsyncPost(String baseUrl, String postString, String urlParams,
                                     FutureCallback<HttpResponse> callback) throws Exception {
        if (baseUrl == null || "".equals(baseUrl)) {
            LOG.warn("we don't have base url, check config");
            throw new Exception("missing base url");
        }
        CloseableHttpAsyncClient hc = HttpClientFactory.getInstance().getHttpAsyncClientPool().getAsyncHttpClient();
        try {
            hc.start();
            HttpPost httpPost = new HttpPost(baseUrl);

            // httpPost.setHeader("Connection","close");

            if (null != postString) {
                LOG.debug("exeAsyncReq post postBody={}", postString);
                StringEntity entity = new StringEntity(postString.toString(), utf8Charset);
                entity.setContentEncoding("UTF-8");
                entity.setContentType("application/json");
                httpPost.setEntity(entity);
            }

            if (null != urlParams) {

                httpPost.setURI(new URI(httpPost.getURI().toString() + "?" + urlParams));
            }

            LOG.warn("exeAsyncReq getparams:" + httpPost.getURI());

            hc.execute(httpPost, callback);

        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
    }

    /**
     * 向指定的url发送一次异步post请求,参数是字符串
     *
     * @param baseUrl   请求地址
     * @param urlParams 请求参数,格式是List<BasicNameValuePair>
     * @param callback  回调方法,格式是FutureCallback
     * @return 返回结果, 请求失败时返回null
     * @apiNote http接口处用 @RequestParam接收参数
     */
    public static void httpAsyncPost(String baseUrl, List<BasicNameValuePair> postBody,
                                     List<BasicNameValuePair> urlParams, FutureCallback<HttpResponse> callback) throws Exception {
        if (baseUrl == null || "".equals(baseUrl)) {
            LOG.warn("we don't have base url, check config");
            throw new Exception("missing base url");
        }

        try {
            CloseableHttpAsyncClient hc = HttpClientFactory.getInstance().getHttpAsyncClientPool().getAsyncHttpClient();

            hc.start();

            HttpPost httpPost = new HttpPost(baseUrl);

            // httpPost.setHeader("Connection","close");

            if (null != postBody) {
                LOG.debug("exeAsyncReq post postBody={}", postBody);
                UrlEncodedFormEntity entity = new UrlEncodedFormEntity(postBody, "UTF-8");
                httpPost.setEntity(entity);
            }

            if (null != urlParams) {

                String getUrl = EntityUtils.toString(new UrlEncodedFormEntity(urlParams));

                httpPost.setURI(new URI(httpPost.getURI().toString() + "?" + getUrl));
            }

            LOG.warn("exeAsyncReq getparams:" + httpPost.getURI());

            hc.execute(httpPost, callback);

        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
    }

    /**
     * 向指定的url发送一次异步get请求,参数是String
     *
     * @param baseUrl   请求地址
     * @param urlParams 请求参数,格式是String
     * @param callback  回调方法,格式是FutureCallback
     * @return 返回结果, 请求失败时返回null
     * @apiNote http接口处用 @RequestParam接收参数
     */
    public static void httpAsyncGet(String baseUrl, String urlParams, FutureCallback<HttpResponse> callback)
            throws Exception {

        if (baseUrl == null || "".equals(baseUrl)) {
            LOG.warn("we don't have base url, check config");
            throw new Exception("missing base url");
        }
        CloseableHttpAsyncClient hc = HttpClientFactory.getInstance().getHttpAsyncClientPool().getAsyncHttpClient();
        try {

            hc.start();

            HttpGet httpGet = new HttpGet(baseUrl);

            // httpGet.setHeader("Connection","close");

            if (null != urlParams || "".equals(urlParams)) {

                httpGet.setURI(new URI(httpGet.getURI().toString() + "?" + urlParams));
            } else {
                httpGet.setURI(new URI(httpGet.getURI().toString()));
            }

            LOG.warn("exeAsyncReq getparams:" + httpGet.getURI());

            hc.execute(httpGet, callback);

        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }

    }

    /**
     * 向指定的url发送一次异步get请求,参数是List<BasicNameValuePair>
     *
     * @param baseUrl   请求地址
     * @param urlParams 请求参数,格式是List<BasicNameValuePair>
     * @param callback  回调方法,格式是FutureCallback
     * @return 返回结果, 请求失败时返回null
     * @apiNote http接口处用 @RequestParam接收参数
     */
    public static void httpAsyncGet(String baseUrl, List<BasicNameValuePair> urlParams,
                                    FutureCallback<HttpResponse> callback) throws Exception {
        if (baseUrl == null || "".equals(baseUrl)) {
            LOG.warn("we don't have base url, check config");
            throw new Exception("missing base url");
        }

        try {
            CloseableHttpAsyncClient hc = HttpClientFactory.getInstance().getHttpAsyncClientPool().getAsyncHttpClient();

            hc.start();

            HttpPost httpGet = new HttpPost(baseUrl);

            // httpGet.setHeader("Connection","close");

            if (null != urlParams) {

                String getUrl = EntityUtils.toString(new UrlEncodedFormEntity(urlParams));

                httpGet.setURI(new URI(httpGet.getURI().toString() + "?" + getUrl));
            }

            LOG.warn("exeAsyncReq getparams:" + httpGet.getURI());

            hc.execute(httpGet, callback);

        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
    }

    /**
     * 发送post请求
     *
     * @param url        路径
     * @param jsonObject 参数(json类型)
     * @param encoding   编码格式
     * @return
     * @throws ParseException
     * @throws IOException
     */
    public static String send(String url, JSONObject jsonObject, String encoding) throws ParseException, IOException {
        String body = "";

        //创建httpclient对象
        CloseableHttpClient client = HttpClients.createDefault();
        //创建post方式请求对象
        HttpPost httpPost = new HttpPost(url);

        //装填参数
        StringEntity s = new StringEntity(jsonObject.toString(), "utf-8");
        s.setContentEncoding(new BasicHeader(HTTP.CONTENT_TYPE,
                "application/json"));
        //设置参数到请求对象中
        httpPost.setEntity(s);
//        System.out.println("请求地址：" + url);
//        System.out.println("请求参数："+nvps.toString());

        //设置header信息
        //指定报文头【Content-type】、【User-Agent】
//        httpPost.setHeader("Content-type", "application/x-www-form-urlencoded");
        httpPost.setHeader("Content-type", "application/json");
        httpPost.setHeader("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");

        //执行请求操作，并拿到结果（同步阻塞）
        CloseableHttpResponse response = client.execute(httpPost);
        //获取结果实体
        HttpEntity entity = response.getEntity();
        if (entity != null) {
            //按指定编码转换结果实体为String类型
            body = EntityUtils.toString(entity, encoding);
        }
        EntityUtils.consume(entity);
        //释放链接
        response.close();
        return body;

    }

    public static boolean checkService(String serviceUrl) {
        try {
            URL url = new URL(serviceUrl);
            URLConnection conn = url.openConnection();
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);
            if (conn instanceof HttpURLConnection) {
                HttpURLConnection httpConn = (HttpURLConnection) conn;
                int statusCode = httpConn.getResponseCode();
                if (statusCode == 200) {
                    return true;
                }
            }
        } catch (IOException e) {
           log.info("serviceUrl:{}服务配置错误",serviceUrl);
        }
        return false;
    }
}