package com.soft.admin.upms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.soft.admin.upms.api.dingtalk.service.IOaDepartmentApi;
import com.soft.admin.upms.api.dingtalk.service.IOaOauth2Api;
import com.soft.admin.upms.api.dingtalk.service.IOaUserApi;
import com.soft.admin.upms.dao.*;
import com.soft.admin.upms.dto.dintalk.OaAuthScopeDTO;
import com.soft.admin.upms.dto.dintalk.OaDepartmentDTO;
import com.soft.admin.upms.dto.dintalk.OaUserDTO;
import com.soft.admin.upms.enums.OaTypeEnums;
import com.soft.admin.upms.model.*;
import com.soft.admin.upms.model.constant.SysUserStatus;
import com.soft.admin.upms.model.constant.SysUserType;
import com.soft.admin.upms.service.OaSyncService;
import com.soft.common.core.exception.ServiceException;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.soft.admin.upms.enums.OaTypeEnums.DING_TALK;

/**
 * @Description OA系统同步Service实现
 * @Date 0009, 2023年8月9日 15:09
 * <AUTHOR>
 **/
@Slf4j
@Service
public class OaSyncServiceImpl implements OaSyncService {

    //    public static final long DEFAULT_PROJECT_ID = 1642798815410917376l;
//    public static final long DEFAULT_ROLE_ID = 1640972651776184321l;
    public static final String CONFIG_KEY_DEFAULT_ROLE = "defaultRole";

    public static final String CONFIG_KEY_DEFAULT_PROPERTY_ROLE = "defaultPropertyRole";
    public static final String CONFIG_KEY_DEFAULT_PASSWORD = "defaultPassword";

    public static final String CONFIG_KEY_DEFAULT_PERMGROUP = "defaultPermGroup";

    /**
     * 账号的同步方式 JobNumber phone
     */
    public static final String CONFIG_KEY_SYNCDINGTALK_MODEL = "dingtalkSyncLoginName";

    /**
     * 钉钉是否同步手机号码 true同步 false不同步
     */
    public static final String CONFIG_KEY_SYNCDINGTALK_PHONE = "dingtalkSyncPhone";

    /**
     * 钉钉是否只同步有工号的人员 true同步 false不同步
     */
    public static final String CONFIG_KEY_SYNCDINGTALK_ONLYJOBNUMBER = "dingtalkOnlySyncJobNumber";

    /**
     * 默认角色
     */
    private static String defaultRole = "123456789";
    /**
     * 默认角色
     */
    private static String defaultPropertyRole = "123456788";
    /**
     * 默认密码
     */
    private static String defaultPassword = "123456";
    /**
     * 默认密码
     */
    private static String defaultPermGroup = "123456789";
    /**
     * 钉钉默认账户名获取字段
     */
    private static String dingtalkSyncLoginName = "phone";

    private static final String PHONE = "phone";

    /**
     * 钉钉是否同步手机号码
     */
    private static boolean dingtalkSyncPhone = true;

    /**
     * 钉钉是否只同步有工号的人员
     */
    private static boolean dingtalkOnlySyncJobNumber = false;

    public static final String LOCK_KEY_SYNC_DATA = "oa:syncData:lock";

    private Long syncVersion = System.currentTimeMillis();

    @Resource
    private SysDeptMapper sysDeptMapper;
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private OaDeptMapper oaDeptMapper;
    @Resource
    private OaUserMapper oaUserMapper;
    @Resource
    private SysUserRoleMapper sysUserRoleMapper;
    @Resource
    private IOaOauth2Api oaOauth2Api;
    @Resource
    private IOaDepartmentApi oaDepartmentApi;
    @Resource
    private IOaUserApi oaUserApi;
    @Resource
    private PasswordEncoder passwordEncoder;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ThreadPoolTaskExecutor syncDataTaskExecutor;

    @Resource
    private SysConfigMapper sysConfigMapper;

    @Resource
    private SysPermCodeMapper sysPermCodeMapper;

    @Resource
    private SysDeptLeaderMapper sysDeptLeaderMapper;

    @Resource
    private SysUserDeptMapper sysUserDeptMapper;
    private static final String CODE_PRE = "ZZ_";

    private static final DateTimeFormatter CODE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    /**
     * 初始化系统配置,为了能动态获取数据,没用@PostConstruct注解
     */
    @Override
    public void init() {
        SysConfig role = sysConfigMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, CONFIG_KEY_DEFAULT_ROLE));
        if (role == null) {
            throw new ServiceException("未配置默认角色,请在系统设置中配置[" + CONFIG_KEY_DEFAULT_ROLE + "]");
        }
        defaultRole = role.getKeyValue();

        SysConfig password = sysConfigMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, CONFIG_KEY_DEFAULT_PASSWORD));
        if (password == null) {
            throw new ServiceException("未配置初始密码,请在系统设置中配置[" + CONFIG_KEY_DEFAULT_PASSWORD + "]");
        }
        defaultPassword = password.getKeyValue();

        SysConfig permGroup = sysConfigMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, CONFIG_KEY_DEFAULT_PERMGROUP));
        if (permGroup == null) {
            throw new ServiceException("未配置初始权限组,请在系统设置中配置[" + CONFIG_KEY_DEFAULT_PERMGROUP + "]");
        }
        defaultPermGroup = permGroup.getKeyValue();

        SysConfig syncModel = sysConfigMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, CONFIG_KEY_SYNCDINGTALK_MODEL));
        if (syncModel != null) {
            dingtalkSyncLoginName = syncModel.getKeyValue();
        }

        SysConfig syncPhone = sysConfigMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, CONFIG_KEY_SYNCDINGTALK_PHONE));
        if (syncPhone != null) {
            dingtalkSyncPhone = Boolean.parseBoolean(syncPhone.getKeyValue());
        }

        SysConfig onlyJobnumber = sysConfigMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, CONFIG_KEY_SYNCDINGTALK_ONLYJOBNUMBER));
        if (onlyJobnumber != null) {
            dingtalkOnlySyncJobNumber = Boolean.parseBoolean(onlyJobnumber.getKeyValue());
        }
        //重置版本号
        syncVersion = System.currentTimeMillis();

        //住房审核管理
        SysConfig propertyRole = sysConfigMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, CONFIG_KEY_DEFAULT_PROPERTY_ROLE));

        if (propertyRole != null) {
            defaultPropertyRole = propertyRole.getKeyValue();
        }
    }

    // 钉钉 用户 同步 入口
    @Override
    public void syncAll(OaTypeEnums oaType) {
        init();
        // 获取同步锁，防止用户多次点击同步数据
        RLock lock = redissonClient.getLock(LOCK_KEY_SYNC_DATA);
        if (lock.isLocked()) {
            throw new ServiceException("数据正在同步中，请稍后重试");
        }
        OaAuthScopeDTO authScope = oaOauth2Api.getAuthScope();

        syncDataTaskExecutor.execute(() -> {
            lock.lock();
            try {
                syncDeptByOaDeptIds(authScope.getOaDeptIds());
                syncUserByOaUserIds(oaType, authScope.getOaUserIds());
                syncUserByOaDeptIds(oaType, oaDeptMapper.selectAllOaDeptIdList(DING_TALK.name()));
                //将小于当前版本的数据删除
//                sysDeptLeaderMapper.deleteBatch(syncVersion);
            } catch (Throwable e) {
                log.error("同步钉钉用户发生异常", e);
                throw e;
            } finally {
                // 取消同步锁
                lock.unlock();
                log.info("同步钉钉用户完成");
            }
        });
    }

    /**
     * 区分OA全量同步和增量同步,该方法为全量同步
     *
     * @param oaDeptIds
     */
    private void syncDeptByOaDeptIds(List<String> oaDeptIds) {
        saveOrUpdateDept(oaDepartmentApi.getDeptListByIds(oaDeptIds));
    }

    /**
     * 区分OA全量同步和增量同步,该方法为增量同步
     *
     * @param oaType
     * @param oaDeptIds
     */
    @Override
    public void syncDeptByOaDeptIdsCallback(OaTypeEnums oaType, List<String> oaDeptIds) {
        saveOrUpdateDept(oaDepartmentApi.getDeptListByIds(oaDeptIds));
        //修改部门领导
        syncUserLeaderByOaDeptIds(oaDeptIds);
    }

    /**
     * 同步部门领导 新接口
     * 由于部门主管会随着部门的事件作为回调,所以在部门中更新一下部门领导表
     *
     * @param oaDeptIds
     */
    private void syncUserLeaderByOaDeptIds(List<String> oaDeptIds) {
        List<OaDept> oaDepts = oaDeptMapper.selectByOaDeptIds(oaDeptIds);

        for (OaDept oaDept : oaDepts) {
            long pageNum = 1;

            if (oaDept == null) {
                return;
            }

            //解除下属部门领导
            new LambdaUpdateChainWrapper<>(sysUserDeptMapper)
                    .eq(SysUserDept::getDeptId, oaDept.getDeptId())
                    .set(SysUserDept::getLeader, 0).update();

            List<OaUserDTO> oaUserDTOListAll = new ArrayList<>();
            while (true) {
                // 分页，测试人数不够可以调1页1条。 发布时调回来
                List<OaUserDTO> oaUserDTOList = oaUserApi.getUsersByOaDeptId(oaDept.getOaDeptId(), pageNum, 100l);
                if (CollectionUtils.isEmpty(oaUserDTOList)) {
                    break;
                }
                pageNum++;
                oaUserDTOListAll.addAll(oaUserDTOList);
            }

            // 钉钉用户id 和 系统用户id对应
            List<String> oaUserIdList = oaUserDTOListAll.stream().filter(x -> Boolean.TRUE.equals(x.getLeader())).map(OaUserDTO::getOaUserId).collect(Collectors.toList());
            List<OaUser> oaUsers = oaUserMapper.selectByOaUserIds(oaUserIdList);
            Map<String, Long> sysUserMap = oaUsers.stream().collect(Collectors.toMap(OaUser::getOaUserId, OaUser::getUserId));
            List<Long> collect = oaUserDTOListAll.stream().map(x -> sysUserMap.get(x.getOaUserId())).filter(Objects::nonNull).collect(Collectors.toList());
            new LambdaUpdateChainWrapper<>(sysUserDeptMapper)
                    .eq(SysUserDept::getDeptId, oaDept.getDeptId())
                    .in(SysUserDept::getUserId, collect)
                    .set(SysUserDept::getLeader, 1).update();
        }
    }

    /**
     * 同步部门领导
     * 由于部门主管会随着部门的事件作为回调,所以在部门中更新一下部门领导表
     *
     * @param oaDeptIds
     */
    private void syncUserLeaderByOaDeptIdsOld(List<String> oaDeptIds) {
        List<OaDept> oaDepts = oaDeptMapper.selectByOaDeptIds(oaDeptIds);

        for (OaDept oaDept : oaDepts) {
            long pageNum = 1;

            if (oaDept == null) {
                return;
            }

            //删除该部门下所有领导
            sysDeptLeaderMapper.deleteByDeptId(oaDept.getDeptId());

            while (true) {
                // 分页，测试人数不够可以调1页1条。 发布时调回来
                List<OaUserDTO> oaUserDTOList = oaUserApi.getUsersByOaDeptId(oaDept.getOaDeptId(), pageNum, 100l);
                if (CollectionUtils.isEmpty(oaUserDTOList)) {
                    break;
                }
                saveOrUpdateUserLeader(oaUserDTOList, oaDept.getDeptId());
                pageNum++;
            }
        }
    }

    private void saveOrUpdateUserLeader(List<OaUserDTO> oaUserDTOList, Long deptId) {
        if (CollectionUtils.isNotEmpty(oaUserDTOList)) {
            //过滤掉非leader的用户
            List<OaUserDTO> leaderDTOList = oaUserDTOList.stream().filter(oaUserDTO -> oaUserDTO.getLeader() != null && oaUserDTO.getLeader()).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(leaderDTOList)) {
                return;
            }

            //获取oauserIds
            List<String> oaUserIds = leaderDTOList.stream().map(OaUserDTO::getOaUserId).collect(Collectors.toList());
            //获取OAUSER信息
            List<OaUser> oaUserList = oaUserMapper.selectByOaUserIds(oaUserIds);

            oaUserList.stream().forEach(oaUser -> {
                this.insertLeader(oaUser.getUserId(), deptId);
            });
        }
    }


    @Override
    public void syncUserByOaDeptIds(OaTypeEnums oaType, List<String> oaDeptIds) {
        List<OaUserDTO> oaUserAllList = new ArrayList<>();
        for (String oaDeptId : oaDeptIds) {
            long pageNum = 1;
            while (true) {
                // 分页，测试人数不够可以调1页1条。 发布时调回来
                List<OaUserDTO> oaUserDTOList = oaUserApi.getUsersByOaDeptId(oaDeptId, pageNum, 100l);
                if (CollectionUtils.isEmpty(oaUserDTOList)) {
                    break;
                }
                oaUserAllList.addAll(oaUserDTOList);
                pageNum++;
            }
        }
        List<OaUserDTO> disposeUserMoreDeptList = moreDeptUser(oaUserAllList);
        saveOrUpdateUser(disposeUserMoreDeptList);
    }

    public List<OaUserDTO> moreDeptUser(List<OaUserDTO> list) {
        // 使用 HashMap 来存储合并后的用户信息
        Map<String, OaUserDTO> mergedUsers = new HashMap<>();

        for (OaUserDTO user : list) {
            String oaUserId = user.getOaUserId();
            if (mergedUsers.containsKey(oaUserId)) {
                // 如果用户已存在，合并部门Id和所管部门Id
                OaUserDTO existingUser = mergedUsers.get(oaUserId);
                existingUser.getOaDeptIds().add(Long.valueOf(user.getOaDeptId()));
                if (user.getLeader()) {
                    existingUser.getOaDeptLeaderIds().add(Long.valueOf(user.getOaDeptId()));
                }
            } else {
                // 如果用户不存在，初始化一下在添加
                user.getOaDeptIds().add(Long.valueOf(user.getOaDeptId()));
                if (Boolean.TRUE.equals(user.getLeader())) {
                    user.getOaDeptLeaderIds().add(Long.valueOf(user.getOaDeptId()));
                }
                mergedUsers.put(oaUserId, user);
            }
        }

        // 将 HashMap 转换回 List
        return new ArrayList<>(mergedUsers.values());
    }


    @Override
    public void leaveUserByOaUserIds(OaTypeEnums oaType, List<String> oaUserIds) {
        List<OaUser> oaUsers = oaUserMapper.selectByOaUserIds(oaUserIds);

        if (CollectionUtils.isEmpty(oaUsers)) {
            return;
        }

        List<Long> userIds = oaUsers.stream().map(OaUser::getUserId).collect(Collectors.toList());

        List<SysUser> sysUserList = sysUserMapper.selectList(new LambdaQueryWrapper<SysUser>()
                .in(SysUser::getUserId, userIds));

        //将用户状态禁用
        sysUserList.stream().forEach(u -> {
            u.setUserStatus(SysUserStatus.STATUS_LOCKED);
            sysUserMapper.updateById(u);
        });
    }

    // 钉钉 增量全量统一调用接口
    public void syncUserByOaUserIds(OaTypeEnums oaType, List<String> oaUserIds) {
        saveOrUpdateUser(oaUserApi.getUsersByOaUserIds(oaUserIds));
    }

    /**
     * 保存或者更新部门信息
     * 1.本地部门上下级是自联表
     * 2.没有做部门删除，后面需要可以进行调整
     *
     * @param oaDepartmentList
     */
    private void saveOrUpdateDept(List<OaDepartmentDTO> oaDepartmentList) {
        if (CollectionUtils.isEmpty(oaDepartmentList)) {
            return;
        }
        List<String> oaParentIds = Lists.newArrayList();
        for (OaDepartmentDTO oaDepartmentDTO : oaDepartmentList) {
            oaParentIds.add(oaDepartmentDTO.getOaDeptId());
            if (StringUtils.isNotBlank(oaDepartmentDTO.getOaParentId())) {
                oaParentIds.add(oaDepartmentDTO.getOaParentId());
            }
        }
        List<OaDept> oaDeptList = oaDeptMapper.selectByOaDeptIds(oaParentIds);
        Map<String, Long> oaDeptIdMap = oaDeptList.stream().collect(Collectors.toMap(OaDept::getOaDeptId, OaDept::getDeptId));
        List<OaDept> oaDeptAddList = Lists.newArrayList();
        List<SysDept> deptAddList = Lists.newArrayList();
        List<SysDept> deptUpdateList = Lists.newArrayList();
        for (OaDepartmentDTO oaDepartment : oaDepartmentList) {
            SysDept sysDept = new SysDept();
            sysDept.setDeptCode(CODE_PRE + oaDepartment.getOaDeptId());
            sysDept.setDeptName(oaDepartment.getName());
            sysDept.setParentId(oaDeptIdMap.getOrDefault(oaDepartment.getOaParentId(), 0L));
            sysDept.setDeletedFlag(1);
            sysDept.setShowOrder(0);
            sysDept.setCreateUserId(0L);
            sysDept.setUpdateUserId(0L);
            if (oaDeptIdMap.containsKey(oaDepartment.getOaDeptId())) {
                sysDept.setDeptId(oaDeptIdMap.get(oaDepartment.getOaDeptId()));
                deptUpdateList.add(sysDept);
            } else {
                deptAddList.add(sysDept);

                OaDept oaDept = new OaDept();
                oaDept.setOaDeptId(oaDepartment.getOaDeptId());
                oaDept.setOaType(DING_TALK.name());
                oaDeptAddList.add(oaDept);
            }
        }
        if (CollectionUtils.isNotEmpty(deptAddList)) {
            sysDeptMapper.insertList(deptAddList);
        }
        if (CollectionUtils.isNotEmpty(deptUpdateList)) {
            for (SysDept sysDept : deptUpdateList) {
                // 这里数据量多的话，考虑替换成批量操作；系统中添加了sharedJdbc，注意语法
                String deptPath = getDeptPath(sysDept);
                sysDept.setDeptPath(deptPath);
                sysDeptMapper.updateById(sysDept);
                RBucket<SysDept> rd = redissonClient.getBucket(DING_TALK.name() + "_DEPT:" + sysDept.getDeptId());
                rd.set(sysDept, 60, TimeUnit.MINUTES);
            }
        }
        for (int i = 0; i < deptAddList.size(); i++) {
            oaDeptAddList.get(i).setDeptId(deptAddList.get(i).getDeptId());
        }
        if (CollectionUtils.isNotEmpty(oaDeptAddList)) {
            oaDeptMapper.batchInsert(oaDeptAddList);
        }

        //更新部门path
        for (SysDept sysDept : deptAddList) {
            String deptPath = getDeptPath(sysDept);
            sysDept.setDeptPath(deptPath);
            sysDeptMapper.updateById(sysDept);
        }

        //将部门缓存到redis中
        for (SysDept sysDept : deptAddList) {
            RBucket<SysDept> rd = redissonClient.getBucket(DING_TALK.name() + "_DEPT:" + sysDept.getDeptId());
            rd.set(sysDept, 60, TimeUnit.MINUTES);
        }

        for (OaDepartmentDTO oaDepartmentDTO : oaDepartmentList) {
            saveOrUpdateDept(oaDepartmentApi.getDeptSubList(oaDepartmentDTO.getOaDeptId()));
        }
    }

    /**
     * 获取部门path
     *
     * @return
     */
    private String getDeptPath(SysDept sysDept) {
        if (sysDept.getParentId() != null && sysDept.getParentId().equals(0L)) {
            return sysDept.getDeptId().toString();
        }
        RBucket<SysDept> rd = redissonClient.getBucket(DING_TALK.name() + "_DEPT:" + sysDept.getParentId());
        if (rd.isExists()) {
            return rd.get().getDeptPath() + "/" + sysDept.getDeptId().toString();
        }
        return null;
    }


    /**
     * 保存或者更新用户信息/用户离职
     * 1、本地一个用户只有一个部门，所以在钉钉api中取了用户所在第一个部门，后面有需要可以进行调整
     * 2、新用户有默认未分配角色和初始化项目。  注意项目和角色如果删掉了，可能会导致新用户无法登录
     * 3、用户离职本地会将deleteFlag设为-1，如果用户在重新加入组织，会更新deleteFlag为1
     *
     * @param oaUserDTOList
     */
    @Transactional
    public void saveOrUpdateUser(List<OaUserDTO> oaUserDTOList) {
        if (CollectionUtils.isEmpty(oaUserDTOList)) {
            return;
        }

        // 钉钉部门id 和 系统部门id对应
        List<OaDept> oaDeptList = oaDeptMapper.selectAll();
        Map<String, Long> oaDeptIdMap = oaDeptList.stream().collect(Collectors.toMap(OaDept::getOaDeptId, OaDept::getDeptId));

        // 钉钉用户id 和 系统用户id对应
        List<String> oaUserIdList = oaUserDTOList.stream().map(OaUserDTO::getOaUserId).collect(Collectors.toList());
        List<OaUser> oaUsers = oaUserMapper.selectByOaUserIds(oaUserIdList);
        Map<String, OaUser> sysUserMap = oaUsers.stream().collect(Collectors.toMap(OaUser::getOaUserId, OaUser -> OaUser));
//        Map<Long, Long> collectDb = new HashMap<>();
        List<String> userdeptList = new ArrayList<>();
        // 根据接收的用户id 删除相关联部门用户表数据
        if (CollectionUtils.isNotEmpty(oaUsers)) {
            List<Long> collect = oaUsers.stream().map(OaUser::getUserId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                new LambdaUpdateChainWrapper<>(sysUserDeptMapper).in(SysUserDept::getUserId, collect).lt(SysUserDept::getSyncVersion, syncVersion).remove();
            }
            // 会存在自己配置的部门，找出来
            List<SysUserDept> list = new LambdaQueryChainWrapper<>(sysUserDeptMapper).in(SysUserDept::getUserId, collect).list();
            if (CollectionUtils.isNotEmpty(list)) {
                userdeptList = list.stream().map(x -> x.getDeptId() + ":" + x.getUserId()).collect(Collectors.toList());
//                collectDb = list.stream().collect(Collectors.toMap(x -> x.getDeptId(), x -> x.getUserId()));
            }

        }

        for (OaUserDTO oaUserDTO : oaUserDTOList) {
            OaUser qUser = sysUserMap.get(oaUserDTO.getOaUserId());

            // 剔除无工号用户
            if (dingtalkOnlySyncJobNumber) {
                if (StringUtils.isBlank(oaUserDTO.getJobNumber())) {
                    removeUser(qUser);
                    continue;
                }
            }

            if (qUser != null) {
                SysUser sysUser = new SysUser();
                sysUser.setUserId(qUser.getUserId());
                String loginName = PHONE.equalsIgnoreCase(dingtalkSyncLoginName) ? oaUserDTO.getMobile() : oaUserDTO.getJobNumber();
                sysUser.setLoginName(StringUtils.isBlank(loginName) ? oaUserDTO.getMobile() : loginName);
                if(StringUtil.isBlank(sysUser.getLoginName())){
                    continue;
                }
                sysUser.setShowName(oaUserDTO.getName());
                sysUser.setPhone(dingtalkSyncPhone ? oaUserDTO.getMobile() : null);
                sysUser.setUserType(SysUserType.TYPE_SYSTEM);
                sysUser.setUserStatus(SysUserStatus.STATUS_NORMAL);
                sysUser.setDeletedFlag(oaUserDTO.getIsLeaved() ? -1 : 1);
                sysUser.setUserTags("钉钉同步");
                sysUser.setCreateUserId(0L);
                sysUser.setUpdateUserId(0L);
                sysUser.setUpdateTime(new Date());
                sysUserMapper.updateUser(sysUser);

                List<Long> oaDeptIds = oaUserDTO.getOaDeptIds();
                if (CollectionUtils.isNotEmpty(oaDeptIds)) {
                    for (Long thirdDeptId : oaDeptIds) {
                        if (userdeptList.contains(oaDeptIdMap.get(thirdDeptId.toString()) + ":" + sysUser.getUserId())) continue;
                        SysUserDept userDept = createUserDept(oaDeptIdMap, oaUserDTO, sysUser, thirdDeptId);
                        sysUserDeptMapper.insert(userDept);
                    }
                }
            } else {
                //离职员工不处理
                if (!oaUserDTO.getIsLeaved()) {
                    SysUser sysUser = new SysUser();
                    String loginName = PHONE.equalsIgnoreCase(dingtalkSyncLoginName) ? oaUserDTO.getMobile() : oaUserDTO.getJobNumber();
                    if (StringUtils.isBlank(loginName)) {
                        loginName = oaUserDTO.getMobile();
                    }
                    sysUser.setLoginName(loginName);
                    sysUser.setPassword(passwordEncoder.encode(defaultPassword));
                    sysUser.setShowName(oaUserDTO.getName());
                    sysUser.setPhone(dingtalkSyncPhone ? oaUserDTO.getMobile() : null);
//                    sysUser.setDeptId(oaDeptIdMap.get(oaUserDTO.getOaDeptId()));
                    sysUser.setUserType(SysUserType.TYPE_SYSTEM);
                    sysUser.setUserStatus(SysUserStatus.STATUS_NORMAL);
                    sysUser.setDeletedFlag(1);
                    sysUser.setUserTags("钉钉同步");
                    sysUser.setCreateUserId(0L);
                    sysUser.setUpdateUserId(0L);
                    sysUserMapper.insert(sysUser);//插入用户

                    SysUserRole userRole = new SysUserRole();
                    userRole.setRoleId(Long.valueOf(defaultRole));
                    userRole.setUserId(sysUser.getUserId());
                    sysUserRoleMapper.insert(userRole);//用户角色

                    OaUser oaUser = new OaUser();
                    oaUser.setUserId(sysUser.getUserId());
                    oaUser.setOaUserId(oaUserDTO.getOaUserId());
                    oaUser.setOaType(DING_TALK.name());
                    oaUserMapper.insert(oaUser);

                    //新增默认权限组
                    sysPermCodeMapper.insertUserPermGroup(oaUser.getUserId(), defaultPermGroup);


                    List<Long> oaDeptIds = oaUserDTO.getOaDeptIds();
                    if (CollectionUtils.isNotEmpty(oaDeptIds)) {
                        for (Long thirdDeptId : oaDeptIds) {
                            if (userdeptList.contains(oaDeptIdMap.get(thirdDeptId.toString()) + ":" + sysUser.getUserId())) continue;
                            SysUserDept userDept = createUserDept(oaDeptIdMap, oaUserDTO, sysUser, thirdDeptId);
                            sysUserDeptMapper.insert(userDept);
                        }
                    }
                }
            }
        }

    }

    private SysUserDept createUserDept(Map<String, Long> oaDeptIdMap, OaUserDTO oaUserDTO, SysUser sysUser, Long thirdDeptId) {
        int isLeader = 0;
        List<Long> oaDeptLeaderIds = oaUserDTO.getOaDeptLeaderIds();
        if (CollectionUtils.isNotEmpty(oaDeptLeaderIds) && oaDeptLeaderIds.contains(thirdDeptId)) {
            isLeader = 1;
        }
        return new SysUserDept(sysUser.getUserId(), oaDeptIdMap.get(thirdDeptId.toString()), isLeader, syncVersion);
    }


    private void insertLeader(Long userId, Long deptId) {
        SysDeptLeader leader = new SysDeptLeader();
        leader.setUserId(userId);
        leader.setDeptId(deptId);
        leader.setSyncVersion(syncVersion);
        sysDeptLeaderMapper.insert(leader);
        updatePropertyRole(userId);
    }

    /**
     * 更新房产用户角色
     */
    private void updatePropertyRole(Long userId) {
        //查询用户是否有权限,没有则新增
        List<SysUserRole> sysUserRoles = sysUserRoleMapper.selectList(Wrappers.lambdaQuery(SysUserRole.class)
                .eq(SysUserRole::getUserId, userId)
                .eq(SysUserRole::getRoleId, Long.valueOf(defaultPropertyRole))
        );

        if (CollectionUtils.isEmpty(sysUserRoles)) {
            SysUserRole userRole = new SysUserRole();
            userRole.setRoleId(Long.valueOf(defaultPropertyRole));
            userRole.setUserId(userId);
            sysUserRoleMapper.insert(userRole);//用户角色
        }
    }

    /**
     * 删除用户
     *
     * @param qUser
     */
    private void removeUser(OaUser qUser) {
        if (qUser != null) {
            SysUser sysUser = sysUserMapper.selectById(qUser.getUserId());
            if (sysUser != null) {
                sysUser.setDeletedFlag(-1);
                sysUser.setUserTags("钉钉同步");
                sysUser.setCreateUserId(0L);
                sysUser.setUpdateUserId(0L);
                sysUser.setUpdateTime(new Date());
                sysUserMapper.updateUser(sysUser);
            }
        }
    }
}
