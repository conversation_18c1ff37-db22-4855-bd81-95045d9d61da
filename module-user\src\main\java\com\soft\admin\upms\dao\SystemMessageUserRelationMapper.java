package com.soft.admin.upms.dao;

import com.soft.common.core.base.dao.BaseDaoMapper;
import com.soft.admin.upms.model.SystemMessageUserRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统消息与用户关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-06-26
 */
public interface SystemMessageUserRelationMapper extends BaseDaoMapper<SystemMessageUserRelation> {

    /**
     * 系统消息批量已读
     * @param userId
     * @param idList
     * @return
     */
    int batchRead(@Param("userId") Long userId, @Param("idList") List<Long> idList);

    /**
     * 批量新增
     * @param list
     * @return
     */
    int batchInsert(@Param("list") List<SystemMessageUserRelation> list);

    /**
     * 根据系统消息id删除
     * @param userId
     * @param messageId
     */
    void deleteByMessageId( @Param("userId") Long userId, @Param("messageId") Long messageId);
}
