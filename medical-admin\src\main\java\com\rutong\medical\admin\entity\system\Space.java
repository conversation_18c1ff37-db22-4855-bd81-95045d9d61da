package com.rutong.medical.admin.entity.system;

import io.swagger.annotations.ApiModelProperty;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rutong.medical.admin.vo.system.SpaceVO;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.common.core.base.model.BaseModel;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 空间对象 sp_space
 *
 * <AUTHOR>
 * @date 2023-03-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sp_space")
public class Space extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 空间完整名称 */
    @TableId(value = "id")
    private Long id;

    /** 上级id */
    private Long parentId;

    /** 空间路径（使用/分割id） */
    private String path;

    /** 编码 */
    private String code;

    /** 空间类型（AREA区域，BUILDING楼栋，FLOOR楼层，POINT点位） */
    private String type;

    /** 空间名称 */
    private String name;

    /** 空间完整名称（使用/分割名称） */
    private String fullName;

    /** 坐标 */
    private String coordinate;
    /**
     * 是否公共区域 0否1是
     */
    private String commonArea;
    /**
     * 排序
     */
    private Long sort;

    /** 删除（0否，1是） */
    @TableLogic(value = "1", delval = "-1")
    private Integer deleted;


    /**
     * 二维模型文件地址
     */
    private String modelTwoDimensional;
    /**
     * 三维模型文件地址
     */
    private String modelThreeDimensional;

    @Mapper
    public interface SpaceModelMapper extends BaseModelMapper<SpaceVO, Space> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        Space toModel(SpaceVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        SpaceVO fromModel(Space entity);
    }

    public static final SpaceModelMapper INSTANCE = Mappers.getMapper(SpaceModelMapper.class);
}
