package com.rutong.medical.admin.listener;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.rutong.medical.admin.constant.DeviceTypeEnum;
import com.rutong.medical.admin.dto.station.BaseStationDataDTO;
import com.rutong.medical.admin.service.location.UserLocationService;
import com.soft.common.mqtt.MQTTTopicMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @ClassName CardListener
 * @Description 工卡主题监听
 * <AUTHOR>
 * @Date 2025/7/14 16:49
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Component
@Slf4j
public class CardListener implements MQTTTopicMessageListener {

    // 监听的主题名称
    private static final String LISTENER_TOPIC_NAME = "/baseStation/location/+/" + DeviceTypeEnum.CARD_LORA.getCode() + "/data";

    private static final Integer STATUS_UN_HANDLE = 0;

    @Autowired
    private UserLocationService userLocationService;

    private final ExecutorService mqttMessageExecutor = Executors.newFixedThreadPool(1);

    @Override
    public String getTopic() {
        return LISTENER_TOPIC_NAME;
    }

    @Override
    public Integer getQos() {
        return null;
    }

    @Override
    public void messageArrived(String topic, MqttMessage mqttMessage) {
        mqttMessageExecutor.submit(() -> {
            try {
                String payload = new String(mqttMessage.getPayload());
                System.out.println(payload);
                processPayload(payload);
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        });
    }


    public void processPayload(String payload) {
        if (StringUtils.isBlank(payload)) {
            return;
        }
        try {
            JSON json = JSONUtil.parse(payload);
            BaseStationDataDTO baseStationData = json.toBean(BaseStationDataDTO.class);
            userLocationService.save(baseStationData);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @PreDestroy
    public void destroy() {
        mqttMessageExecutor.shutdown();
    }
}
