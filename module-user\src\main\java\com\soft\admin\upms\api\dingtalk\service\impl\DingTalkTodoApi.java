package com.soft.admin.upms.api.dingtalk.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.aliyun.core.utils.EncodeUtil;
import com.aliyun.dingtalktodo_1_0.models.*;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.soft.admin.upms.api.dingtalk.DingTalkConfig;
import com.soft.admin.upms.api.dingtalk.service.IOaTodoApi;
import com.soft.admin.upms.api.dingtalk.service.IOaUserApi;
import com.soft.admin.upms.api.dingtalk.utils.DingUtil;
import com.soft.admin.upms.dao.DingTalkTodoMapper;
import com.soft.admin.upms.dto.dintalk.OaRemindTodoDTO;
import com.soft.admin.upms.model.DingTalkTodo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description 钉钉用户Api
 * @Date 0009, 2023年8月9日 14:20
 * <AUTHOR>
 **/
@Slf4j
@Service
public class DingTalkTodoApi implements IOaTodoApi {
    @Resource
    private DingTalkOauth2Api dingTalkOauth2Api;
    @Resource
    private IOaUserApi oaUserApi;
    @Resource
    private DingTalkTodoMapper dingTalkTodoMapper;

    @Resource
    private DingTalkConfig dingTalkConfig;

    // 钉钉统一跳转协议
    public static final String path = "dingtalk://dingtalkclient/action/open_platform_link?mobileLink=";

    public com.aliyun.dingtalktodo_1_0.Client createClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        if (StringUtils.isNotBlank(dingTalkConfig.getServerApiUrl())) {
            config.protocol = "http";
            config.endpoint = dingTalkConfig.getServerApiUrl();
        }
        return new com.aliyun.dingtalktodo_1_0.Client(config);
    }

//    @Override
//    public void createTodo(OaRemindTodoDTO oaRemindTodoDTO) {
//        String unionId = oaUserApi.getUserByOaUserId(oaRemindTodoDTO.getExecutorId()).getUnionId();
//        String creatorId = null;
//        if (Objects.nonNull(oaRemindTodoDTO.getCreatorId())) {
//            OaUserDTO oaUserDTO = oaUserApi.getUserByOaUserId(oaRemindTodoDTO.getCreatorId());
//            creatorId = oaUserDTO.getUnionId();
//        }
//
//        CreateTodoTaskHeaders createTodoTaskHeaders = new CreateTodoTaskHeaders();
//        createTodoTaskHeaders.xAcsDingtalkAccessToken = dingTalkOauth2Api.getAccessToken();
//        CreateTodoTaskRequest.CreateTodoTaskRequestNotifyConfigs notifyConfigs = new CreateTodoTaskRequest.CreateTodoTaskRequestNotifyConfigs()
//                .setDingNotify("1");
//        CreateTodoTaskRequest.CreateTodoTaskRequestDetailUrl detailUrl = null;
//        try {
//            String url = dingTalkConfig.getAppUrl() + oaRemindTodoDTO.getUrl();
//            detailUrl = new CreateTodoTaskRequest.CreateTodoTaskRequestDetailUrl()
//                    // app内打开的链接
//                    .setAppUrl(path + EncodeUtil.encode(url))
//                    // pc端打开的链接
//                    .setPcUrl(url);
//        } catch (Exception e) {
//            log.error(e.getMessage(),e);
//        }
//        CreateTodoTaskRequest createTodoTaskRequest = new CreateTodoTaskRequest()
//                // 待办标题
//                .setSubject(oaRemindTodoDTO.getTitle())
//                // 待办备注描述
//                .setDescription(oaRemindTodoDTO.getDescription())
//                // 截止时间
////                .setDueTime(DateUtil.endOfDay(DateUtil.date()).getTime())
//                .setDueTime(new Date().getTime() + 1000 * 10)
//                // 创建者
//                .setCreatorId(creatorId)
//                // 执行者
//                .setExecutorIds(Collections.singletonList(unionId))
//                // 详情页跳转地址
//                .setDetailUrl(detailUrl)
//                // 生成的待办是否仅展示在执行者的待办列表中
//                .setIsOnlyShowExecutor(true)
//                // 优先级，普通
//                .setPriority(20)
//                // 待办通知配置
//                      .setNotifyConfigs(notifyConfigs);
//        try {
//            log.info("ding talk api createTodo request: {}", JSON.toJSONString(createTodoTaskRequest));
//            com.aliyun.dingtalktodo_1_0.Client client = createClient();
//            CreateTodoTaskResponse rsp = client.createTodoTaskWithOptions(creatorId, createTodoTaskRequest, createTodoTaskHeaders, new RuntimeOptions());
//            if (rsp != null) {
//                log.info("ding talk api createTodo result: {}", JSON.toJSON(rsp.getBody()));
//                if (rsp.getBody() != null) {
//                    DingTalkTodo dingTalkTodo = new DingTalkTodo();
//                    dingTalkTodo.setMessageId(oaRemindTodoDTO.getMessageId());
//                    dingTalkTodo.setBusiId(oaRemindTodoDTO.getBusiId());
//                    dingTalkTodo.setTodoId(rsp.getBody().getId());
//                    dingTalkTodo.setUserUnionId(unionId);
//                    dingTalkTodo.setBusiType(oaRemindTodoDTO.getBusiType());
//                    dingTalkTodo.setCreateTime(new Date());
//                    dingTalkTodo.setSendUserId(oaRemindTodoDTO.getSendUserId());
//                    dingTalkTodo.setReceiveUserId(oaRemindTodoDTO.getReceiveUserId());
//                    dingTalkTodo.setTitle(oaRemindTodoDTO.getTitle());
//                    dingTalkTodoMapper.insert(dingTalkTodo);
//                }
//            }
//        } catch (Exception err) {
//            log.error("ding talk api createTodo error", err.getMessage(),err);
//        }
//    }

    @Override
    @SneakyThrows
    public void createTodo(OaRemindTodoDTO oaRemindTodoDTO) {
        String unionId = oaUserApi.getUserByOaUserId(oaRemindTodoDTO.getExecutorId()).getUnionId(); //gaFEuZfYepM7PHd4iSXgQagiEiE
        String unionSendId = oaUserApi.getUserByOaUserId(oaRemindTodoDTO.getCreatorId()).getUnionId(); // WPKcswJeys53Zl5epMGrwAiEiE

        // 调用钉钉待办接口
        String url = "".concat(dingTalkConfig.getAppUrl()).concat(oaRemindTodoDTO.getUrl());
        String rsp = DingUtil.sendTodo(StringUtils.isEmpty(unionSendId) ? unionId : unionSendId, unionId, oaRemindTodoDTO.getTitle(),
                oaRemindTodoDTO.getDescription(), url, path + EncodeUtil.encode(url));

        // 原来的逻辑
        try {
            log.info("ding talk api createTodo request: {}", JSON.toJSONString(rsp));
            if (rsp != null) {
                DingTalkTodo dingTalkTodo = new DingTalkTodo();
                dingTalkTodo.setMessageId(oaRemindTodoDTO.getMessageId());
                dingTalkTodo.setBusiId(oaRemindTodoDTO.getBusiId());
                dingTalkTodo.setTodoId(new JSONObject(rsp).getStr("id"));
                dingTalkTodo.setUserUnionId(unionId);
                dingTalkTodo.setBusiType(oaRemindTodoDTO.getBusiType());
                dingTalkTodo.setCreateTime(new Date());
                dingTalkTodo.setSendUserId(oaRemindTodoDTO.getSendUserId());
                dingTalkTodo.setReceiveUserId(oaRemindTodoDTO.getReceiveUserId());
                dingTalkTodo.setTitle(oaRemindTodoDTO.getTitle());
                dingTalkTodoMapper.insert(dingTalkTodo);
            }
        } catch (Exception err) {
            log.error("ding talk api createTodo error", err.getMessage(), err);
        }
    }


    public void updateTodoReceiver(Long busiId, String busiType, List<String> titles, Long receiverIds) {
        List<DingTalkTodo> dingTalkTodoList = dingTalkTodoMapper.selectList(Wrappers.lambdaQuery(DingTalkTodo.class)
                .eq(DingTalkTodo::getBusiId, busiId)
                .eq(DingTalkTodo::getBusiType, busiType)
                .in(CollectionUtil.isNotEmpty(titles), DingTalkTodo::getTitle, titles)
                .in(receiverIds != null, DingTalkTodo::getReceiveUserId, receiverIds)
                .orderByDesc(DingTalkTodo::getCreateTime));
        if (CollUtil.isNotEmpty(dingTalkTodoList)) {
            try {
                com.aliyun.dingtalktodo_1_0.Client client = createClient();
                dingTalkTodoList.forEach(dingTalkTodo -> {
                    UpdateTodoTaskHeaders updateTodoTaskHeaders = new UpdateTodoTaskHeaders();
                    updateTodoTaskHeaders.xAcsDingtalkAccessToken = dingTalkOauth2Api.getAccessToken();
                    UpdateTodoTaskRequest updateTodoTaskRequest = new UpdateTodoTaskRequest();
                    updateTodoTaskRequest.setDone(true);
                    UpdateTodoTaskResponse rsp = null;
                    try {
                        rsp = client.updateTodoTaskWithOptions(dingTalkTodo.getUserUnionId(), dingTalkTodo.getTodoId(),
                                updateTodoTaskRequest, updateTodoTaskHeaders, new RuntimeOptions());
                        if (rsp != null) {
                            log.info("ding talk api updateTask result: {}", JSON.toJSON(rsp.getBody()));
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                });
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        // com.aliyun.dingtalktodo_1_0.Client client = null;
        // try {
        //     client = createClient();
        // } catch (Exception e) {
        //     e.printStackTrace();
        // }
        // UpdateTodoTaskHeaders updateTodoTaskHeaders = new UpdateTodoTaskHeaders();
        // updateTodoTaskHeaders.xAcsDingtalkAccessToken = dingTalkOauth2Api.getAccessToken();
        // UpdateTodoTaskRequest updateTodoTaskRequest = new UpdateTodoTaskRequest();
        // updateTodoTaskRequest.setDone(true);
        // UpdateTodoTaskResponse rsp = null;
        // try {
        //     rsp = client.updateTodoTaskWithOptions(dingTalkTodo.getUserUnionId(), dingTalkTodo.getTodoId(),
        //             updateTodoTaskRequest, updateTodoTaskHeaders, new RuntimeOptions());
        //     if (rsp != null) {
        //         log.info("ding talk api updateTask result: {}", JSON.toJSON(rsp.getBody()));
        //     }
        // } catch (Exception err) {
        //     log.error("ding talk api createTodo error", err.getMessage(),err);
        // }
    }

    @Override
    public void updateTodo(Long busiId, String busiType, List<String> titles, Long receiverIds) {
        List<DingTalkTodo> dingTalkTodoList = dingTalkTodoMapper.selectList(Wrappers.lambdaQuery(DingTalkTodo.class)
                .eq(DingTalkTodo::getBusiId, busiId)
                .eq(DingTalkTodo::getBusiType, busiType)
                .in(CollectionUtil.isNotEmpty(titles), DingTalkTodo::getTitle, titles)
//                .in(receiverIds != null, DingTalkTodo::getReceiveUserId, receiverIds) 加上之后导致只能处理当前的接收人，目前需要处理全部接收人
                .orderByDesc(DingTalkTodo::getCreateTime));
        if (CollUtil.isNotEmpty(dingTalkTodoList)) {
            try {
                com.aliyun.dingtalktodo_1_0.Client client = createClient();
                dingTalkTodoList.forEach(dingTalkTodo -> {
                    UpdateTodoTaskHeaders updateTodoTaskHeaders = new UpdateTodoTaskHeaders();
                    updateTodoTaskHeaders.xAcsDingtalkAccessToken = dingTalkOauth2Api.getAccessToken();
                    UpdateTodoTaskRequest updateTodoTaskRequest = new UpdateTodoTaskRequest();
                    updateTodoTaskRequest.setDone(true);
                    UpdateTodoTaskResponse rsp = null;
                    try {
                        rsp = client.updateTodoTaskWithOptions(dingTalkTodo.getUserUnionId(), dingTalkTodo.getTodoId(),
                                updateTodoTaskRequest, updateTodoTaskHeaders, new RuntimeOptions());
                        if (rsp != null) {
                            log.info("ding talk api updateTask result: {}", JSON.toJSON(rsp.getBody()));
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                });
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        // com.aliyun.dingtalktodo_1_0.Client client = null;
        // try {
        //     client = createClient();
        // } catch (Exception e) {
        //     e.printStackTrace();
        // }
        // UpdateTodoTaskHeaders updateTodoTaskHeaders = new UpdateTodoTaskHeaders();
        // updateTodoTaskHeaders.xAcsDingtalkAccessToken = dingTalkOauth2Api.getAccessToken();
        // UpdateTodoTaskRequest updateTodoTaskRequest = new UpdateTodoTaskRequest();
        // updateTodoTaskRequest.setDone(true);
        // UpdateTodoTaskResponse rsp = null;
        // try {
        //     rsp = client.updateTodoTaskWithOptions(dingTalkTodo.getUserUnionId(), dingTalkTodo.getTodoId(),
        //             updateTodoTaskRequest, updateTodoTaskHeaders, new RuntimeOptions());
        //     if (rsp != null) {
        //         log.info("ding talk api updateTask result: {}", JSON.toJSON(rsp.getBody()));
        //     }
        // } catch (Exception err) {
        //     log.error("ding talk api createTodo error", err.getMessage(),err);
        // }
    }

    @Override
    public void queryTodo(String unionId) {
        com.aliyun.dingtalktodo_1_0.Client client = null;
        try {
            client = createClient();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        QueryOrgTodoTasksHeaders queryOrgTodoTasksHeaders = new QueryOrgTodoTasksHeaders();
        queryOrgTodoTasksHeaders.xAcsDingtalkAccessToken = dingTalkOauth2Api.getAccessToken();
        QueryOrgTodoTasksRequest queryOrgTodoTasksRequest = new QueryOrgTodoTasksRequest()
                .setNextToken("0")
                .setIsDone(true);
        QueryOrgTodoTasksResponse rsp = null;
        try {
            rsp = client.queryOrgTodoTasksWithOptions(unionId, queryOrgTodoTasksRequest, queryOrgTodoTasksHeaders, new RuntimeOptions());
            if (rsp != null) {
                log.info("ding talk api queryTodo result: {}", JSON.toJSON(rsp.getBody()));
            }
        } catch (Exception err) {
            log.error("ding talk api createTodo error", err.getMessage(), err);
        }
    }


}
