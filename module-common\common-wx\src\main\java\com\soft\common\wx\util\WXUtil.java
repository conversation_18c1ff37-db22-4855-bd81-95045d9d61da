package com.soft.common.wx.util;

import com.alibaba.fastjson.JSON;
import com.soft.common.core.http.HttpClientUtil;
import com.soft.common.wx.config.WXProperties;
import com.soft.common.wx.entity.AccessToken;
import com.soft.common.wx.entity.JsAPISignature;
import com.soft.common.wx.entity.UserAccessToken;
import com.soft.common.wx.entity.WXTicket;
import com.soft.common.wx.exception.AesException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import static com.soft.common.wx.constent.WXConstants.*;

/**
 * 专有钉钉部门信息获取
 *
 * <AUTHOR>
 * @date 2023-09-12
 */
@Slf4j
@Component
public class WXUtil {
    @Resource
    private WXProperties wxProperties;


    /**
     * 获取网页授权凭证
//     * @param appId 公众账号的唯一标识
//     * @param appSecret 公众账号的密钥
     * @param code
     * @return WeixinAouth2Token
     */
    public UserAccessToken getOauth2AccessToken(String code) throws URISyntaxException {
        log.debug("get weixin user access token with parameters - {}", code);
        List<BasicNameValuePair> list = new ArrayList<>();
        list.add(new BasicNameValuePair("appid", wxProperties.getAppId()));
        list.add(new BasicNameValuePair("secret", wxProperties.getAppSecret()));
        list.add(new BasicNameValuePair("code",code));
        list.add(new BasicNameValuePair("grant_type", "authorization_code"));
        String response = HttpClientUtil.httpSyncGet(WX_USER_ACCESS_TOKEN,list);
        return JSON.parseObject(response,UserAccessToken.class);
    }

    /**
     * 获取token access_token的有效期目前为2个小时，需定时刷新，重复获取将导致上次获取的access_token失效。
     * @return
     */
    public AccessToken getAccessToken() {
        List<BasicNameValuePair> list = new ArrayList<>();
        list.add(new BasicNameValuePair("appid", wxProperties.getAppId()));
        list.add(new BasicNameValuePair("secret", wxProperties.getAppSecret()));
        list.add(new BasicNameValuePair("grant_type","client_credential"));
        String response = HttpClientUtil.httpSyncGet(WX_ACCESS_TOKEN,list);
        return JSON.parseObject(response,AccessToken.class);
    }

    /**
     * 获取jsapi_ticket 有效期7200秒，开发者必须在自己的服务全局缓存jsapi_ticket
     * @return
     */
    public WXTicket getJsapiTicket(String accessToken) {
        List<BasicNameValuePair> list = new ArrayList<>();
        list.add(new BasicNameValuePair("access_token", accessToken));
        list.add(new BasicNameValuePair("type", "jsapi"));
        String response = HttpClientUtil.httpSyncGet(WX_JSAPI_TICKET,list);
        return JSON.parseObject(response, WXTicket.class);
    }

    public JsAPISignature getSignature(String url,WXTicket ticket) throws AesException {
        long timestamp = System.currentTimeMillis() / 1000;
        String nonce = this.getRandomStringByLength(16);
        String signature = SHA1Util.getSHA1("jsapi_ticket=" + ticket.getTicket() + "&noncestr=" + nonce + "&timestamp=" + timestamp + "&url=" + url);
        JsAPISignature jsAPISignature = new JsAPISignature();
        jsAPISignature.setAppId(wxProperties.getAppId());
        jsAPISignature.setNonce(nonce);
        jsAPISignature.setTimestamp(timestamp);
        jsAPISignature.setSignature(signature);
        jsAPISignature.setUrl(url);
        return jsAPISignature;
    }


    /**
     * 获取一定长度的随机字符串
     * @param length 指定字符串长度
     * @return 一定长度的字符串
     */
    public String getRandomStringByLength(int length) {
        String base = "abcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }
        return sb.toString();
    }
}
