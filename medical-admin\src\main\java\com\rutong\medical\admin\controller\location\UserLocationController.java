package com.rutong.medical.admin.controller.location;

import com.rutong.medical.admin.service.location.UserLocationService;
import com.soft.common.core.constant.ErrorCodeEnum;
import com.soft.common.core.object.ResponseResult;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName UserLocationController
 * @Description 人员定位
 * <AUTHOR>
 * @Date 2025/7/22 13:49
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@RestController
@RequestMapping("/location/")
@Api(tags = "人员定位")
@AllArgsConstructor
public class UserLocationController {

    private UserLocationService userLocationService;

    /**
     * 单个用户订阅
     * @param userId
     * @return
     */
    @GetMapping("subscriptionSingle")
    public ResponseResult<Void> subscriptionSingle(@RequestParam(required = true) Long userId) {
        return userLocationService.subscriptionSingle(userId) == true ? ResponseResult.success()
                : ResponseResult.error(ErrorCodeEnum.ARGUMENT_PK_ID_NULL);
    }


}
