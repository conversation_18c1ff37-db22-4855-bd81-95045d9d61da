package com.soft.admin.upms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.admin.upms.vo.NoticeUserRelationVO;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.common.core.base.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 公告通知与用户关联对象 sp_notice_user_relation
 *
 * <AUTHOR>
 * @date 2023-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sp_notice_user_relation")
public class NoticeUserRelation extends BaseModel {

    /** 用户id */
    private Long userId;

    /** 公告通知id */
    private Long noticeId;


    @Mapper
    public interface NoticeUserRelationModelMapper extends BaseModelMapper<NoticeUserRelationVO, NoticeUserRelation> {
        /**
        * 转换Vo对象到实体对象。
        *
        * @param entityVo 域对象。
        * @return 实体对象。
        */
        @Override
        NoticeUserRelation toModel(NoticeUserRelationVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        NoticeUserRelationVO fromModel(NoticeUserRelation entity);
    }

    public static final NoticeUserRelationModelMapper INSTANCE = Mappers.getMapper(NoticeUserRelationModelMapper.class);
}
