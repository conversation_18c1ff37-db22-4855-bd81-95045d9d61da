package com.soft.admin.upms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * ThreePartyRobotDTO对象
 *
 * <AUTHOR>
 * @date 2023-05-23
 */
@ApiModel("ThreePartyRobotDTO对象")
@Data
public class ThreePartyRobotDTO {

    @ApiModelProperty(value = "id")
    @NotNull(message = "数据验证失败，id不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "平台（QW企业微信，DINGDING钉钉）")
    private String platform;

    @ApiModelProperty(value = "机器人名称")
    private String name;


    @ApiModelProperty(value = "类型")
    private String type;


    @ApiModelProperty(value = "webhook地址")
    private String webHook;


    @ApiModelProperty(value = "密钥")
    private String secret;


    @ApiModelProperty(value = "备注")
    private String remark;


    @ApiModelProperty(value = "创建时间", hidden = true)
    private Date createTime;


    @ApiModelProperty(value = "创建者id", hidden = true)
    private Long createUserId;


    @ApiModelProperty(value = "更新时间", hidden = true)
    private Date updateTime;


    @ApiModelProperty(value = "更新者id", hidden = true)
    private Long updateUserId;

}
