package com.soft.admin.upms.controller;

import com.github.pagehelper.PageHelper;
import com.soft.admin.upms.dto.SysConfigDTO;
import com.soft.admin.upms.model.SysConfig;
import com.soft.admin.upms.service.SysConfigService;
import com.soft.admin.upms.vo.SysConfigVO;
import com.soft.common.core.constant.ErrorCodeEnum;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.common.core.util.MyCommonUtil;
import com.soft.common.core.util.MyModelUtil;
import com.soft.common.core.util.MyPageUtil;
import com.soft.common.log.annotation.OperationLog;
import com.soft.common.log.model.constant.SysOperationLogType;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "系统配置")
@RestController
@RequestMapping("/core/sysConfig")
public class SysConfigController {

    @Autowired
    private SysConfigService sysConfigService;

    /**
     * 查询系统配置
     *
     * @param sysConfigDTO
     * @return
     */
    @GetMapping("/list")
    public ResponseResult<MyPageData<SysConfigVO>> list(SysConfigDTO sysConfigDTO) {
        if (sysConfigDTO.getPageNum() != null && sysConfigDTO.getPageSize() != null) {
            PageHelper.startPage(sysConfigDTO.getPageNum(), sysConfigDTO.getPageSize());
        }
        SysConfig sysConfig = MyModelUtil.copyTo(sysConfigDTO, SysConfig.class);
        List<SysConfig> list = sysConfigService.list(sysConfig);
        return ResponseResult.success(MyPageUtil.makeResponseData(list, SysConfig.INSTANCE));
    }

    @GetMapping("/twoKey")
    public ResponseResult<String> twoKey() {
        return ResponseResult.success(sysConfigService.getValue("twoKey"));
    }



    @GetMapping("/getValue/{configKey}")
    public ResponseResult<String> getValue(@PathVariable("configKey") String configKey) {
       return ResponseResult.success(sysConfigService.getValue(configKey));
    }

    /**
     * 更新部门管理数据。
     *
     * @param sysDeptDto 更新对象。
     * @return 应答结果对象。
     */
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody SysConfigDTO sysConfigDTO) {
        String errorMessage = MyCommonUtil.getModelValidationError(sysConfigDTO, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SysConfig sysConfig = MyModelUtil.copyTo(sysConfigDTO, SysConfig.class);
        SysConfig originalSysDept = sysConfigService.getById(sysConfig.getConfigKey());
        if (originalSysDept == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!sysConfigService.update(sysConfig, originalSysDept)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }
}
