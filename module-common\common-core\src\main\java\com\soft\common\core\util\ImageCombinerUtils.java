package com.soft.common.core.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.freewayso.image.combiner.ImageCombiner;
import com.freewayso.image.combiner.element.TextElement;
import com.freewayso.image.combiner.enums.OutputFormat;
import com.freewayso.image.combiner.enums.ZoomMode;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import org.springframework.core.io.ClassPathResource;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.util.Arrays;
import java.util.Hashtable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 图片合成工具类
 * @date 2023-09-05  09:08:15
 */
public class ImageCombinerUtils {



    /**
     * 生产设备二维码
     * @param qrContent
     * @param equipmentName
     * @param equipmentCode
     * @param spaceFullName
     * @return
     */
    public static ImageCombiner generatorEquipmentQrcode(String qrContent, String equipmentName, String equipmentCode, String spaceFullName) throws Exception {
        // 创建一个底图，指定宽高
        BufferedImage bufferedImage = new BufferedImage(225, 260, BufferedImage.TYPE_INT_RGB);
        // 获取底图图形
        Graphics graphics = bufferedImage.getGraphics();
        // 设置底图颜色为 空白
        graphics.setColor(Color.WHITE);
        // 填充底图
        graphics.fillRect(0, 0, bufferedImage.getWidth(), bufferedImage.getHeight());

        // 合成器（指定背景图和输出格式，整个图片的宽高和相关计算依赖于背景图，所以背景图的大小是个基准）
        ImageCombiner combiner = new ImageCombiner(bufferedImage, 0, 0, ZoomMode.Origin, OutputFormat.PNG);
        combiner.setQuality(1f);

        // 二维码
        BufferedImage qrCodeImage =createQRcode(qrContent,180, 180);
        combiner.addImageElement(qrCodeImage, 0, 0, 180, 180,ZoomMode.Width) .setCenter(true);
        //.setZoomMode(ZoomMode.WidthHeight)


        // 设备名称
        TextElement equipmentNameElement = combiner.addTextElement("名称：" + equipmentName, "宋体", 14, 28, 185)
//                .setCenter(true)
                .setAutoBreakLine(180);

        // 安装位置
        Integer equipmentCodeElementHeight = equipmentNameElement.getHeight();
        int codeOverHeight = equipmentCodeElementHeight + 11;
        combiner.addTextElement("位置：" + (StrUtil.isBlank(spaceFullName) ? "" : spaceFullName), "宋体", 14, 28, 185 +  codeOverHeight)
                .setAutoBreakLine(180);

        //执行图片合并
        combiner.combine();

        return combiner;
    }


    /**
     * 生产设备二维码
     *
     * @param qrContent
     * @param equipmentName
     * @param equipmentCode
     * @param spaceFullName
     * @return
     */
    public static ImageCombiner generatorEquipmentQrcodeMgs(String qrContent, String equipmentName, String equipmentCode, String spaceFullName) throws Exception {
        // 首先先画背景图片
        ClassPathResource classPathResource = new ClassPathResource("images/mgseb/equi_qr.png");
        BufferedImage bufferedImage = ImageIO.read(classPathResource.getInputStream());

        // 合成器（指定背景图和输出格式，整个图片的宽高和相关计算依赖于背景图，所以背景图的大小是个基准）
        ImageCombiner combiner = new ImageCombiner(bufferedImage, OutputFormat.PNG);
        combiner.setQuality(1f);

        // 二维码
        BufferedImage qrCodeImage = createQRcode(qrContent, 215, 215);
        combiner.addImageElement(qrCodeImage, 0, 150).setZoomMode(ZoomMode.Width).setCenter(true);

        // 首先先画背景图片
        ClassPathResource logoPathResource = new ClassPathResource("images/mgseb/logo_qr.png");
        BufferedImage logoImage = ImageIO.read(logoPathResource.getInputStream());
        combiner.addImageElement(logoImage, 0, 230).setZoomMode(ZoomMode.Width).setCenter(true);
        // 设备名称
        combiner.addTextElement("名称：" + equipmentName, "宋体", 18, 1, 418).setCenter(true);
        String spaceFullNameStr = "";
        if (StringUtils.isNotBlank(spaceFullName)) {
            String[] split = spaceFullName.split("/");
            List<String> spaceFullNameList = Arrays.asList(split);
            if (CollectionUtil.isNotEmpty(spaceFullNameList)) {
                if (spaceFullNameList.size() == 4 || spaceFullNameList.size() == 3) {
                    spaceFullNameStr = spaceFullNameList.get(1) + "/" + spaceFullNameList.get(2);
                }
                if (spaceFullNameList.size() == 2) {
                    spaceFullNameStr = spaceFullNameList.get(1);
                }
                if (spaceFullNameList.size() == 1) {
                    spaceFullNameStr = spaceFullNameList.get(0);
                }
            }
        }
        combiner.addTextElement("位置：" + spaceFullNameStr, "宋体", 18, 1, 440).setCenter(true);
        //执行图片合并
        combiner.combine();
        return combiner;
    }


    /**
     * 生成二维码
     *
     * @param url 二维码内容
     * @return 返回新image
     */
    public static BufferedImage createQRcode(String url, int width, int height) throws Exception {
        //二维码容错率，分四个等级：H、L 、M、 Q
        //生成二维码中的设置
        Hashtable hints = new Hashtable();
        //编码、容错率、二维码边框宽度，这里文档说设置0-4
        hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        hints.put(EncodeHintType.MARGIN, 0);

        //二维码图片大小
        int size = width;
        //生成bitMatrix
        BitMatrix bitMatrix = new MultiFormatWriter().encode(url, BarcodeFormat.QR_CODE, size, size, hints);

        //自定义白边边框宽度
        int margin = 0;
        //生成新的bitMatrix
        bitMatrix = updateBit(bitMatrix, margin);

        //因为二维码生成时，白边无法控制，去掉原有的白边，再添加自定义白边后，二维码大小与size大小就存在差异了，
        //为了让新生成的二维码大小还是size大小，根据size重新生成图片
        BufferedImage bi = MatrixToImageWriter.toBufferedImage(bitMatrix);
        //根据size放大、缩小生成的二维码
        return zoomInImage(bi, size, size);
    }

    /**
     * 因为二维码边框设置那里不起作用，不管设置多少，都会生成白边，所以根据网上
     * 的例子进行修改，自定义控制白边宽度，该方法生成自定义白边框后的bitMatrix；
     */
    private static BitMatrix updateBit(BitMatrix matrix, int margin) {
        int tempM = margin * 2;
        //获取二维码图案的属性
        int[] rec = matrix.getEnclosingRectangle();
        int resWidth = rec[2] + tempM;
        int resHeight = rec[3] + tempM;
        // 按照自定义边框生成新的BitMatrix
        BitMatrix resMatrix = new BitMatrix(resWidth, resHeight);
        resMatrix.clear();
        //循环，将二维码图案绘制到新的bitMatrix中
        for (int i = margin; i < resWidth - margin; i++) {
            for (int j = margin; j < resHeight - margin; j++) {
                if (matrix.get(i - margin + rec[0], j - margin + rec[1])) {
                    resMatrix.set(i, j);
                }
            }
        }
        return resMatrix;
    }

    /**
     * 图片放大缩小
     */
    public static BufferedImage zoomInImage(BufferedImage originalImage, int width, int height) {
        BufferedImage newImage = new BufferedImage(width, height, originalImage.getType());
        Graphics g = newImage.getGraphics();
        g.drawImage(originalImage, 0, 0, width, height, null);
        g.dispose();
        return newImage;
    }


    public static ImageCombiner generatorSpaceQrcode(String qrContent, String fullName) throws Exception {
        // 创建一个底图，指定宽高
        BufferedImage bufferedImage = new BufferedImage(225, 260, BufferedImage.TYPE_INT_RGB);
        // 获取底图图形
        Graphics graphics = bufferedImage.getGraphics();
        // 设置底图颜色为 空白
        graphics.setColor(Color.WHITE);
        // 填充底图
        graphics.fillRect(0, 0, bufferedImage.getWidth(), bufferedImage.getHeight());

        // 合成器（指定背景图和输出格式，整个图片的宽高和相关计算依赖于背景图，所以背景图的大小是个基准）
        ImageCombiner combiner = new ImageCombiner(bufferedImage, 0, 0, ZoomMode.Origin, OutputFormat.PNG);
        combiner.setQuality(1f);

        // 二维码
        BufferedImage qrCodeImage = createQRcode(qrContent, 180, 180);
        combiner.addImageElement(qrCodeImage, 0, 0).setZoomMode(ZoomMode.Width).setCenter(true);
        combiner.addTextElement("位置：" + (StrUtil.isBlank(fullName) ? "" : fullName), "宋体", 18, 28, 205).setAutoBreakLine(180);

        //执行图片合并
        combiner.combine();

        return combiner;
    }

    public static ImageCombiner generatorSpaceQrcodeMgseb(String qrContent, String fullName) throws Exception {
        // 首先先画背景图片
        ClassPathResource classPathResource = new ClassPathResource("images/mgseb/point_qr.png");
        BufferedImage bufferedImage = ImageIO.read(classPathResource.getInputStream());

        // 合成器（指定背景图和输出格式，整个图片的宽高和相关计算依赖于背景图，所以背景图的大小是个基准）
        ImageCombiner combiner = new ImageCombiner(bufferedImage, OutputFormat.PNG);
        //combiner.setQuality(1f);

        // 二维码
        BufferedImage qrCodeImage = createQRcode(qrContent, 215, 215);
        combiner.addImageElement(qrCodeImage, 0, 150).setZoomMode(ZoomMode.Width).setCenter(true);

        // 首先先画背景图片
        ClassPathResource logoPathResource = new ClassPathResource("images/mgseb/logo_qr.png");
        BufferedImage logoImage = ImageIO.read(logoPathResource.getInputStream());
        combiner.addImageElement(logoImage, 0, 230).setZoomMode(ZoomMode.Width).setCenter(true);

        combiner.addTextElement("编码：" + (StrUtil.isBlank(fullName) ? "" : fullName), "宋体", 18, 0, 418).setAutoBreakLine(350).setCenter(true);

        //执行图片合并
        combiner.combine();

        return combiner;
    }


    public static ImageCombiner generatorSignInQrcode(String qrContent) throws Exception {
        // 首先先画背景图片
        ClassPathResource classPathResource = new ClassPathResource("images/mgseb/sign_in_qr.png");
        BufferedImage bufferedImage = ImageIO.read(classPathResource.getInputStream());

        // 合成器（指定背景图和输出格式，整个图片的宽高和相关计算依赖于背景图，所以背景图的大小是个基准）
        ImageCombiner combiner = new ImageCombiner(bufferedImage, OutputFormat.PNG);
        //combiner.setQuality(1f);

        // 二维码
        BufferedImage qrCodeImage = createQRcode(qrContent, 215, 215);
        combiner.addImageElement(qrCodeImage, 0, 150).setZoomMode(ZoomMode.Width).setCenter(true);

        // 首先先画背景图片
        ClassPathResource logoPathResource = new ClassPathResource("images/mgseb/logo_qr.png");
        BufferedImage logoImage = ImageIO.read(logoPathResource.getInputStream());
        combiner.addImageElement(logoImage, 0, 230).setZoomMode(ZoomMode.Width).setCenter(true);

        //执行图片合并
        combiner.combine();

        return combiner;
    }


//    public static void main(String[] args) throws Exception {
//        JSONObject qrContent = new JSONObject();
//        qrContent.put("equipmentId", 123456789);
//        ImageCombiner imageCombiner = generatorEquipmentQrcode(qrContent.toJSONString(), "测试阿凡达的法国队高富帅", "跟货代国会对懊悔大噶大家快来", "1幢2层402室勾搭勾搭积极考虑警告i");
//        BufferedImage combinedImage = imageCombiner.getCombinedImage();
//
//        // 转 Base 64
//        ByteArrayOutputStream baos = new ByteArrayOutputStream();
//        ImageIO.write(combinedImage, "png", baos);
//        byte[] bytes = baos.toByteArray();
//        String imageBase64 = Base64Encoder.encode(bytes)
//                .trim()
//                .replaceAll("\n", "")
//                .replaceAll("\r", "");
//        System.out.println(imageBase64);
//    }
}
