<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rutong.medical.admin.mapper.TDMapper">



    <select id="selectByTimeRange" resultType="com.rutong.medical.admin.entity.AlarmDetailTD">
      <![CDATA[
        SELECT
            alarm_date,
            is_alarm,
            alarm_detail_id
        FROM
            alarm_detail
        WHERE
            alarm_date >= #{start}
          AND alarm_date <= #{end}
        ]]>
    </select>

</mapper>
