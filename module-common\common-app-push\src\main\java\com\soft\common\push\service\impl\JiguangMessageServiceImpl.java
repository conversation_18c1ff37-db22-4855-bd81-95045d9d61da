package com.soft.common.push.service.impl;

import cn.jiguang.sdk.api.PushApi;
import cn.jiguang.sdk.bean.push.PushSendParam;
import cn.jiguang.sdk.bean.push.PushSendResult;
import cn.jiguang.sdk.bean.push.audience.Audience;
import cn.jiguang.sdk.bean.push.message.notification.NotificationMessage;
import cn.jiguang.sdk.bean.push.options.Options;
import cn.jiguang.sdk.constants.ApiConstants;
import cn.jiguang.sdk.exception.ApiErrorException;
import com.alibaba.fastjson.JSON;
import com.soft.common.push.dto.MessageRecordContentDTO;
import com.soft.common.push.service.JiguangMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * @className: JiguangMessageServiceImpl
 * @author: WangZeYu
 * @date: 2024/9/11 下午7:52
 * @description:
 */
@Service
@Slf4j
public class JiguangMessageServiceImpl implements JiguangMessageService {
    @Autowired(required = false)
    private PushApi pushApi;

    @Value("${jiguang-push.switch-flag:false}")
    private Boolean switchFlag;

    @Value("${jiguang-push.android-activity:uni}")
    private String androidActivity;


    /**
     * @return void
     * <AUTHOR>
     * @Description 请求极光推送接口，封装请求参数
     * @Date 上午11:37 2024/9/18
     * @Param [messageRecordContent]
     **/
    @Override
    public void pushJiGuangMessage(MessageRecordContentDTO messageRecordContent) {
        StringBuffer sb = new StringBuffer();
        sb.append("intent:#Intent;action=").append(messageRecordContent.getHyperlink())
                .append(";component=").append(androidActivity).append("/com.test.badge.MainActivity;end\n");
        PushSendParam param = new PushSendParam();
        // 通知内容
        NotificationMessage.Android android = new NotificationMessage.Android();
        android.setAlert(messageRecordContent.getContent());
        android.setTitle(messageRecordContent.getTitle());
        //设置角标数字累加值，在原角标的基础上进行累加
        android.setBadgeAddNumber(1);
        //桌面图标对应的应用入口 Activity 类,badge_add_num 使用，二者需要共存，缺少其一不可
        android.setBadgeClass("com.test.badge.MainActivity");
        NotificationMessage.Android.Intent intent = new NotificationMessage.Android.Intent();
        intent.setUrl(sb.toString());
        android.setIntent(intent);
        Map<String, Object> extrasMap = new HashMap<>(4);
        extrasMap.put("voiceReminderEnabled", messageRecordContent.getVoiceReminderEnabled());
        extrasMap.put("hmosUrl", messageRecordContent.getHyperlink());
        android.setExtras(extrasMap);

        NotificationMessage.IOS ios = new NotificationMessage.IOS();
        Map<String, String> iosAlert = new HashMap<>(4);
        iosAlert.put("title", messageRecordContent.getTitle());
        //iosAlert.put("subtitle", messageRecordContent.getTitle());
        iosAlert.put("body", messageRecordContent.getContent());
        ios.setAlert(iosAlert);
        ios.setExtras(extrasMap);
        //ios.setBadge("1");
        //推送的时候携带 "content-available":true，说明是 Background Remote Notification，如果不携带此字段则是普通的 Remote Notification
        //https://docs.jiguang.cn/jpush/client/iOS/ios_new_fetures#ios-7-background-remote-notification
        //ios.setContentAvailable(true);

        NotificationMessage.HMOS hmos = new NotificationMessage.HMOS();
        Map<String, String> hmosAlert = new HashMap<>(4);
        hmosAlert.put("alert", messageRecordContent.getContent());
        hmosAlert.put("title", messageRecordContent.getTitle());
        NotificationMessage.Android.Intent hmosIntent = new NotificationMessage.Android.Intent();
        hmosIntent.setUrl(sb.toString());
        hmos.setIntent(hmosIntent);
        //设置角标数字累加值,badge_add_num 数据会和原角标数量进行相加，建议取值为 1
        hmos.setBadgeAddNumber(1);
        hmos.setAlert(messageRecordContent.getContent());
        hmos.setTitle(messageRecordContent.getTitle());
        hmos.setExtras(extrasMap);
        hmos.setCategory("WORK");
        //可选,测试消息标识,false：正常消息（默认值）,true：测试消息,生产环境可以修改为false
        //TODO 生产环境修改为false
        hmos.setIsTest(true);

        NotificationMessage notificationMessage = new NotificationMessage();
        notificationMessage.setAlert(messageRecordContent.getContent());
        notificationMessage.setAndroid(android);
        notificationMessage.setIos(ios);
        notificationMessage.setHmos(hmos);
        param.setNotification(notificationMessage);
        //options
        Options options = new Options();
        Map<String, Object> thirdPartyMap = new HashMap<>();
        Map<String, Object> huaweiMap = new HashMap<>();
        //通知栏消息下发逻辑，secondary_push：表示推送优先走极光，极光不在线再走厂商，厂商作为辅助（建议此种方式）
        huaweiMap.put("distribution", "secondary_push");
        //华为、荣耀通知栏消息智能分类,LOW：一般消息  NORMAL：重要消息   HIGH：非常重要消息（仅华为支持）
        huaweiMap.put("importance", "NORMAL");
        //华为消息类型,仅华为通道有效 0：普通消息（默认值）1：测试消息，每个应用每日可发送该测试消息500条,生产环境修改为0
        //TODO 生产环境修改为0
        huaweiMap.put("target_user_type", 1);
        huaweiMap.put("category", "WORK");
        thirdPartyMap.put("huawei", huaweiMap);
        options.setThirdPartyChannel(thirdPartyMap);
        options.setApnsProduction(false);
        param.setOptions(options);

        //目标人群
        Audience audience = new Audience();
        audience.setRegistrationIdList(messageRecordContent.getRegistrationId());
        param.setAudience(audience);
        //发送所有平台
        param.setPlatform(ApiConstants.Platform.ALL);
        try {
            log.info("jiguang push message request: {}", JSON.toJSONString(param));
            PushSendResult send = pushApi.send(param);
            System.out.println("jiguang push message response 推送" + send);
        } catch (ApiErrorException e) {
            log.info("jiguang push message response error:{}", e.getMessage());
        }
    }


    /**
     * @return java.lang.Boolean
     * <AUTHOR>
     * @Description 获取极光推送配置的开关 启用或者禁用
     * @Date 上午11:38 2024/9/18
     * @Param []
     **/
    @Override
    public Boolean getJiGuangConfig() {
        return switchFlag;
    }
}
