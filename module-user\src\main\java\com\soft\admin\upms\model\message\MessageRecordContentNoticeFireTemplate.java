package com.soft.admin.upms.model.message;


import cn.hutool.core.date.DateUtil;
import com.soft.admin.upms.api.dingtalk.enums.DingTalkMessageType;
import com.soft.admin.upms.enums.MessageRecordBusiTypeEnums;
import com.soft.admin.upms.enums.MessageRecordTypeEnums;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class MessageRecordContentNoticeFireTemplate extends MessageRecordContentNoticeTemplate {


    private static final MessageRecordBusiTypeEnums BUSI_TYPE = MessageRecordBusiTypeEnums.FIRE;

    public MessageRecordContent pushCheckPlan(String checkPlanName, Date checkStartTime, Long busiId, String level, Long sendUserId, Long... receiveUserIds) {
        messageRecordContent.setTitle("巡查任务");
        messageRecordContent.setBusiId(busiId);
        messageRecordContent.setLevel(level);
        String content = "您有一条新的消安巡查任务" +
                "于" +
                DateUtil.format(checkStartTime, "yyyy-MM-dd HH:mm") +
                "开始，请及时处理！";
        messageRecordContent.setContent(content);
        messageRecordContent.setSendUserId(sendUserId);
        messageRecordContent.setReceiveUserIds(Stream.of(receiveUserIds).collect(Collectors.toList()));
        messageRecordContent.setHyperlink("/smartfire/fireCheck/taskDetail?id=" + busiId);
        messageRecordContent.setType(MessageRecordTypeEnums.TODO);
        messageRecordContent.setBusiType(BUSI_TYPE);
        pushJiGuangMessage(Boolean.FALSE);
        push();
        callback(messageRecordContent);
        return messageRecordContent;
    }

    /**
     * //寻找消防隐患详情表找到派单人
     */
    public MessageRecordContent pushDanger(String dangerCode, Date rectificationDeadline, Integer dangerType, Long busiId, String level, Long sendUserId, Long... receiveUserIds) {
        messageRecordContent.setTitle("隐患治理");
        messageRecordContent.setBusiId(busiId);
        messageRecordContent.setLevel(level);
        String content = "您有一条新的隐患" +
                dangerCode +
                "，请在" +
                DateUtil.format(rectificationDeadline, "yyyy-MM-dd HH:mm") +
                "前处理完成！";
        messageRecordContent.setContent(content);
        messageRecordContent.setSendUserId(sendUserId);
        messageRecordContent.setReceiveUserIds(Stream.of(receiveUserIds).collect(Collectors.toList()));
        String hyperlink = String.format("/smartfire/dangerCheck/dangerDetail?id=%d&type=%d", busiId, dangerType);
        messageRecordContent.setHyperlink(hyperlink);
        messageRecordContent.setType(MessageRecordTypeEnums.TODO);
        messageRecordContent.setBusiType(BUSI_TYPE);
        pushJiGuangMessage(Boolean.FALSE);
        push();
        callback(messageRecordContent);
        return messageRecordContent;
    }


    public MessageRecordContent pushWaitScrap(String equipmentName, String equipmentCode, Long busiId, String level, Long sendUserId, Long... receiveUserIds) {
        messageRecordContent.setTitle("设施到期");
        messageRecordContent.setBusiId(busiId);
        messageRecordContent.setLevel(level);
        String content = equipmentName + equipmentCode + "即将到期，请及时处理！";
        messageRecordContent.setContent(content);
        messageRecordContent.setSendUserId(sendUserId);
        messageRecordContent.setReceiveUserIds(Stream.of(receiveUserIds).collect(Collectors.toList()));
        messageRecordContent.setHyperlink("");
        messageRecordContent.setType(TYPE);
        messageRecordContent.setBusiType(BUSI_TYPE);
        messageRecordContent.setDingTalkMessageType(DingTalkMessageType.MARKDOWN);
        pushJiGuangMessage(Boolean.FALSE);
        push();
//        callback(messageRecordContent);
        workNotice(messageRecordContent);
        return messageRecordContent;
    }


    public MessageRecordContent pushDeptCheck(Long busiId, String level, Long sendUserId, Long... receiveUserIds) {
        messageRecordContent.setTitle("科室自查");
        messageRecordContent.setBusiId(busiId);
        messageRecordContent.setLevel(level);
        messageRecordContent.setContent("您有科室自查任务未填写，请及时处理！");
        messageRecordContent.setSendUserId(sendUserId);
        messageRecordContent.setReceiveUserIds(Stream.of(receiveUserIds).collect(Collectors.toList()));
        messageRecordContent.setHyperlink("/smartfire/deptSelfCheck/deptSelfCheck");
        messageRecordContent.setType(MessageRecordTypeEnums.TODO);
        messageRecordContent.setBusiType(BUSI_TYPE);
        pushJiGuangMessage(Boolean.FALSE);
        List<Long> receiveUserIdList = push();
        messageRecordContent.setReceiveUserIds(receiveUserIdList);
        callback(messageRecordContent);
        return messageRecordContent;
    }

}
