package com.rutong.medical.admin.service.defence;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rutong.medical.admin.dto.defence.DefenceArmDTO;
import com.rutong.medical.admin.dto.defence.DefenceManageDTO;
import com.rutong.medical.admin.entity.defence.SmInvadeDefence;
import com.rutong.medical.admin.vo.defence.DetailVO;
import com.rutong.medical.admin.vo.defence.SmInvadeDefenceVO;
import com.soft.common.core.object.MyPageData;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-07-21
 */
public interface SmInvadeDefenceService extends IService<SmInvadeDefence> {

    //    分页查询
    MyPageData<SmInvadeDefenceVO> page(DefenceManageDTO defenceManageDTO);

    /**
     * 布防/撤防/自动布防
     *
     * @param defenceArmDTO 布防操作参数
     * @return 操作结果
     */
    Boolean updateDefenceArm(DefenceArmDTO defenceArmDTO);

    /**
     * 解除关联
     * @param id
     * @return
     */
    Boolean removeConnect(Long id);


    /**
     * 批量删除防区
     *
     * @param defenceIds 防区ID列表
     * @return 删除结果
     */
    Boolean batchDeleteDefence(List<Long> defenceIds);

    MyPageData<DetailVO> getAllDevice(DefenceManageDTO defenceManageDTO);


}
