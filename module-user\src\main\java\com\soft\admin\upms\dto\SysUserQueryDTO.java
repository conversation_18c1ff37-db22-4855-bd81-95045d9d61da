package com.soft.admin.upms.dto;


import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysUserQueryDTO extends MyPageParam {


    @ApiModelProperty("用户姓名")
    private String showName;

    private Integer userStatus;


    @ApiModelProperty("用户角色")
    private Long roleId;


    @ApiModelProperty("所属部门")
    private Long deptId;


    @ApiModelProperty("岗位")
    private Long postId;

    @ApiModelProperty("是否显示手机号 0否 1是")
    private Integer isShowPhone = 1;

}
