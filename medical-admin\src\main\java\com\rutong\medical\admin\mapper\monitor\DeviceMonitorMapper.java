package com.rutong.medical.admin.mapper.monitor;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rutong.medical.admin.dto.monitor.DeviceMonitorQueryDTO;
import com.rutong.medical.admin.entity.monitor.DeviceMonitor;

import feign.Param;

/**
 * 视频监控Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
public interface DeviceMonitorMapper extends BaseMapper<DeviceMonitor> {

    /**
     * 视频监控列表
     *
     * @param deviceMonitorQueryDTO
     * @return
     */
    List<DeviceMonitor> list(DeviceMonitorQueryDTO deviceMonitorQueryDTO);

    /**
     * 设备编号或设备名称是否存在
     *
     * @param params
     * @return
     */
    int existsByDeviceNumberOrName(@Param("monitorCode") String monitorCode, @Param("monitorName") String monitorName);

    /**
     * 设备编号或设备名称是否存在 不包括自身
     *
     * @param params
     * @return
     */
    int existsByDeviceNumberOrNameExcludingId(@Param("monitorCode") String monitorCode,
        @Param("monitorName") String monitorName, @Param("id") Long id);

    /**
     * 批量更新
     * 
     * @param list
     * @return
     */
    int batchUpdate(List<DeviceMonitor> list);

    /**
     * 根据id更新
     * 
     * @param deviceMonitor
     * @return
     */
    int updateById(DeviceMonitor deviceMonitor);

}
