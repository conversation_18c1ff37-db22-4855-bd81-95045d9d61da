package com.rutong.medical.admin.service.alarm;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rutong.medical.admin.entity.alarm.AlarmDetail;
import com.rutong.medical.admin.vo.alarm.AlarmDetailTDVO;

import java.util.List;
import java.util.Map;

/**
 * @ClassName AlarmDetailService
 * @Description
 * <AUTHOR>
 * @Date 2025/7/15 10:23
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
public interface AlarmDetailService extends IService<AlarmDetail> {


    /**
     * 获取设备状态
     *
     * @param deviceSns
     * @return
     */
    Map<String, String> getDeviceStatus(List<String> deviceSns);

    /**
     * 获取所有的报警记录
     *
     * @return
     */
    List<AlarmDetailTDVO> getAlarmDetailList();
}
