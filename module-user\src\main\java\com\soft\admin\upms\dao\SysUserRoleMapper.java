package com.soft.admin.upms.dao;

import com.soft.admin.upms.model.SysUserRole;
import com.soft.common.core.base.dao.BaseDaoMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户与角色关联关系数据访问操作接口。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
public interface SysUserRoleMapper extends BaseDaoMapper<SysUserRole> {
    /**
     * 批量新增
     * @param list
     * @return
     */
    int batchInsert(@Param("list") List<SysUserRole> list);
}
