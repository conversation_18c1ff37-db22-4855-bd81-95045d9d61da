package com.rutong.medical.admin.entity.device;

import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@Data
@TableName("device")
public class DeviceTD {
    // 时序数据列(Columns)
    private Timestamp createTime;            // 对应 tz 列
    private Integer isOnline;        // 对应 li_online 列
    private Timestamp updateTime;    // 对应 update_time 列

    // 标签数据列(Tags)
    private Long id;
    private Long deviceTerminalTypeId;
    private String deviceCode;
    private String deviceName;
    private String businessCode;
    private Long spaceId;
    private Float x;
    private Float y;
    private Float z;
    private Float longitude;
    private Float latitude;
    private Long createUserId;
    private Long updateUserId;
}