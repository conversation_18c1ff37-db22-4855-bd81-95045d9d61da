package com.soft.admin.upms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * SysUserTagDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
@ApiModel("SysUserTagDTO对象")
@Data
public class SysUserTagDTO {

    @ApiModelProperty(value = "用户Id")
    @NotNull(message = "数据验证失败，用户Id不能为空！", groups = {UpdateGroup.class})
    private Long userId;

    @ApiModelProperty(value = "标签Id")
    @NotNull(message = "数据验证失败，标签Id不能为空！", groups = {UpdateGroup.class})
    private Long tagId;

}
