package com.soft.admin.upms.controller;

import com.soft.admin.upms.dto.SysTagDTO;
import com.soft.admin.upms.dto.SysTagPageDTO;
import com.soft.admin.upms.service.SysTagService;
import com.soft.admin.upms.vo.SysTagVO;
import com.soft.common.core.constant.ErrorCodeEnum;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.common.core.validator.UpdateGroup;
import com.soft.common.log.annotation.OperationLog;
import com.soft.common.log.model.constant.SysOperationLogType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 标签控制器类
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@RestController
@RequestMapping("/admin/upms/tag")
public class SysTagController {

    @Autowired
    private SysTagService tagService;


    /**
     * 标签列表查询
     *
     * @param pageDTO 查询条件
     * @return
     */
    @GetMapping("/pageList")
    public ResponseResult<MyPageData<SysTagVO>> pageList(@Validated SysTagPageDTO pageDTO) {
        MyPageData<SysTagVO> pageData = tagService.pageList(pageDTO);
        return ResponseResult.success(pageData);
    }


    /**
     * 新建标签
     *
     * @param tagDTO 标签新增
     * @return
     */
    @PostMapping("/add")
    @OperationLog(type = SysOperationLogType.ADD)
    public ResponseResult<Boolean> add(@RequestBody @Validated SysTagDTO tagDTO) {
        return ResponseResult.success(tagService.update(tagDTO));
    }


    /**
     * 修改标签
     *
     * @param tagDTO
     * @return
     */
    @PostMapping("/update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    public ResponseResult<Boolean> update(@RequestBody @Validated({UpdateGroup.class}) SysTagDTO tagDTO) {
        return ResponseResult.success(tagService.update(tagDTO));
    }

    /**
     * 删除标签
     *
     * @param tagId 标签ID
     * @return
     */
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@RequestParam Long tagId) {
        boolean delete = tagService.delete(tagId);
        return delete ? ResponseResult.success() : ResponseResult.error(ErrorCodeEnum.NO_ERROR, "删除失败！");
    }

}
