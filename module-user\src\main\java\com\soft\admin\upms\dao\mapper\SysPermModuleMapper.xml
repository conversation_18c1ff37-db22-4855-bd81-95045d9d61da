<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.SysPermModuleMapper">
    <resultMap id="BaseResultMap" type="com.soft.admin.upms.model.SysPermModule">
        <id column="module_id" jdbcType="BIGINT" property="moduleId"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="module_name" jdbcType="VARCHAR" property="moduleName"/>
        <result column="module_type" jdbcType="INTEGER" property="moduleType"/>
        <result column="show_order" jdbcType="INTEGER" property="showOrder"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <resultMap id="BaseResultMapEx" type="com.soft.admin.upms.model.SysPermModule" extends="BaseResultMap">
        <collection property="sysPermList" column="module_id" javaType="ArrayList"
                    ofType="com.soft.admin.upms.model.SysPerm" notNullColumn="perm_id"
                    resultMap="com.soft.admin.upms.dao.SysPermMapper.BaseResultMap">
        </collection>
    </resultMap>

    <select id="getPermModuleAndPermList" resultMap="BaseResultMapEx">
        SELECT
            pm.module_id,
            pm.module_name,
            pm.parent_id,
            pm.module_type,
            p.perm_id,
            p.perm_name,
            p.module_id,
            p.url
        FROM
            common_sys_perm_module pm
        LEFT JOIN
            common_sys_perm p ON pm.module_id = p.module_id
        ORDER BY
            pm.show_order, p.show_order
    </select>
</mapper>