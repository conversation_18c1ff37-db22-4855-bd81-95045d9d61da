package com.soft.common.push.dto;

import lombok.Data;

import java.util.List;

/**
 * @className: MessageRecordContentDTO
 * @author: WangZeYu
 * @date: 2024/9/12 下午3:36
 * @description:
 */
@Data
public class MessageRecordContentDTO {
    private String title;

    private Long busiId;

    private String level;

    private String content;

    private Long sendUserId;

    private List<Long> receiveUserIds;

    private String hyperlink;
    /**
     * 设备id集合
     */
    private List<String> registrationId;
    /**
     * 是否有语音提醒
     */
    private Boolean voiceReminderEnabled;
}
