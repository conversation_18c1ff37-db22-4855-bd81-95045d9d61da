package com.soft.admin.upms.dto;

import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description 系统消息分页查询DTO
 * @Date 0026, 2023年6月26日 14:01
 * <AUTHOR>
 **/
@ApiModel("系统消息分页查询DTO")
@Data
public class SysTagPageDTO extends MyPageParam {

    /**
     * 是否已读
     */
    @ApiModelProperty("标签名称/编号")
    private String key;

    /**
     * 是否已读
     */
    @ApiModelProperty("标签类型 1用户标签 2设备标签")
    @NotNull(message = "标签类型不能为空")
    private Integer type;

}
