package com.rutong.medical.admin.service.station.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rutong.medical.admin.entity.station.DeviceBaseStationMonitor;
import com.rutong.medical.admin.mapper.station.DeviceBaseStationMonitorMapper;
import com.rutong.medical.admin.service.station.DeviceBaseStationMonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 基站监控关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class DeviceBaseStationMonitorServiceImpl extends ServiceImpl<DeviceBaseStationMonitorMapper, DeviceBaseStationMonitor> implements DeviceBaseStationMonitorService {

    @Autowired
    private DeviceBaseStationMonitorMapper smDeviceBaseStationMonitorMapper;

}
