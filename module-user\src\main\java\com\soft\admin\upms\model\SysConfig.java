package com.soft.admin.upms.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.admin.upms.vo.SysConfigVO;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.common.core.base.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 系统参数配置标对象 common_sys_config
 *
 * <AUTHOR>
 * @date 2023-09-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "common_sys_config")
public class SysConfig extends BaseModel {

    /**
     * 键名称
     */
    @TableId(value = "config_key")
    private String configKey;

    /**
     * 值
     */
    private String keyValue;

    /**
     * 值类型(select:下拉框、input输入框、date:单时间选择器、datetimerange:双时间选择器、json)
     */
    private String valueType;

    /**
     * 枚举值(如下拉[{key:'1',value:'值1'},{key:'2',value:'值2'}])
     */
    private String enumValue;

    /**
     * 说明
     */
    private String explainInit;

    /**
     * 是否显示 0显示 1不显示
     */
    private String isShow;

    /**
     * 是否可编辑 0不可编辑 1可编辑
     */
    private String isEdit;

    private String remark;


    @Mapper
    public interface SysConfigModelMapper extends BaseModelMapper<SysConfigVO, SysConfig> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        SysConfig toModel(SysConfigVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        SysConfigVO fromModel(SysConfig entity);
    }

    public static final SysConfigModelMapper INSTANCE = Mappers.getMapper(SysConfigModelMapper.class);
}
