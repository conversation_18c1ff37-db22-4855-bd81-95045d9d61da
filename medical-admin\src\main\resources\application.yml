spring:
  profiles:
    active: common,dev
  application:
    name: smart-park
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  mvc:
    converters:
      preferred-json-mapper: fastjson
  freemarker:
    template-loader-path: classpath:/template/
    cache: false
    charset: UTF-8
    check-template-location: true
    content-type: text/html
    expose-request-attributes: false
    expose-session-attributes: false
    request-context-attribute: request
    suffix: .ftl

# 暴露监控端点
management:
  endpoints:
    web:
      exposure:
        # 只暴露必要的端点（如health, info）
        include: health
      # 修改默认的actuator路径（可选）
      base-path: /internal-actuator
  endpoint:
    # 禁用敏感端点
    health:
      show-details: never
    shutdown:
      enabled: false
    env:
      enabled: false
    beans:
      enabled: false
    conditions:
      enabled: false
    configprops:
      enabled: false
    mappings:
      enabled: false

# 服务配置
application:
  # Jwt令牌加密的签名值。该值的长度要超过10个字符(过短会报错)。
  tokenSigningKey: common_flowable-signing-key
  # Jwt令牌在Http Header中的键名称。
  tokenHeaderKey: Authorization
  # Jwt令牌刷新后在Http Header中的键名称。
  refreshedTokenHeaderKey: RefreshedToken
  # Jwt令牌过期时间(毫秒)。
  expiration: 72000000
  appExpiration: **********
  # 初始化密码。
  defaultUserPassword: 123456
  # 缺省的文件上传根目录。
  uploadFileBaseDir: ./zz-resource/upload-files/app
  # 跨域的IP(http://*************:8086)白名单列表，多个IP之间逗号分隔(* 表示全部信任，空白表示禁用跨域信任)。
  credentialIpList: "*"
  # Session的用户和数据权限在Redis中的过期时间(秒)。
  sessionExpiredSeconds: 2592000

# mybatis-plus相关配置
mybatis-plus:
  mapper-locations: classpath*:com/soft/admin/upms/dao/mapper/*Mapper.xml,com/soft/common/log/dao/mapper/*Mapper.xml,com/rutong/medical/admin/mapper/**/*Mapper.xml
  type-aliases-package: com.rutong.medical.admin.entity.*.*,com.soft.admin.upms.model.*,com.soft.common.log.model.*
  global-config:
    db-config:
      logic-delete-value: -1
      logic-not-delete-value: 1

# 自动分页的配置
pagehelper:
  helperDialect: mysql
  reasonable: false
  supportMethodsArguments: false
  params: count=countSql
# 主键生成策略
sequence:
  # Snowflake 分布式Id生成算法所需的WorkNode参数值。
  snowflakeWorkNode: 1
flowable:
  async-executor-activate: false
  database-schema-update: false
datafilter:
  tenant:
    # 对于单体服务，该值始终为false。
    enabled: false
  permGroup:
    # 权限组过滤
    enabled: true
  dataperm:
    enabled: true
    # 在拼接数据权限过滤的SQL时，我们会用到sys_dept_relation表，该表的前缀由此配置项指定。
    # 如果没有前缀，请使用 "" 。
    deptRelationTablePrefix: common_
    # 是否在每次执行数据权限查询过滤时，都要进行菜单Id和URL之间的越权验证。
    enableMenuPermVerify: true
iot:
  autoSyncData: false
