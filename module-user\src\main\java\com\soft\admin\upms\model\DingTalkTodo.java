package com.soft.admin.upms.model;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@TableName(value = "ding_talk_todo")
public class DingTalkTodo {

    private Long id;

    private Long messageId;

    private Long busiId;

    private String todoId;

    private String userUnionId;

    private String busiType;

    private Date createTime;

    private Long sendUserId;

    private Long receiveUserId;

    private String title;

}
