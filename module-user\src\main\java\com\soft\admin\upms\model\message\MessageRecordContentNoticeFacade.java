package com.soft.admin.upms.model.message;

public class MessageRecordContentNoticeFacade {

    public MessageRecordContentNoticeWorkOrderTemplate WORK_ORDER() {
        return new MessageRecordContentNoticeWorkOrderTemplate();
    }

    public MessageRecordContentNoticeEstateTemplate ESTATE() {
        return new MessageRecordContentNoticeEstateTemplate();
    }

    public MessageRecordContentNoticePropertyTemplate PROPERTY() {
        return new MessageRecordContentNoticePropertyTemplate();
    }

    public MessageRecordContentNoticeContingencyTemplate CONTINGENCY() {
        return new MessageRecordContentNoticeContingencyTemplate();
    }

    public MessageRecordContentNoticeFireTemplate FIRE() {
        return new MessageRecordContentNoticeFireTemplate();
    }


    /**
     * 发送通知
     * @return
     */
    public MessageRecordContentCustomTemplate CUSTOM() {
        return new MessageRecordContentCustomTemplate();
    }
}