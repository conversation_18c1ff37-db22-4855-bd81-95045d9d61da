package com.rutong.medical.admin.controller.device;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.rutong.medical.admin.dto.device.DeviceTerminalSaveDTO;
import com.rutong.medical.admin.dto.station.DevicePageQueryDTO;
import com.rutong.medical.admin.service.device.DeviceService;
import com.rutong.medical.admin.vo.device.DeviceVO;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 设备控制器类
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@RestController
@RequestMapping("/device/")
@Api(tags = "设备管理")
public class DeviceController {

    @Autowired
    private DeviceService smDeviceService;

    @ApiOperation(value = "设备分页查询")
    @GetMapping("page")
    public ResponseResult<MyPageData<DeviceVO>> page(DevicePageQueryDTO devicePageQueryDTO) {
        return ResponseResult.success(smDeviceService.page(devicePageQueryDTO));
    }

    @ApiOperation(value = "设备查询列表")
    @GetMapping("list")
    public ResponseResult<List<DeviceVO>> list(DevicePageQueryDTO devicePageQueryDTO) {
        return ResponseResult.success(smDeviceService.list(devicePageQueryDTO));
    }

    @ApiOperation(value = "删除设备")
    @PostMapping("delete/{id}")
    public ResponseResult<Void> delete(@PathVariable(value = "id", required = true) Long id) {
        smDeviceService.delete(id);
        return ResponseResult.success();
    }

    @ApiOperation(value = "新增或者修改设备")
    @PostMapping("saveOrUpdate")
    public ResponseResult<Void> saveOrUpdate(@RequestBody DeviceTerminalSaveDTO deviceTerminalSaveDTO) {
        smDeviceService.saveOrUpdate(deviceTerminalSaveDTO);
        return ResponseResult.success();
    }

    @ApiOperation(value = "查询系统名称")
    @GetMapping("listSystem")
    public ResponseResult<List<Map<String, String>>> listSystem() {
        return ResponseResult.success(smDeviceService.listSystem());
    }

    @ApiOperation(value = "设备详情接口")
    @GetMapping("detail/{id}")
    public ResponseResult<DeviceVO> detail(@PathVariable("id") Long id) {
        return ResponseResult.success(smDeviceService.detail(id));
    }
}
