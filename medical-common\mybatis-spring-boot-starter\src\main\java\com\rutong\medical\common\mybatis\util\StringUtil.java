package com.rutong.medical.common.mybatis.util;

import java.time.LocalDate;  
import java.time.format.DateTimeFormatter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.lang.Nullable;

 
/**
 * @Description:
 * @Auther: chaibo
 * @Date: 2019/3/6 14:47
 * @version:
 */
public class StringUtil {

    public static boolean checkMatch(String source, int sourceLength, String rule, int ruleLength) {
        boolean[][] dynamic = new boolean[sourceLength + 1][ruleLength + 1];
        dynamic[0][0] = true;
        for (int i = 1; i < sourceLength + 1; i++) {
            for (int j = 1; j < ruleLength + 1; j++) {
                if (rule.charAt(j - 1) == '*') {
                    dynamic[i][j] = dynamic[i - 1][j] || dynamic[i][j-1];
                }else {
                    dynamic[i][j] = source.charAt(i - 1) == rule.charAt(j - 1) && dynamic[i - 1][j - 1];
                }
            }
        }
        return dynamic[sourceLength][ruleLength];
    }

    public static int showNumber(String srcText, String findText) {
        int count = 0;
        Pattern p = Pattern.compile(findText);
        Matcher m = p.matcher(srcText);
        while (m.find()) {
            count++;
        }
        return count;
    }

    public static String removePunct(String source){
        if (source == null || source.length() == 0) {
            return "";
        }
        try{
            String regEx="[\n`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。， 、？]";
            Pattern p = Pattern.compile(regEx);
            Matcher m = p.matcher(source);
            return  m.replaceAll("").trim();
        }catch (Exception e){
            e.printStackTrace();
        }
       return source;
    }

	/**
	 * 转换为String数组<br>
	 *
	 * @param str 被转换的值
	 * @return 结果
	 */
	public static String[] toStrArray(String str) {
		return toStrArray(",", str);
	}
	
	/**
	 * 转换为String数组<br>
	 *
	 * @param split 分隔符
	 * @param str   被转换的值
	 * @return 结果
	 */
	public static String[] toStrArray(String split, String str) {
		if (org.apache.commons.lang3.StringUtils.isBlank(str)) {
			return new String[]{};
		}
		return str.split(split);
	}

	/**
	 * 获取标识符，用于参数清理
	 *
	 * @param param 参数
	 * @return 清理后的标识符
	 */
	@Nullable
	public static String cleanIdentifier(@Nullable String param) {
		if (param == null) {
			return null;
		}
		StringBuilder paramBuilder = new StringBuilder();
		for (int i = 0; i < param.length(); i++) {
			char c = param.charAt(i);
			if (Character.isJavaIdentifierPart(c)) {
				paramBuilder.append(c);
			}
		}
		return paramBuilder.toString();
	}
	
}
