package com.rutong.medical.admin.entity.device;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 报警位置详情实体类
 */
@Data
@TableName("alarm_location_detail")
public class AlarmLocationDetailTD {

    /** 创建时间 */
    private Timestamp createTime;

    /** 是否报警 */
    private Boolean isAlarm;

    /** 新定位序列号 */
    private String newLocatorSn;

    /** 旧定位序列号 */
    private String oldLocatorSn;

    /** 用户ID */
    private Long userId;

    /** 用户名 */
    private String userName;

    /** 基站序列号 */
    private String baseStationSn;

    /** 楼宇ID */
    private Long buildingId;

    /** 楼层ID */
    private Long floorId;

    /** 点位ID */
    private Long pointId;

    /** 楼宇名称 */
    private String buildingName;

    /** 楼层名称 */
    private String floorName;

    /** 点位名称 */
    private String pointName;

    /** 报警详情ID */
    private String alarmDetailId;

    /** 报警类型 */
    private String alarmType;

    /** 报警方式（标签） */
    private Integer deviceTypeCode;

    /** 设备序列号（标签） */
    private String deviceSn;
}
