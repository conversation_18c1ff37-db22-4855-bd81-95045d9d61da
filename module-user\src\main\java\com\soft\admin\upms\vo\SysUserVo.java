package com.soft.admin.upms.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * SysUserVO视图对象。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
@ApiModel("SysUserVO视图对象")
@Data
public class SysUserVo {

    /**
     * 用户Id。
     */
    @ApiModelProperty(value = "用户Id")
    private Long userId;

    /**
     * 登录用户名。
     */
    @ApiModelProperty(value = "登录用户名")
    private String loginName;

    /**
     * 用户显示名称。
     */
    @ApiModelProperty(value = "用户显示名称")
    private String showName;


    @ApiModelProperty(value = "性别，1男，2女，3未知")
    private Integer sex;

    /**
     * 用户手机号码
     */
    @ApiModelProperty(value = "用户手机号码")
    private String phone;

    /**
     * 手机短号
     */
    @ApiModelProperty(value = "手机短号")
    private String shortPhone;


    @ApiModelProperty(value = "身份证号")
    private String cardNo;

    /**
     * 组织Id
     */
    private Long orgId;

    /**
     * 用户部门Id。
     */
    @ApiModelProperty(value = "用户部门Id")
    private Long deptId;

    /**
     * 用户类型(0: 管理员 1: 系统管理用户 2: 系统业务用户)。
     */
    @ApiModelProperty(value = "用户类型(0: 管理员 1: 系统管理用户 2: 系统业务用户)")
    private Integer userType;

    /**
     * 用户头像的Url。
     */
    @ApiModelProperty(value = "用户头像的Url")
    private String headImageUrl;

    /**
     * 用户状态(0: 正常 1: 锁定)。
     */
    @ApiModelProperty(value = "用户状态(0: 正常 1: 锁定)")
    private Integer userStatus;

    /**
     * 创建者Id。
     */
    @ApiModelProperty(value = "创建者Id")
    private Long createUserId;

    /**
     * 更新者Id。
     */
    @ApiModelProperty(value = "更新者Id")
    private Long updateUserId;

    /**
     * 创建时间。
     */
    @ApiModelProperty(value = "创建时间")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private Date createTime;

    /**
     * 更新时间。
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


    // 最后登录时间
    private Date lastLoginTime;


    /**
     * 头像
     */
    @ApiModelProperty(value = "头像")
    private String photoUrl;
    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String employeeNumber;

    /**
     * 分机号
     */
    @ApiModelProperty(value = "分机号")
    private String extensionNumber;

    /**
     * 多对多用户岗位数据集合。
     */
    @ApiModelProperty(value = "多对多用户岗位数据集合")
    private List<Map<String, Object>> sysUserPostList;

    /**
     * 多对多用户角色数据集合。
     */
    @ApiModelProperty(value = "多对多用户角色数据集合")
    private List<Map<String, Object>> sysUserRoleList;

    /**
     * 多对多用户数据权限数据集合。
     */
    @ApiModelProperty(value = "多对多用户数据权限数据集合")
    private List<Map<String, Object>> sysDataPermUserList;

    /**
     * 多对多用户项目数据集合。
     */
    @ApiModelProperty(value = "多对多用户项目数据集合")
    private List<Map<String, Object>> sysUserProjectList;

    /**
     * deptId 字典关联数据。
     */
    @ApiModelProperty(value = "deptId 字典关联数据")
    private Map<String, Object> deptIdDictMap;

    /**
     * userType 常量字典关联数据。
     */
    @ApiModelProperty(value = "userType 常量字典关联数据")
    private Map<String, Object> userTypeDictMap;

    /**
     * userStatus 常量字典关联数据。
     */
    @ApiModelProperty(value = "userStatus 常量字典关联数据")
    private Map<String, Object> userStatusDictMap;

    /**
     * 人脸照
     */
    @ApiModelProperty(value = "人脸照")
    private String facePicture;

    /**
     * 一卡通
     */
    @ApiModelProperty(value = "一卡通")
    private String oneCardNo;

    /**
     * 客户标签
     */
    @ApiModelProperty(value = "客户标签")
    private String userTags;
}
