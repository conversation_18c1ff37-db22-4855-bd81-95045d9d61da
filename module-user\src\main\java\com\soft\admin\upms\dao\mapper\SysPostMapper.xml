<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.SysPostMapper">
    <resultMap id="BaseResultMap" type="com.soft.admin.upms.model.SysPost">
        <id column="post_id" jdbcType="BIGINT" property="postId"/>
        <result column="post_name" jdbcType="VARCHAR" property="postName"/>
        <result column="post_level" jdbcType="INTEGER" property="postLevel"/>
        <result column="leader_post" jdbcType="BOOLEAN" property="leaderPost"/>
        <result column="post_code" jdbcType="VARCHAR" property="postCode"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <resultMap id="BaseResultMapWithSysDeptPost" type="com.soft.admin.upms.model.SysPost" extends="BaseResultMap">
        <association property="sysDeptPost" column="post_id" foreignColumn="post_id"
                     notNullColumn="post_id" resultMap="com.soft.admin.upms.dao.SysDeptPostMapper.BaseResultMap" />
    </resultMap>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.soft.admin.upms.dao.SysPostMapper.inputFilterRef"/>
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        and deleted_flag = 1
        <if test="sysPostFilter != null">
            <if test="sysPostFilter.postName != null and sysPostFilter.postName != ''">
                <bind name = "safeSysPostPostName" value = "'%' + sysPostFilter.postName + '%'" />
                AND common_sys_post.post_name LIKE #{safeSysPostPostName}
            </if>
            <if test="sysPostFilter.leaderPost != null">
                AND common_sys_post.leader_post = #{sysPostFilter.leaderPost}
            </if>
        </if>
    </sql>

    <select id="getSysPostList" resultMap="BaseResultMap" parameterType="com.soft.admin.upms.model.SysPost">
        SELECT * FROM common_sys_post
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSysPostListByDeptId" resultMap="BaseResultMapWithSysDeptPost">
        SELECT
            common_sys_post.*,
            common_sys_dept_post.*
        FROM
            common_sys_post,
            common_sys_dept_post
        <where>
            AND common_sys_dept_post.dept_id = #{deptId}
            AND common_sys_dept_post.post_id = common_sys_post.post_id
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getNotInSysPostListByDeptId" resultMap="BaseResultMap">
        SELECT
            common_sys_post.*
        FROM
            common_sys_post
        <where>
            AND NOT EXISTS (SELECT * FROM common_sys_dept_post
                WHERE common_sys_dept_post.dept_id = #{deptId} AND common_sys_dept_post.post_id = common_sys_post.post_id)
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
