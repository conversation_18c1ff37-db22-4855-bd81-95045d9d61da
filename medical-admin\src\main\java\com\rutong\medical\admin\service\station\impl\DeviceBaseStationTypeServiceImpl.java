package com.rutong.medical.admin.service.station.impl;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rutong.medical.admin.dto.station.DeviceBaseStationTypeDTO;
import com.rutong.medical.admin.dto.station.DeviceBaseStationTypeQueryDTO;
import com.rutong.medical.admin.entity.station.DeviceBaseStation;
import com.rutong.medical.admin.entity.station.DeviceBaseStationType;
import com.rutong.medical.admin.mapper.station.DeviceBaseStationMapper;
import com.rutong.medical.admin.mapper.station.DeviceBaseStationTypeMapper;
import com.rutong.medical.admin.service.station.DeviceBaseStationTypeService;
import com.soft.common.core.constant.GlobalDeletedFlag;
import com.soft.common.core.exception.ServiceException;
import com.soft.common.core.object.TokenData;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;

/**
 * 基站分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class DeviceBaseStationTypeServiceImpl extends ServiceImpl<DeviceBaseStationTypeMapper, DeviceBaseStationType>
    implements DeviceBaseStationTypeService {

    @Autowired
    private DeviceBaseStationMapper deviceBaseStationMapper;

    @Autowired
    private DeviceBaseStationTypeMapper deviceBaseStationTypeMapper;

    @Autowired
    private RedissonClient redissonClient;

    public static final String smartMedicalTypeCode = "smart:medical:station:typeCode:";

    @Override
    public List<Tree<Long>> getTreeList(DeviceBaseStationTypeQueryDTO deviceBaseStationTypeQuery) {
        // 根据条件查询设备类型列表
        List<DeviceBaseStationType> typeList =
            deviceBaseStationTypeMapper.selectList(new LambdaQueryWrapper<DeviceBaseStationType>()
                .eq(DeviceBaseStationType::getIsDelete, GlobalDeletedFlag.NORMAL)
                .eq(StringUtils.isNotEmpty(deviceBaseStationTypeQuery.getTypeCode()),
                    DeviceBaseStationType::getTypeCode, deviceBaseStationTypeQuery.getTypeCode())
                .like(StringUtils.isNotBlank(deviceBaseStationTypeQuery.getTypeName()),
                    DeviceBaseStationType::getTypeName, deviceBaseStationTypeQuery.getTypeName())
                .orderByDesc(DeviceBaseStationType::getCreateTime));

        // 组装成树结构
        TreeNodeConfig config = new TreeNodeConfig();
        return TreeUtil.build(typeList, 0L, config, ((type, tree) -> {
            tree.setId(type.getId());
            tree.setName(type.getTypeName());
            tree.setParentId(type.getParentId());
            tree.setWeight(0);
            tree.putExtra("typeCode", type.getTypeCode());
        }));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(DeviceBaseStationTypeDTO deviceBaseStationType) {
        String typeName = deviceBaseStationType.getTypeName();
        // 校验：type_name 是否已存在
        boolean exists = deviceBaseStationTypeMapper.selectCount(
            new LambdaQueryWrapper<DeviceBaseStationType>().eq(DeviceBaseStationType::getTypeName, typeName)
                .eq(DeviceBaseStationType::getIsDelete, GlobalDeletedFlag.NORMAL)) > 0;

        if (exists) {
            throw new ServiceException("分类名称 [" + typeName + "] 已存在，请勿重复添加");
        }

        DeviceBaseStationType entity = new DeviceBaseStationType();

        // 设置 type_code
        String nextCode = generateNextTypeCode();
        entity.setTypeCode(nextCode);
        entity.setTypeName(deviceBaseStationType.getTypeName());

        // 先保存 entity，获取生成的 id
        try {
            entity.setCreateUserId(TokenData.takeFromRequest().getUserId());
            entity.setCreateTime(new Date());
            entity.setIsDelete(GlobalDeletedFlag.NORMAL);
            deviceBaseStationTypeMapper.insert(entity);
        } catch (Exception e) {
            throw new ServiceException("添加设备类型失败");
        }

        // 处理父级路径
        if (Objects.nonNull(deviceBaseStationType.getParentId()) && deviceBaseStationType.getParentId() != 0L) {
            DeviceBaseStationType parentType =
                deviceBaseStationTypeMapper.selectById(deviceBaseStationType.getParentId());
            Assert.notNull(parentType, "上级设备类型不存在");

            // 拼接 path_id
            String parentPathId = parentType.getPathId();
            if (parentPathId == null) {
                parentPathId = "";
            }
            entity.setPathId(parentPathId + "/" + entity.getId());
            entity.setUpdateTime(new Date());
            entity.setParentId(parentType.getId());
            entity.setUpdateUserId(TokenData.takeFromRequest().getUserId());
            // 更新 path_id 字段
            deviceBaseStationTypeMapper.updateById(entity);
        } else {
            // 如果是根节点，path_id 就是自己的 id
            entity.setPathId(entity.getId().toString());
            // 更新 path_id 字段
            entity.setUpdateTime(new Date());
            entity.setUpdateUserId(TokenData.takeFromRequest().getUserId());
            deviceBaseStationTypeMapper.updateById(entity);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(DeviceBaseStationTypeDTO deviceBaseStationType) {
        // 检查目标分类是否存在
        DeviceBaseStationType stationType = deviceBaseStationTypeMapper.selectById(deviceBaseStationType.getId());
        Assert.notNull(stationType, "分类不存在");

        // 检查上级分类是否存在（如果提供了上级分类ID）
        if (deviceBaseStationType.getParentId() != null) {
            DeviceBaseStationType parentType =
                deviceBaseStationTypeMapper.selectById(deviceBaseStationType.getParentId());
            Assert.notNull(parentType, "上级分类不存在");

            // 防止循环引用（分类不能成为自己的上级）
            if (deviceBaseStationType.getId().equals(deviceBaseStationType.getParentId())) {
                throw new ServiceException("分类不能设置自己为上级");
            }
        }

        // 更新分类信息
        stationType.setTypeName(deviceBaseStationType.getTypeName());
        stationType.setParentId(deviceBaseStationType.getParentId());

        try {
            deviceBaseStationTypeMapper.updateById(stationType);
        } catch (Exception e) {
            throw new ServiceException("修改设备类型失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        DeviceBaseStationType equipmentType = deviceBaseStationTypeMapper.selectById(id);
        if (equipmentType == null) {
            throw new ServiceException("该分类不存在");
        }

        Long countDeviceStation = deviceBaseStationMapper
            .selectCount(new LambdaQueryWrapper<DeviceBaseStation>().eq(DeviceBaseStation::getDeviceBaseStationType, id)
                .eq(DeviceBaseStation::getIsDelete, GlobalDeletedFlag.NORMAL));
        if (countDeviceStation > 0) {
            throw new ServiceException("该分类有基站,无法删除");
        }

        DeviceBaseStationType parentType = deviceBaseStationTypeMapper.selectById(equipmentType.getParentId());
        if (parentType == null) {
            boolean exists = new LambdaQueryChainWrapper<>(deviceBaseStationTypeMapper)
                .eq(DeviceBaseStationType::getIsDelete, GlobalDeletedFlag.NORMAL)
                .eq(DeviceBaseStationType::getParentId, id).last("limit 1").exists();
            if (exists)
                throw new ServiceException("存在下级分类无法删除");
        }
        try {
            equipmentType.setIsDelete(GlobalDeletedFlag.DELETED);
            deviceBaseStationTypeMapper.updateById(equipmentType);
        } catch (Exception e) {
            throw new ServiceException("删除设备类型失败");
        }
    }

    /**
     * 获取基站分类编号
     * 
     * @return
     */
    private String generateNextTypeCode() {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(smartMedicalTypeCode);
        long nextValue = atomicLong.incrementAndGet();
        return String.format("%06d", nextValue);
    }
}
