package com.soft.admin.upms.api.robot;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.admin.upms.dao.SysUserMapper;
import com.soft.admin.upms.dao.ThreePartyRobotMapper;
import com.soft.admin.upms.enums.ThreePartyRobotTypeEnums;
import com.soft.admin.upms.model.SysUser;
import com.soft.admin.upms.model.ThreePartyRobot;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Sets;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description 三方机器人接口委派
 * @Date 0025, 2023年5月25日 9:17
 * <AUTHOR>
 **/
@Slf4j
@Service
public class ThreePartyRobotDelegate {

    @Resource
    private ObjectProvider<IThreePartyRobot> robotProvider;
    @Resource
    private ThreePartyRobotMapper threePartyRobotMapper;
    @Resource
    private SysUserMapper userMapper;

    /**
     * 推送消息
     *
     * @param typeList     机器人类型
     * @param message      消息内容
     * @param atUserIdList @用户id列表
     */
    public void send( Set<ThreePartyRobotTypeEnums> typeList, String message, Set<Long> atUserIdList) {
        // 默认推送机器人类型：GLOBAL
        if (CollectionUtils.isEmpty(typeList)) {
            typeList = Sets.newHashSet(ThreePartyRobotTypeEnums.GLOBAL);
        } else {
            typeList.add(ThreePartyRobotTypeEnums.GLOBAL);
        }

        // 获取当前项目机器人列表
        List<ThreePartyRobot> robotList = threePartyRobotMapper.selectList(new LambdaQueryWrapper<ThreePartyRobot>()
                .in(ThreePartyRobot::getType, typeList));
        if (CollectionUtils.isEmpty(robotList)) {
            log.error("robot - project {} is not configured with a robot");
            return;
        }

        // 获取@用户手机号
        List<String> atUserList = null;
        if (CollectionUtils.isNotEmpty(atUserIdList)) {
            List<SysUser> userList = userMapper.selectList(new LambdaQueryWrapper<SysUser>().in(SysUser::getUserId, atUserIdList));
            atUserList = userList.stream().map(SysUser::getPhone).collect(Collectors.toList());
        }

        // 根据平台类型给机器人配置分组
        Map<String, List<ThreePartyRobot>> platformRobotMap = robotList.stream()
                .collect(Collectors.groupingBy(ThreePartyRobot::getPlatform));
        // 通过平台类型获取对应的三方机器人接口实现类，发送消息
        for (String platform : platformRobotMap.keySet()) {
            IThreePartyRobot iThreePartyRobot = robotProvider.stream()
                    .filter(robotPlatform -> robotPlatform.getPlatform().equals(platform))
                    .findFirst().get();
            if (Objects.isNull(iThreePartyRobot)) {
                log.error("robot - not found three party robot api implementation: {}", platform);
                continue;
            }
            for (ThreePartyRobot robot : platformRobotMap.get(platform)) {
                iThreePartyRobot.send(robot.getWebHook(), robot.getSecret(), message, atUserList);
            }
        }
    }

    /**
     * 推送消息
     *    项目id
     * @param message      消息内容
     * @param atUserIdList @用户id列表
     */
    public void send( String message, Set<Long> atUserIdList) {
        this.send( null, message, atUserIdList);
    }

    /**
     * 推送消息
     *
     * @param message   消息内容
     */
    public void send(String message) {
        this.send( null, message, null);
    }

}
