package com.rutong.medical.admin.service.monitor;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rutong.medical.admin.dto.monitor.DeviceMonitorQueryDTO;
import com.rutong.medical.admin.dto.monitor.DeviceMonitorSaveDTO;
import com.rutong.medical.admin.dto.monitor.EquipmentControlParamDTO;
import com.rutong.medical.admin.entity.monitor.DeviceMonitor;
import com.rutong.medical.admin.vo.monitor.DeviceMonitorVO;
import com.rutong.medical.admin.vo.monitor.EquipmentImportCheckVO;
import com.soft.common.core.object.MyPageData;

/**
 * 视频监控Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
public interface DeviceMonitorService extends IService<DeviceMonitor> {
    /**
     * 视频监控列表
     * 
     * @param deviceMonitorQueryDTO
     * @return
     */
    List<DeviceMonitorVO> listMonitor(DeviceMonitorQueryDTO deviceMonitorQueryDTO);

    /**
     * 删除视频监控
     * 
     * @param id
     */
    void delete(Long id);

    /**
     * 视频监控详情接口
     * 
     * @param id
     * @return
     */
    DeviceMonitorVO detail(Long id);

    /**
     * 视频监控分页查询
     * 
     * @param deviceMonitorQueryDTO
     * @return
     */
    MyPageData<DeviceMonitorVO> page(DeviceMonitorQueryDTO deviceMonitorQueryDTO);

    /**
     * 新增或者修改视频监控
     * 
     * @param deviceMonitorSaveDTO
     */
    void saveOrUpdate(DeviceMonitorSaveDTO deviceMonitorSaveDTO);

    /**
     * 下载模板文件
     */
    void exportTemplate();

    /**
     * 获取视频厂家
     * 
     * @return
     */
    Map<String, String> getFactory();

    /**
     * 导入
     * 
     * @param file
     * @return
     * @throws IOException
     */
    void importExcel(EquipmentImportCheckVO equipmentImportCheckVO) throws IOException;

    /**
     * 导入检查
     * 
     * @param file
     * @return
     */
    EquipmentImportCheckVO importCheck(MultipartFile file) throws IOException;

    /**
     * 播放视频
     * 
     * @param methodParam
     * @return
     */
    String playFlv(EquipmentControlParamDTO methodParam);
}
