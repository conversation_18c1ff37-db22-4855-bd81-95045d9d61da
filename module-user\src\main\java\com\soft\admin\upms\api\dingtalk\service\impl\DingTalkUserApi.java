package com.soft.admin.upms.api.dingtalk.service.impl;

import com.alibaba.fastjson.JSON;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiAttendanceRecordUploadRequest;
import com.dingtalk.api.request.OapiV2UserGetRequest;
import com.dingtalk.api.request.OapiV2UserGetuserinfoRequest;
import com.dingtalk.api.request.OapiV2UserListRequest;
import com.dingtalk.api.response.OapiAttendanceRecordUploadResponse;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.dingtalk.api.response.OapiV2UserGetuserinfoResponse;
import com.dingtalk.api.response.OapiV2UserListResponse;
import com.soft.admin.upms.api.dingtalk.DingTalkConfig;
import com.soft.admin.upms.api.dingtalk.DingTalkConstants;
import com.soft.admin.upms.api.dingtalk.service.IOaUserApi;
import com.soft.admin.upms.dto.dintalk.OaUserDTO;
import com.soft.common.core.exception.ServiceException;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description 钉钉用户Api
 * @Date 0009, 2023年8月9日 14:20
 * <AUTHOR>
 **/
@Slf4j
@Service
public class DingTalkUserApi implements IOaUserApi {

    @Resource
    private DingTalkConfig dingTalkConfig;
    @Resource
    private DingTalkOauth2Api dingTalkOauth2Api;

    @Override
    public List<OaUserDTO> getUsersByOaDeptId(String oaDeptId, Long pageNum, Long pageSize) {
        DingTalkClient client = new DefaultDingTalkClient(
                dingTalkConfig.getServerUrl(DingTalkConstants.API_GET_USER_LIST_BY_DEPT));
        OapiV2UserListRequest req = new OapiV2UserListRequest();
        req.setDeptId(Long.parseLong(oaDeptId));
        req.setCursor((pageNum - 1) * pageSize);
        req.setSize(pageSize);
        req.setOrderField("custom");
        req.setContainAccessLimit(false);
        req.setLanguage("zh_CN");
        OapiV2UserListResponse rsp = null;
        try {
            rsp = client.execute(req, dingTalkOauth2Api.getAccessToken());
        } catch (ApiException e) {
            log.error("ding talk api getUsersByDeptId error: {}", e.getErrMsg());
            // throw new ServiceException(e.getErrMsg());
            return Lists.newArrayList();
        }
        log.info("ding talk api getUsersByDeptId: {}", rsp.getBody());
        if (!rsp.isSuccess()) {
            //throw new ServiceException(rsp.getErrmsg());
            return Lists.newArrayList();
        }
        log.info("*************>>>>>" + JSON.toJSONString(rsp.getResult().getList()));
        List<OaUserDTO> oaUserList = Lists.newArrayList();
        for (OapiV2UserListResponse.ListUserResponse userResponse : rsp.getResult().getList()) {
            OaUserDTO oaUser = new OaUserDTO();
            oaUser.setOaUserId(userResponse.getUserid());
            oaUser.setOaDeptId(oaDeptId);
            oaUser.setName(userResponse.getName());
            oaUser.setMobile(userResponse.getMobile());
            oaUser.setJobNumber(userResponse.getJobNumber());
            oaUser.setIsLeaved(Boolean.FALSE);
            oaUser.setLeader(userResponse.getLeader());
            oaUserList.add(oaUser);
        }
        return oaUserList;
    }

    @Override
    public OaUserDTO getUserByOaUserId(String oaUserId) {
        DingTalkClient client = new DefaultDingTalkClient(
                dingTalkConfig.getServerUrl(DingTalkConstants.API_GET_USER));
        OapiV2UserGetRequest req = new OapiV2UserGetRequest();
        req.setUserid(oaUserId);
        req.setLanguage("zh_CN");
        OapiV2UserGetResponse rsp = null;
        try {
            rsp = client.execute(req, dingTalkOauth2Api.getAccessToken());
        } catch (ApiException e) {
            log.error("ding talk api getUserById error: {}", e.getErrMsg());
            throw new ServiceException(e.getErrMsg());
        }
        log.info("ding talk api getUserById: {}", rsp.getBody());
        // 找不到该用户 = 离职
        if (rsp.getErrcode() == 60121) {
            OaUserDTO oaUser = new OaUserDTO();
            oaUser.setOaUserId(oaUserId);
            oaUser.setIsLeaved(Boolean.TRUE);
            return oaUser;
        }
        if (!rsp.isSuccess()) {
            throw new ServiceException(rsp.getErrmsg());
        }
        OaUserDTO oaUser = new OaUserDTO();
        oaUser.setOaUserId(rsp.getResult().getUserid());
        oaUser.setUnionId(rsp.getResult().getUnionid());
        if (CollectionUtils.isNotEmpty(rsp.getResult().getDeptIdList())) {
//            oaUser.setOaDeptId(rsp.getResult().getDeptIdList().get(0).toString());
            oaUser.setOaDeptIds(rsp.getResult().getDeptIdList());
        }
        oaUser.setJobNumber(rsp.getResult().getJobNumber());
        oaUser.setName(rsp.getResult().getName());
        oaUser.setMobile(rsp.getResult().getMobile());
        oaUser.setIsLeaved(Boolean.FALSE);
        return oaUser;
    }

    @Override
    public List<OaUserDTO> getUsersByOaUserIds(List<String> oaUserIds) {
        return oaUserIds.stream().map(this::getUserByOaUserId).collect(Collectors.toList()) ;
    }

    @Override
    public String getOaUserIdByCode(String code) {
        DingTalkClient client = new DefaultDingTalkClient(
                dingTalkConfig.getServerUrl(DingTalkConstants.API_GET_USER_BY_CODE));
        OapiV2UserGetuserinfoRequest req = new OapiV2UserGetuserinfoRequest();
        req.setCode(code);
        OapiV2UserGetuserinfoResponse rsp = null;
        try {
            rsp = client.execute(req, dingTalkOauth2Api.getAccessToken());
        } catch (ApiException e) {
            log.error("ding talk api getOaUserIdByCode error: {}", e.getErrMsg());
            throw new ServiceException(e.getErrMsg());
        }
        log.info("ding talk api getOaUserIdByCode: {}", rsp.getBody());
        if (!rsp.isSuccess()) {
            throw new ServiceException(rsp.getErrmsg());
        }
        return rsp.getResult().getUserid();
    }

    @Override
    public void pushPunchRecords(String oaUserId, String deviceId, String deviceName) {
        DingTalkClient client = new DefaultDingTalkClient(dingTalkConfig
                .getServerUrl(DingTalkConstants.API_UPLOAD_PUNCH_RECORDS));
        OapiAttendanceRecordUploadRequest req = new OapiAttendanceRecordUploadRequest();
        req.setUserid(oaUserId);
        req.setDeviceId(Optional.ofNullable(deviceId).orElse("000"));
        req.setDeviceName(Optional.ofNullable(deviceName).orElse("人脸闸机"));
        // 调试海康事件订阅，没有图片地址，所以暂时传空
//        req.setPhotoUrl("");
        req.setUserCheckTime(System.currentTimeMillis());
        OapiAttendanceRecordUploadResponse rsp = null;
        try {
            rsp = client.execute(req, dingTalkOauth2Api.getAccessToken());
        } catch (ApiException e) {
            log.error("ding talk api pushPunchRecords error: {}", e.getErrMsg());
            throw new ServiceException(e.getErrMsg());
        }
        log.info("ding talk api pushPunchRecords: {}", rsp.getBody());
    }
}
