<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rutong.medical.admin.mapper.alarm.AlarmDetailMapper">
    <resultMap type="com.rutong.medical.admin.entity.alarm.AlarmDetail" id="AlarmDetailResult">
        <result property="id" column="id" />
        <result property="deviceBaseStationId" column="device_base_station_id" />
        <result property="userId" column="user_id" />
        <result property="deviceId" column="device_id" />
        <result property="deviceSn" column="device_sn" />
        <result property="businessCode" column="business_code" />
        <result property="userName" column="user_name" />
        <result property="alarmAwy" column="alarm_awy" />
        <result property="alarmType" column="alarm_type" />
        <result property="disposeState" column="dispose_state" />
        <result property="spaceId" column="space_id" />
        <result property="reportTime" column="report_time" />
        <result property="disposeTime" column="dispose_time" />
    </resultMap>

    <sql id="selectAlarmDetailVo">
        select id, device_base_station_id, user_id, device_id, device_sn, business_code, user_name, alarm_awy, alarm_type, dispose_state, space_id, report_time, dispose_time from alarm_detail
    </sql>

</mapper>