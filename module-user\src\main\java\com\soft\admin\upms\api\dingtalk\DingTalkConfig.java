package com.soft.admin.upms.api.dingtalk;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Description 钉钉配置
 * @Date 0009, 2023年8月9日 8:56
 * <AUTHOR>
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "oa.ding-talk")
public class DingTalkConfig {
    private Boolean enable;
    private String appKey;
    private String appSecret;
    private String serverUrl;
    private String appUrl;
    private String serverApiUrl;
    /**
     * 流式消息是否开启
     */
    private boolean streamEnable = false;
    /**
     * http回调加密key
     */
    private String httpCallbackAesKey;
    /**
     * http回调token
     */
    private String httpCallbackToken;
    /**
     * 应用id
     */
    private Long agentId;


    public String getServerUrl(String apiUrl) {
        return getServerUrl() + apiUrl;
    }
}
