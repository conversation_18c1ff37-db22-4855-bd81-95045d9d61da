package com.soft.admin.upms.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.soft.admin.upms.dao.SysConfigMapper;
import com.soft.admin.upms.dto.SysConfigDTO;
import com.soft.admin.upms.job.SysTaskInfoService;
import com.soft.admin.upms.model.SysConfig;
import com.soft.admin.upms.service.SysConfigService;
import com.soft.admin.upms.vo.SysConfigVO;
import com.soft.common.core.base.dao.BaseDaoMapper;
import com.soft.common.core.base.service.BaseService;
import com.soft.common.core.object.MyRelationParam;
import com.soft.common.core.util.MyModelUtil;
import com.soft.common.core.util.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;


/**
 * 系统参数配置标Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-14
 */
@Service
public class SysConfigServiceImpl extends BaseService<SysConfig, Long> implements SysConfigService {
    @Resource
    private SysConfigMapper sysConfigMapper;
    @Resource
    private SysTaskInfoService sysTaskInfoService;

    @Override
    protected BaseDaoMapper<SysConfig> mapper() {
        return sysConfigMapper;
    }

    @Override
    public List<SysConfig> list(SysConfig sysConfig) {
        List<SysConfig> list = getProjectList(sysConfig);
        int batchSize = list instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(list, MyRelationParam.normal(), batchSize);
        return list;
    }

    private List<SysConfig> getProjectList(SysConfig sysConfig) {
        QueryWrapper<SysConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_show", 0);
        List<SysConfig> list = sysConfigMapper.selectList(queryWrapper);
        return list;
    }

    @Override
    public boolean update(SysConfig sysConfig, SysConfig originalSysDept) {
        MyModelUtil.fillCommonsForUpdate(sysConfig, originalSysDept);

        if (sysConfigMapper.updateById(sysConfig) == 0) {
            return false;
        }
        return true;
    }

    @Override
    public String getValue(String key) {
        SysConfig sysConfig = sysConfigMapper.selectOne(new LambdaQueryWrapper<SysConfig>().eq(SysConfig::getConfigKey, key));
        return sysConfig == null ? null : sysConfig.getKeyValue();
    }

    @Override
    public boolean getBooleanValue(String key) {
        SysConfig sysConfig = sysConfigMapper.selectOne(new LambdaQueryWrapper<SysConfig>().eq(SysConfig::getConfigKey, key));
        return sysConfig != null && Boolean.valueOf(sysConfig.getKeyValue());
    }

    @Override
    public SysConfigVO getSysConfig(SysConfigDTO sysConfigDTO) {
        SysConfig sysConfig = sysConfigMapper.selectOne(new LambdaQueryWrapper<SysConfig>().eq(SysConfig::getConfigKey, sysConfigDTO.getConfigKey()));
        return MyModelUtil.copyTo(sysConfig, SysConfigVO.class);
    }
}
