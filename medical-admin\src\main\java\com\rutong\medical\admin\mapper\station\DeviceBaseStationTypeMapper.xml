<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rutong.medical.admin.mapper.station.DeviceBaseStationTypeMapper">
    <resultMap type="com.rutong.medical.admin.entity.station.DeviceBaseStationType" id="DeviceBaseStationTypeResult">
        <result property="id" column="id" />
        <result property="typeCode" column="type_code" />
        <result property="typeName" column="type_name" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
        <result property="isDelete" column="is_delete" />
    </resultMap>

    <sql id="selectDeviceBaseStationTypeVo">
        select id, type_code, type_name, create_user_id, create_time, update_user_id, update_time, is_delete from device_base_station_type
    </sql>
    
</mapper>