package com.soft.admin.upms.api.dingtalk;

/**
 * @Description //TODO
 * @Date 0017, 2023年8月17日 16:10
 * <AUTHOR>
 **/
public enum DingTalkEventEnums {
//                        org_dept_remove

    DEPT_CREATE("org_dept_create"),

    DEPT_MODIFY("org_dept_modify"),

    USER_ADD_ORG("user_add_org"),

    USER_MODIFY_ORG("user_modify_org"),

    USER_LEAVE_ORG("user_leave_org"),

    NO_HANDLER("");

    private String value;

    DingTalkEventEnums(String value) {
        this.value = value;
    }

    public static DingTalkEventEnums getByValue(String value) {
        for (DingTalkEventEnums dingTalkEventEnums : values()) {
            if(value.equals(dingTalkEventEnums.value)) {
                return dingTalkEventEnums;
            }
        }
        return NO_HANDLER;
    }
}
