<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>module-common</artifactId>
        <groupId>com.soft</groupId>
        <version>1.0.0</version>
    </parent>

    <artifactId>common-wx</artifactId>
    <version>1.0.0</version>
    <name>common-wx</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-core</artifactId>
            <version>1.0.0</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.redisson</groupId>-->
<!--            <artifactId>redisson</artifactId>-->
<!--            <version>${redisson.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.apache.httpcomponents</groupId>-->
<!--            <artifactId>httpcore</artifactId>-->
<!--        </dependency>-->
    </dependencies>

</project>