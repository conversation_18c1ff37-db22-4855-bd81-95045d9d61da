package com.soft.admin.upms.dto.dintalk;

import com.taobao.api.internal.mapping.ApiField;
import lombok.*;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * @program: snow
 * @description 发送信息实体
 * @author: 没用的阿吉
 * @create: 2021-03-01 16:03
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class OaSendMessageDTO implements Serializable {

    private static final long serialVersionUID = 9148998626002082665L;


    /**
     * 业务类型
     */
    private String busiType;

    /**
     * 消息id
     */
    private Long busiId;

    /**
     * 通知标题
     */
    private String head;


    /**
     * 内容标题
     */
    private String title;

    /**
     * 内容列表 比如 : 创建人: 张三; 显示的内容会将列表转换
     */
    private List<Form> contentList;

    /**
     * 单行内容
     */
    private String content;

    /**
     * 发送人
     */
    private String from;

    /**
     * 接收人set集合
     */
    private Set<String> receiverSet;

    /**
     * pc端url
     */
    private String pcUrl;

    /**
     * app端url
     */
    private String appUrl;

    /**
     * 详情链接的标题
     */
    private String singleTitle;

    /**
     * 外部消息id
     */
    private String messageOutsideId;

    /**
     * 任务ID
     */
    private Long taskId;

    public static class Form  implements Serializable{
        private static final long serialVersionUID = 5497629143915434225L;
        @ApiField("key")
        private String key;
        @ApiField("value")
        private String value;

        public Form() {
        }

        public String getKey() {
            return this.key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getValue() {
            return this.value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        @Override
        public String toString() {
            return key + "：" + value;
        }
    }

}
