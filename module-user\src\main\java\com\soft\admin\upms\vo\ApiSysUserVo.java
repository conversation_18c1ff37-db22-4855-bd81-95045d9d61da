package com.soft.admin.upms.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

@ApiModel("ApiSysUserVo视图对象")
@Data
public class ApiSysUserVo {

    private Long userId;
    private String showName;
    /**
     * 用户部门Id。
     */
    private Long deptId;
    /**
     *
     */
    private String facePicture;

    /**
     *
     */
    private String oneCardNo;
    private Integer userStatus;
    private Integer deletedFlag;
    private Date createTime;
    private String loginName;
    private String phone;

}
