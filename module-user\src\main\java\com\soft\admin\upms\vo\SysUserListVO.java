package com.soft.admin.upms.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.soft.common.core.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.*;

@Data
public class SysUserListVO {

    /**
     * 用户Id。
     */
    @ApiModelProperty(value = "用户Id")
    private Long userId;

    /**
     * 登录用户名。
     */
    @ApiModelProperty(value = "登录用户名")
    private String loginName;

    /**
     * 用户显示名称。
     */
    @ApiModelProperty(value = "用户显示名称")
    private String showName;


    @ApiModelProperty(value = "性别，1男，2女，3未知")
    private Integer sex;

    /**
     * 用户手机号码
     */
    @ApiModelProperty(value = "用户手机号码")
    private String phone;

    /**
     * 头像
     */
    @ApiModelProperty(value = "头像")
    private String photoUrl;
    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String employeeNumber;

    /**
     * 分机号
     */
    @ApiModelProperty(value = "分机号")
    private String extensionNumber;

    /**
     * 手机短号
     */
    @ApiModelProperty(value = "手机短号")
    private String shortPhone;


    /**
     * 用户类型(0: 管理员 1: 系统管理用户 2: 系统业务用户)。
     */
    @ApiModelProperty(value = "用户类型(0: 管理员 1: 系统管理用户 2: 系统业务用户)")
    private Integer userType;

    /**
     * 用户状态(0: 正常 1: 锁定)。
     */
    @ApiModelProperty(value = "用户状态(0: 正常 1: 锁定)")
    private Integer userStatus;

    /**
     * 创建时间。
     */
    @ApiModelProperty(value = "创建时间")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private Date createTime;


    // 最后登录时间
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private Date lastLoginTime;


    @ApiModelProperty("角色名称")
    private String roleNames;

    @ApiModelProperty("部门Id")
    private Long deptId;

    @ApiModelProperty("部门名称")
    private String deptName;

    @ApiModelProperty("终端信息")
    private String deviceSn;

    @ApiModelProperty("部门名称")
    private String deptNames;

    @ApiModelProperty("部门名称列表")
    private List<String> deptNameList;

    public void setDeptNames(String deptNames) {
        if (StringUtils.isNotEmpty(deptNames)) {
            String[] split = deptNames.split(",");
            this.deptNameList = Arrays.asList(split);
        }
    }

    @ApiModelProperty("岗位名称")
    private String postNames;

    @ApiModelProperty("岗位部门名称")
    private List<PostDept> postDeptList;

    public void setPostNames(String postNames) {
        this.postNames = postNames;
        if (StringUtils.isNotEmpty(postNames)) {
            List<PostDept> postDeptList = new ArrayList<>();

            StringTokenizer tokenizer = new StringTokenizer(postNames, ",");
            while (tokenizer.hasMoreTokens()) {
                String token = tokenizer.nextToken();
                String[] deptAndPost = token.split("\\|");
                if (deptAndPost.length == 2) {
                    String deptName = deptAndPost[0];
                    String postName = deptAndPost[1];
                    postDeptList.add(new PostDept(deptName, postName));
                }
            }
            this.postDeptList = postDeptList;
        }
    }

    @ApiModelProperty("是否管理")
    private String isManager;

    @Data
    class PostDept {
        private String deptName;
        private String postName;

        public PostDept() {
        }

        public PostDept(String deptName, String postName) {
            this.deptName = deptName;
            this.postName = postName;
        }
    }
}
