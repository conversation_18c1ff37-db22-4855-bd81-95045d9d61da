package com.soft.admin.upms.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.admin.upms.vo.ThreePartyRobotVO;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.common.core.base.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 三方机器人配置对象 sp_three_party_robot
 *
 * <AUTHOR>
 * @date 2023-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sp_three_party_robot")
public class ThreePartyRobot extends BaseModel {

    @TableId(value = "id")
    private Long id;

    /**
     * 平台（QW企业微信，DINGDING钉钉）
     */
    private String platform;

    /**
     * 机器人名称
     */
    private String name;

    /**
     * 类型
     */
    private String type;

    /**
     * webhook地址
     */
    private String webHook;

    /**
     * 密钥
     */
    private String secret;

    private String remark;



    @Mapper
    public interface ThreePartyRobotModelMapper extends BaseModelMapper<ThreePartyRobotVO, ThreePartyRobot> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        ThreePartyRobot toModel(ThreePartyRobotVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        ThreePartyRobotVO fromModel(ThreePartyRobot entity);
    }

    public static final ThreePartyRobotModelMapper INSTANCE = Mappers.getMapper(ThreePartyRobotModelMapper.class);
}
