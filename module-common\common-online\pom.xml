<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>module-common</artifactId>
        <groupId>com.soft</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>common-online</artifactId>
    <version>1.0.0</version>
    <name>common-online</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-core</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-dict</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-datafilter</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-redis</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-sequence</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-log</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-minio</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-swagger</artifactId>
            <version>1.0.0</version>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>
</project>