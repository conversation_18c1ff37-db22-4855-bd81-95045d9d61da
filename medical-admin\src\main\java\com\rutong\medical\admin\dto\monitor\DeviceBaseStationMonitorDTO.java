package com.rutong.medical.admin.dto.monitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * SmDeviceBaseStationMonitorDTO对象
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@ApiModel("SmDeviceBaseStationMonitorDTO对象")
@Data
public class DeviceBaseStationMonitorDTO {

    @ApiModelProperty(value = "基站监控关联表ID")
    @NotNull(message = "数据验证失败，基站监控关联表ID不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "基站表ID")
    private Long deviceBaseStationId;

    @ApiModelProperty(value = "视频监控表ID")
    private Long deviceMonitorId;

}
