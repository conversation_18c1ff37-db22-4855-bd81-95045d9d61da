<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.SystemMessageMapper">
    <resultMap type="com.soft.admin.upms.model.SystemMessage" id="SystemMessageResult">
        <result property="id" column="id" />
        <result property="title" column="title" />
        <result property="type" column="type" />
        <result property="priority" column="priority" />
        <result property="businessId" column="business_id" />
        <result property="businessCode" column="business_code" />
        <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="columns">
        `id`, `title`, `type`, `priority`, `business_id`, `business_code`, `create_time`
    </sql>
    <sql id="selectSystemMessageVo">
        select <include refid="columns"/> from sp_system_message
    </sql>
    <insert id="batchInsert">
        insert into
            sp_system_message(<include refid="columns"/>)
        values
            <foreach collection="list" item="record" separator=",">
                (
                 #{record.id},
                 #{record.title},
                 #{record.type},
                 #{record.priority},
                 #{record.businessId},
                 #{record.businessCode},
                 #{record.createTime}
                )
            </foreach>
    </insert>
    <select id="selectByIds" resultMap="SystemMessageResult">
        <include refid="selectSystemMessageVo"/>
        where `id` in
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </select>

</mapper>