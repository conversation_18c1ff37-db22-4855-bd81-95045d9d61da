package com.soft.admin.upms.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.admin.upms.dao.NoticeMapper;
import com.soft.admin.upms.dao.NoticeUserRelationMapper;
import com.soft.admin.upms.dto.NoticeQueryDTO;
import com.soft.admin.upms.dto.NoticeSaveDTO;
import com.soft.admin.upms.model.Notice;
import com.soft.admin.upms.model.NoticeUserRelation;
import com.soft.admin.upms.service.NoticeService;
import com.soft.admin.upms.vo.NoticeVO;
import com.soft.common.core.base.dao.BaseDaoMapper;
import com.soft.common.core.base.service.BaseService;
import com.soft.common.core.object.TokenData;
import com.soft.common.core.util.MyModelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 公告通知Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-03
 */
@Service
public class NoticeServiceImpl extends BaseService<Notice, Long> implements NoticeService {

    @Autowired
    private NoticeMapper noticeMapper;

    @Autowired
    private NoticeUserRelationMapper noticeUserRelationMapper;

    @Override
    protected BaseDaoMapper<Notice> mapper() {
        return noticeMapper;
    }

    @Override
    public List<NoticeVO> queryList(NoticeQueryDTO queryDTO) {
        if (TokenData.takeFromRequest() != null) {
            // 获取当前登录用户
            queryDTO.setUserId(TokenData.takeFromRequest().getUserId().toString());
        }
        return noticeMapper.queryList(queryDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(NoticeSaveDTO saveDTO) {
        Notice notice = MyModelUtil.copyTo(saveDTO, Notice.class);
        if (saveDTO.getId() != null) {
            // 编辑
            noticeMapper.updateById(notice);

            if ("通知".equals(notice.getType())) {
                noticeUserRelationMapper.delete(
                        new LambdaQueryWrapper<NoticeUserRelation>().eq(NoticeUserRelation::getNoticeId, notice.getId())
                );
            }
        } else {
            // 新增
            noticeMapper.insert(notice);
        }

        // 如果是通知则新增公告通知与用户关联信息
        if ("通知".equals(notice.getType()) && CollectionUtil.isNotEmpty(saveDTO.getUserIdList())) {
            List<NoticeUserRelation> relationList = new ArrayList<>();
            for (Long userId : saveDTO.getUserIdList()) {
                NoticeUserRelation noticeUserRelation = new NoticeUserRelation();
                noticeUserRelation.setNoticeId(notice.getId());
                noticeUserRelation.setUserId(userId);
                relationList.add(noticeUserRelation);
            }
            noticeUserRelationMapper.batchInsert(relationList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id, String type) {
        noticeMapper.deleteById(id);
        if ("通知".equals(type)) {
            noticeUserRelationMapper.delete(
                    new LambdaQueryWrapper<NoticeUserRelation>().eq(NoticeUserRelation::getNoticeId, id)
            );
        }
    }

    @Override
    public void updateStatus(Long id, Integer status) {
        Notice notice = new Notice();
        notice.setId(id);
        notice.setStatus(status);
        noticeMapper.updateById(notice);
    }

}
