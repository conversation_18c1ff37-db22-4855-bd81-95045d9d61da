<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rutong.medical.admin.mapper.device.DeviceTerminalTypeMapper">
    <resultMap type="com.rutong.medical.admin.entity.device.DeviceTerminalType" id="SmDeviceTerminalTypeResult">
        <result property="id" column="id" />
        <result property="typeCode" column="type_code" />
        <result property="typeName" column="type_name" />
        <result property="parentId" column="parent_id" />
        <result property="pathId" column="path_id" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
        <result property="isDelete" column="is_delete" />
    </resultMap>

    <sql id="selectSmDeviceTerminalTypeVo">
        select id, type_code, type_name, parent_id, path_id, create_user_id, create_time, update_user_id, update_time, is_delete from sm_device_terminal_type
    </sql>
    
</mapper>