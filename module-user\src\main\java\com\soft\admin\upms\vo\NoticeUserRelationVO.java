package com.soft.admin.upms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * NoticeUserRelationVO视图对象
 *
 * <AUTHOR>
 * @date 2023-07-03
 */
@ApiModel("NoticeUserRelationVO视图对象")
@Data
public class NoticeUserRelationVO {

    @ApiModelProperty(value = "用户id")
    private Long userId;


    @ApiModelProperty(value = "公告通知id")
    private Long noticeId;

}
