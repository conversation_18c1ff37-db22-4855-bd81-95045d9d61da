package com.soft.admin.upms.service;

import com.soft.admin.upms.enums.OaTypeEnums;

import java.util.List;

/**
 * @Description OA系统同步Service
 * @Date 0009, 2023年8月9日 15:08
 * <AUTHOR>
 **/
public interface OaSyncService {
    /**
     * 初始化参数
     */
    void init();

    /**
     * 同步全部（授权范围内）
     * 1、部门
     * 2、用户信息，这里同步的用户信息，只能同步对方oa目前能获取到的，如果用户已经离职是无法获取到信息的；
     * 离职人员通过消息推送可以获取到信息，如果服务消费信息失败，那么可以通过系统的手动删除用户信息完成操作
     */
    void syncAll(OaTypeEnums oaType);


    /**
     * 根据oa部门id列表同步部门
     *
     * @param oaDeptIds
     */
    void syncDeptByOaDeptIdsCallback(OaTypeEnums oaType, List<String> oaDeptIds);

    /**
     * 根据oa用户id处理离职用户
     * @param oaUserIds
     */
    void leaveUserByOaUserIds(OaTypeEnums oaType, List<String> oaUserIds);

    /**
     * 根据oa部门id列表同步用户
     *
     * @param oaDeptIds
     */
    void syncUserByOaDeptIds(OaTypeEnums oaType, List<String> oaDeptIds);

    /**
     * 根据oa用户id列表同步用户
     *
     * @param oaUserIds
     */
    void syncUserByOaUserIds(OaTypeEnums oaType, List<String> oaUserIds);
}
