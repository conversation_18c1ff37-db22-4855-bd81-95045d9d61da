package com.soft.admin.upms.model.message;

import cn.hutool.core.date.DateUtil;
import com.soft.admin.upms.api.dingtalk.enums.DingTalkMessageType;
import com.soft.admin.upms.enums.MessageRecordBusiTypeEnums;
import com.soft.admin.upms.enums.MessageRecordTypeEnums;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class MessageRecordContentNoticeContingencyTemplate extends MessageRecordContentNoticeTemplate {

    private static final MessageRecordBusiTypeEnums BUSI_TYPE = MessageRecordBusiTypeEnums.CONTINGENCY;


    /**
     * 危化品审批
     *
     * @param approveNo      领用编号
     * @param busiId         业务id
     * @param level          优先级
     * @param sendUserId     发送人
     * @param receiveUserIds 接收人
     * @return
     */
    public MessageRecordContent pushExamine(String approveNo, Long busiId, String level, Long sendUserId, List<Long> receiveUserIds) {
        messageRecordContent.setTitle("危化品待审批");
        messageRecordContent.setBusiId(busiId);
        messageRecordContent.setLevel(level);
        messageRecordContent.setContent("您有一条新的危化品领用申请" + approveNo + "待审批，请及时处理！");
        messageRecordContent.setSendUserId(sendUserId);
        messageRecordContent.setReceiveUserIds(receiveUserIds);
        messageRecordContent.setHyperlink("/contingency/hazardousReceive/detail?id=${id}&type=1".replace("${id}", busiId.toString()));
        messageRecordContent.setType(MessageRecordTypeEnums.TODO);
        messageRecordContent.setBusiType(BUSI_TYPE);
        pushJiGuangMessage(Boolean.FALSE);
        push();
        callback(messageRecordContent);
        return messageRecordContent;
    }


    /**
     * 危化品领用
     *
     * @param approveResult  审批结果
     * @param busiId         业务id
     * @param level          优先级
     * @param sendUserId     发送人
     * @param receiveUserIds 接收人
     * @return
     */
    public MessageRecordContent pushReceive(boolean approveResult, Long busiId, String level, Long sendUserId, Long... receiveUserIds) {
        messageRecordContent.setTitle("危化品领用");
        messageRecordContent.setBusiId(busiId);
        messageRecordContent.setLevel(level);
        messageRecordContent.setContent("您提交的危化品领用申请" + (approveResult ? "审批通过" : "被驳回") + "，请及时查看！");
        messageRecordContent.setSendUserId(sendUserId);
        messageRecordContent.setReceiveUserIds(Stream.of(receiveUserIds).collect(Collectors.toList()));
        messageRecordContent.setHyperlink("/contingency/hazardousReceive/detail?id=${id}&type=0".replace("${id}", busiId.toString()));
        messageRecordContent.setType(TYPE);
        messageRecordContent.setBusiType(BUSI_TYPE);
        messageRecordContent.setDingTalkMessageType(DingTalkMessageType.MARKDOWN);
        pushJiGuangMessage(Boolean.FALSE);
        push();
//        callback(messageRecordContent);
        workNotice(messageRecordContent);
        return messageRecordContent;
    }


    /**
     * 应急演练通知
     *
     * @param planName       演练名称
     * @param startTime      演练开始时间
     * @param busiId         业务id
     * @param level          优先级
     * @param sendUserId     发送人
     * @param receiveUserIds 接收人
     * @return 前一天是通知 当前天是待办
     */
    public MessageRecordContent pushNotice(String planName, Date startTime, Long busiId, String level, Long sendUserId, List<Long> receiveUserIds) {
        messageRecordContent.setTitle("演练通知");
        messageRecordContent.setBusiId(busiId);
        messageRecordContent.setLevel(level);
        messageRecordContent.setContent("您有一条新的" + planName + "于" + DateUtil.format(startTime, "yyyy-MM-dd HH:mm") + "开始，请按时参加！");
        messageRecordContent.setSendUserId(sendUserId);
        messageRecordContent.setReceiveUserIds(receiveUserIds);
        messageRecordContent.setHyperlink("/contingency/drillPlan/detail?id=${id}".replace("${id}", busiId.toString()));
        messageRecordContent.setType(TYPE);
        messageRecordContent.setBusiType(BUSI_TYPE);
        messageRecordContent.setDingTalkMessageType(DingTalkMessageType.MARKDOWN);
        pushJiGuangMessage(Boolean.FALSE);
        push();
//        callback(messageRecordContent);
        workNotice(messageRecordContent);
        return messageRecordContent;
    }


    /**
     * 应急演练开始签到
     *
     * @param planName       演练名称
     * @param busiId         业务id
     * @param level          优先级
     * @param sendUserId     发送人
     * @param receiveUserIds 接收人
     * @return
     */
    public MessageRecordContent pushStart(String planName, Long busiId, String level, Long sendUserId, List<Long> receiveUserIds) {
        messageRecordContent.setTitle("签到提醒");
        messageRecordContent.setBusiId(busiId);
        messageRecordContent.setLevel(level);
        messageRecordContent.setContent(planName + "已开始，请及时签到！");
        messageRecordContent.setSendUserId(sendUserId);
        messageRecordContent.setReceiveUserIds(receiveUserIds);
        messageRecordContent.setHyperlink("/contingency/drillPlan/detail?id=${id}".replace("${id}", busiId.toString()));
        messageRecordContent.setType(MessageRecordTypeEnums.TODO);
        messageRecordContent.setBusiType(BUSI_TYPE);
        pushJiGuangMessage(Boolean.FALSE);
        push();
        callback(messageRecordContent);
        return messageRecordContent;
    }

    /**
     * 应急处置
     *
     * @param eventName          事件名称
     * @param eventSpaceFullName 事件地点
     * @param eventTime          事件发生时间
     * @param busiId             业务id
     * @param level              优先级
     * @param receiveUserIds     接收人
     * @return
     */
    public MessageRecordContent pushHandle(String eventName, String eventSpaceFullName, Date eventTime, Long busiId, String level, Long sendUserId, List<Long> receiveUserIds) {
        messageRecordContent.setTitle(eventName);
        messageRecordContent.setBusiId(busiId);
        messageRecordContent.setLevel(level);
        messageRecordContent.setContent(eventSpaceFullName + "于" + DateUtil.format(eventTime, "yyyy-MM-dd HH:mm") + "发生" + eventName + "，请及时处理！");
        messageRecordContent.setSendUserId(sendUserId);
        messageRecordContent.setReceiveUserIds(receiveUserIds);
        messageRecordContent.setHyperlink("/pages/warning/detail?id=${id}".replace("${id}", busiId.toString()));
        messageRecordContent.setType(MessageRecordTypeEnums.TODO);
        messageRecordContent.setBusiType(BUSI_TYPE);
        pushJiGuangMessage(Boolean.FALSE);
        push();
        callback(messageRecordContent);
        return messageRecordContent;
    }
}
