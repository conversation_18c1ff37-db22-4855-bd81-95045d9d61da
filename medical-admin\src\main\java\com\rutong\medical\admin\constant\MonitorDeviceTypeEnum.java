package com.rutong.medical.admin.constant;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Getter
public enum MonitorDeviceTypeEnum {
    DA_HUA_MONITOR("大华", "DA_HUA_MONITOR"),
    DA_HUA_MONITOR_V5("大华V5", "DA_HUA_MONITOR_V5"),
    HIK_MONITOR("海康威视", "HIK_MONITOR"),
    YUSHI_MONITOR("宇视", "YUSHI_MONITOR");

    private final String name;
    private final String code;

    MonitorDeviceTypeEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    /**
     * 获取所有 code 集合
     */
    public static List<String> getAllCodes() {
        return Arrays.stream(values())
                .map(MonitorDeviceTypeEnum::getCode)
                .collect(Collectors.toList());
    }

    public static List<String> getAllNames() {
        return Arrays.stream(values())
                .map(MonitorDeviceTypeEnum::getName)
                .collect(Collectors.toList());
    }

    /**
     * 根据 name 获取对应的 code
     */
    public static Optional<String> getCodeFromName(String name) {
        return Arrays.stream(values())
                .filter(type -> type.getName().equals(name))
                .map(MonitorDeviceTypeEnum::getCode)
                .findFirst();
    }


    /**
     * 根据 code 获取对应的枚举项
     */
    public static Optional<MonitorDeviceTypeEnum> fromCode(String code) {
        return Arrays.stream(values())
                .filter(type -> type.getCode().equals(code))
                .findFirst();
    }

    /**
     * 获取所有枚举项的 Map<code, name>
     */
    public static Map<String, String> getCodeNameMap() {
        return Arrays.stream(values())
                .collect(Collectors.toMap(
                        MonitorDeviceTypeEnum::getCode,
                        MonitorDeviceTypeEnum::getName
                ));
    }
}
