package com.rutong.medical.admin.dto.monitor;

import javax.validation.constraints.NotNull;

import com.rutong.medical.admin.entity.monitor.DeviceMonitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description 设备控制参数DTO
 * @Date 0029, 2023年3月29日 14:22
 * <AUTHOR>
 **/
@ApiModel("设备控制参数DTO")
@Data
public class EquipmentControlParamDTO {
    @NotNull(message = "设备id不可为空")
    @ApiModelProperty("设备id")
    private Long equipmentId;

    @ApiModelProperty(hidden = true)
    private DeviceMonitor deviceMonitor;


    /** ip地址 */
    private String ip;

    /** 是否主码流 */
    @ApiModelProperty("是否主码流")
    private boolean mainStream = true;
}
