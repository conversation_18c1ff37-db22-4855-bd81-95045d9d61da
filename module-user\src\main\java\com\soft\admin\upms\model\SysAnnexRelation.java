package com.soft.admin.upms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.admin.upms.vo.SysAnnexRelationVO;
import com.soft.common.core.base.mapper.BaseModelMapper;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 业务附件关联关系对象 common_sys_annex_relation
 *
 * <AUTHOR>
 * @date 2023-09-19
 */
@Data
@TableName(value = "common_sys_annex_relation")
public class SysAnnexRelation {

    /**
     * 附件表ID
     */
    private Long annexId;

    /**
     * 业务表ID
     */
    private Long busiId;

    /**
     * 业务表
     */
    private String busiTable;


    @Mapper
    public interface SysAnnexRelationModelMapper extends BaseModelMapper<SysAnnexRelationVO, SysAnnexRelation> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        SysAnnexRelation toModel(SysAnnexRelationVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        SysAnnexRelationVO fromModel(SysAnnexRelation entity);
    }

    public static final SysAnnexRelationModelMapper INSTANCE = Mappers.getMapper(SysAnnexRelationModelMapper.class);
}