<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.SysPermMapper">
    <resultMap id="BaseResultMap" type="com.soft.admin.upms.model.SysPerm">
        <id column="perm_id" jdbcType="BIGINT" property="permId"/>
        <result column="module_id" jdbcType="BIGINT" property="moduleId"/>
        <result column="perm_name" jdbcType="VARCHAR" property="permName"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="show_order" jdbcType="INTEGER" property="showOrder"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="getPermListByUserId" resultType="java.lang.String">
        SELECT
            p.url
        FROM
            common_sys_user_role ur,
            common_sys_role_menu rm,
            common_sys_menu_perm_code mpc,
            common_sys_perm_code_perm pcp,
            common_sys_perm p
        <where>
            AND ur.user_id = #{userId}
            AND ur.role_id = rm.role_id
            AND rm.menu_id = mpc.menu_id
            AND mpc.perm_code_id = pcp.perm_code_id
            AND pcp.perm_id = p.perm_id
        </where>
    </select>

    <!-- 以下查询仅用于权限分配的问题定位，由于关联表较多，可能会给系统运行带来性能影响 -->
    <select id="getSysUserListWithDetail" resultType="map">
        SELECT
            u.user_id userId,
            u.login_name loginName,
            u.show_name showName,
            r.role_id roleId,
            r.role_name roleName,
            m.menu_id menuId,
            m.menu_name menuName,
            m.menu_type menuType,
            pc.perm_code_id permCodeId,
            pc.perm_code permCode,
            pc.perm_code_type permCodeType
        FROM
            common_sys_perm_code_perm pcp,
            common_sys_perm_code pc,
            common_sys_menu_perm_code mpc,
            common_sys_menu m,
            common_sys_role_menu rm,
            common_sys_role r,
            common_sys_user_role ur,
            common_sys_user u
        <where>
            AND pcp.perm_id = #{permId}
            AND pcp.perm_code_id = pc.perm_code_id
            AND pcp.perm_code_id = mpc.perm_code_id
            AND mpc.menu_id = m.menu_id
            AND mpc.menu_id = rm.menu_id
            AND rm.role_id = r.role_id
            AND rm.role_id = ur.role_id
            AND ur.user_id = u.user_id
            <if test="loginName != null and loginName != ''">
                AND u.login_name = #{loginName}
            </if>
        </where>
        ORDER BY
            u.user_id, r.role_id, m.menu_id, pc.perm_code_id
    </select>

    <select id="getSysRoleListWithDetail" resultType="map">
        SELECT
            r.role_id roleId,
            r.role_name roleName,
            m.menu_id menuId,
            m.menu_name menuName,
            m.menu_type menuType,
            pc.perm_code_id permCodeId,
            pc.perm_code permCode,
            pc.perm_code_type permCodeType
        FROM
            common_sys_perm_code_perm pcp,
            common_sys_perm_code pc,
            common_sys_menu_perm_code mpc,
            common_sys_menu m,
            common_sys_role_menu rm,
            common_sys_role r
        <where>
            AND pcp.perm_id = #{permId}
            AND pcp.perm_code_id = pc.perm_code_id
            AND pcp.perm_code_id = mpc.perm_code_id
            AND mpc.menu_id = m.menu_id
            AND mpc.menu_id = rm.menu_id
            AND rm.role_id = r.role_id
            <if test="roleName != null and roleName != ''">
                AND r.role_name = #{roleName}
            </if>
        </where>
        ORDER BY
            r.role_id, m.menu_id, pc.perm_code_id
    </select>

    <select id="getSysMenuListWithDetail" resultType="map">
        SELECT
            m.menu_id menuId,
            m.menu_name menuName,
            m.menu_type menuType,
            pc.perm_code_id permCodeId,
            pc.perm_code permCode,
            pc.perm_code_type permCodeType
        FROM
            common_sys_perm_code_perm pcp,
            common_sys_perm_code pc,
            common_sys_menu_perm_code mpc,
            common_sys_menu m
        <where>
            AND pcp.perm_id = #{permId}
            AND pcp.perm_code_id = pc.perm_code_id
            AND pcp.perm_code_id = mpc.perm_code_id
            AND mpc.menu_id = m.menu_id
            <if test="menuName != null and menuName != ''">
                AND m.menu_name = #{menuName}
            </if>
        </where>
        ORDER BY
            m.menu_id, pc.perm_code_id
    </select>
</mapper>
