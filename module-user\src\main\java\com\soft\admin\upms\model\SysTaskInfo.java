package com.soft.admin.upms.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * SysUser实体对象。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
@Data
@TableName(value = "common_sys_task_info")
public class SysTaskInfo {

    /**
     * id。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 任务调度描述。
     */
    private String jobDesc;

    /**
     * 任务类型(1系统任务 2自定义)。
     */
    private Integer jobType;

    /**
     * 调度状态：0-停止，1-运行。
     */
    private Integer triggerStatus;

    /**
     * 执行任务表Id。
     */
    private String jobIds;

    /**
     * 业务表名称。
     */
    private String refTable;

    /**
     *	业务表Id
     **/
    private String refId;

    /**
     *	创建时间
     **/
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

}
