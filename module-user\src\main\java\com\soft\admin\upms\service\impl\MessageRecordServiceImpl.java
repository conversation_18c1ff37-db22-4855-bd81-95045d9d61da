package com.soft.admin.upms.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.soft.admin.upms.job.PushMsgJob;
import org.apache.commons.compress.utils.Lists;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.soft.admin.upms.dao.MessageRecordMapper;
import com.soft.admin.upms.dao.SystemMessageMapper;
import com.soft.admin.upms.dao.SystemMessageUserRelationMapper;
import com.soft.admin.upms.dto.message.*;
import com.soft.admin.upms.enums.MessageRecordBusiTypeEnums;
import com.soft.admin.upms.enums.MessageRecordTypeEnums;
import com.soft.admin.upms.model.SysUser;
import com.soft.admin.upms.model.SystemMessage;
import com.soft.admin.upms.model.SystemMessageUserRelation;
import com.soft.admin.upms.model.message.MessageRecord;
import com.soft.admin.upms.model.message.MessageRecordContent;
import com.soft.admin.upms.service.MessageRecordService;
import com.soft.admin.upms.service.SysUserService;
import com.soft.admin.upms.vo.SystemMessageVO;
import com.soft.admin.upms.vo.message.MessageRecordUnreadCountVO;
import com.soft.admin.upms.vo.message.MessageRecordUserVO;
import com.soft.admin.upms.vo.message.MessageRecordVO;
import com.soft.common.core.constant.GlobalDeletedFlag;
import com.soft.common.core.exception.MyRuntimeException;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.TokenData;
import com.soft.common.core.util.MyPageUtil;
import com.soft.common.core.util.StringUtils;

import cn.hutool.core.collection.CollectionUtil;

/**
 * 消息中心记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-27
 */
@Service
public class MessageRecordServiceImpl extends ServiceImpl<MessageRecordMapper, MessageRecord>
    implements MessageRecordService {

    @Resource
    private MessageRecordMapper messageRecordMapper;
    @Resource
    private SimpMessagingTemplate simpMessagingTemplate;

    @Resource
    private SystemMessageMapper systemMessageMapper;

    @Resource
    private SystemMessageUserRelationMapper systemMessageUserRelationMapper;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private PushMsgJob pushMsgJob;

    @Resource
    private ThreadPoolTaskExecutor syncDataTaskExecutor;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> push(MessageRecordContent messageRecordContent) {
        String title = messageRecordContent.getTitle();
        Long busiId = messageRecordContent.getBusiId();
        String level = messageRecordContent.getLevel();
        String content = messageRecordContent.getContent();
        Long sendUserId = messageRecordContent.getSendUserId();
        List<Long> receiveUserIds = messageRecordContent.getReceiveUserIds();
        String hyperlink = messageRecordContent.getHyperlink();
        MessageRecordTypeEnums type = messageRecordContent.getType();
        MessageRecordBusiTypeEnums busiType = messageRecordContent.getBusiType();

        if (busiId == null) {
            throw new RuntimeException("业务id不能为空！");
        }

        if (CollectionUtil.isEmpty(receiveUserIds)) {
            throw new RuntimeException("消息接收人不能为空！");
        }
        List<Long> receiveUserIdList = new ArrayList<>();

        if ("科室自查".equals(title)) {
            List<Long> finalReceiveUserIdList = receiveUserIdList;
            receiveUserIds.forEach(receiveUserId -> {
                int count = messageRecordMapper.countMessageRecord(receiveUserId, busiId);
                if (count < 1) {
                    finalReceiveUserIdList.add(receiveUserId);
                }
            });
        } else {
            receiveUserIdList = receiveUserIds;
        }
        if (messageRecordContent.getIsExist()) {
            // 如果存在只更新状态
            Date date = new Date();
            MessageRecord messageRecord = messageRecordMapper.selectById(messageRecordContent.getMsgId());
            //messageRecord.setMessageIssueStatus(1);
            messageRecord.setSendDate(messageRecord.getSendDate() == null ? date : messageRecord.getSendDate());
            messageRecord.setSendTime(messageRecord.getSendTime() == null ? date : messageRecord.getSendTime());
            messageRecordMapper.updateById(messageRecord);

            MessageRecordUserVO messageRecordUserVO = new MessageRecordUserVO();
            messageRecordUserVO.setUserId(messageRecord.getReceiveUserId());
            Long count = new LambdaQueryChainWrapper<>(messageRecordMapper)
                .eq(MessageRecord::getReceiveUserId, messageRecord.getReceiveUserId()).eq(MessageRecord::getIsRead, 0)
                .count();
            messageRecordUserVO.setNum(count);
            simpMessagingTemplate.convertAndSend("/topic/msg", JSONObject.toJSONString(messageRecordUserVO));
        } else {
            // 消息表新增
            Date now = new Date();
            List<MessageRecord> messageRecords = receiveUserIdList.stream().map(receiveUserId -> {
                MessageRecord messageRecord = new MessageRecord();
                messageRecord.setTitle(title);
                messageRecord.setLevel(level);
                messageRecord.setType(type);
                messageRecord.setContent(content);
                messageRecord.setBusiId(busiId);
                messageRecord.setBusiType(busiType);
                messageRecord.setHyperlink(hyperlink);
                messageRecord.setSendDate(now);
                messageRecord.setSendTime(now);
                messageRecord.setCreateTime(now);
                messageRecord.setSendUserId(sendUserId);
                messageRecord.setReceiveUserId(receiveUserId);
                messageRecord.setIsRead(0);
                return messageRecord;
            }).collect(Collectors.toList());
            saveBatch(messageRecords);
            receiveUserIdList.forEach(receiveUserId -> {
                MessageRecordUserVO messageRecordUserVO = new MessageRecordUserVO();
                messageRecordUserVO.setUserId(receiveUserId);
                Long count = new LambdaQueryChainWrapper<>(messageRecordMapper)
                    .eq(MessageRecord::getReceiveUserId, receiveUserId).eq(MessageRecord::getIsRead, 0).count();
                messageRecordUserVO.setNum(count);
                simpMessagingTemplate.convertAndSend("/topic/msg", JSONObject.toJSONString(messageRecordUserVO));
            });

        }
        return receiveUserIdList;
    }

    // 小程序待办通知列表
    @Override
    public MyPageData<MessageRecordVO> myList(MessageRecordQueryDTO messageRecordQueryDTO) {
        Integer pageNum = messageRecordQueryDTO.getPageNum();
        Integer pageSize = messageRecordQueryDTO.getPageSize();
        if (pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize);
        }
        Long userId = TokenData.takeFromRequest().getUserId();
        LambdaQueryWrapper<MessageRecord> queryWrapper =
            Wrappers.lambdaQuery(MessageRecord.class).eq(MessageRecord::getReceiveUserId, userId);
        if (messageRecordQueryDTO.getMessageRecordType() != null) {
            queryWrapper.eq(MessageRecord::getType, messageRecordQueryDTO.getMessageRecordType());
        }
        if (messageRecordQueryDTO.getIsRead() != null) {
            queryWrapper.eq(MessageRecord::getIsRead, messageRecordQueryDTO.getIsRead());
        }
        if (StringUtils.isNotEmpty(messageRecordQueryDTO.getDate())) {
            queryWrapper.eq(MessageRecord::getSendDate, messageRecordQueryDTO.getDate());
        }
        // 新增的日期范围查询条件
        if (StringUtils.isNotEmpty(messageRecordQueryDTO.getStartDate())) {
            queryWrapper.ge(MessageRecord::getSendDate, messageRecordQueryDTO.getStartDate());
        }
        if (StringUtils.isNotEmpty(messageRecordQueryDTO.getEndDate())) {
            queryWrapper.le(MessageRecord::getSendDate, messageRecordQueryDTO.getEndDate());
        }

            queryWrapper.orderByDesc(MessageRecord::getCreateTime);
        List<MessageRecord> messageRecords = messageRecordMapper.selectList(queryWrapper);

        if (CollectionUtil.isNotEmpty(messageRecords)) {
            List<MessageRecord> receiveTimeMessageRecords =
                messageRecords.stream().filter(messageRecord -> messageRecord.getReceiveTime() == null)
                    .peek(messageRecord -> messageRecord.setReceiveTime(new Date())).collect(Collectors.toList());
            updateBatchById(receiveTimeMessageRecords);
        }
        return MyPageUtil.makeResponseData(messageRecords, MessageRecord.INSTANCE);
    }

    // 小程序待办通知列表
    @Override
    public MyPageData<SystemMessageVO> managerMessageList(MessageRecordQueryDTO dto) {
        Integer pageNum = dto.getPageNum();
        Integer pageSize = dto.getPageSize();
        if (pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize);
        }
        LambdaQueryWrapper<SystemMessage> queryWrapper = Wrappers.lambdaQuery(SystemMessage.class);
        queryWrapper.eq(dto.getMessageIssueStatus() != null, SystemMessage::getStatus, dto.getMessageIssueStatus());
        // 查询正在生效的巡更计划
        queryWrapper.ge(StringUtils.isNotBlank(dto.getDate()), SystemMessage::getSendTime, dto.getDate() + " 00:00:00");
        queryWrapper.le(StringUtils.isNotBlank(dto.getDate()), SystemMessage::getSendTime, dto.getDate() + " 23:59:59");
        queryWrapper.eq(SystemMessage::getDeletedFlag, GlobalDeletedFlag.NORMAL);
        queryWrapper.orderByDesc(SystemMessage::getCreateTime);
        List<SystemMessage> systemMessages = systemMessageMapper.selectList(queryWrapper);
        return MyPageUtil.makeResponseData(systemMessages, SystemMessage.INSTANCE);
    }

    @Transactional(rollbackFor = Exception.class)
    public void managerMessageAdd(MessageRecordAddDTO dto) {
        Long userId = TokenData.takeFromRequest().getUserId();
        
        if(dto.getId() != null){
            SystemMessage systemMessage = systemMessageMapper.selectById(dto.getId());
            systemMessage.setTitle(dto.getTitle());
            systemMessage.setContent(dto.getContent());
            systemMessage.setAllUser(dto.getAllUser());
            systemMessageMapper.updateById(systemMessage);

            //删除现有关联用户
            LambdaUpdateWrapper<SystemMessageUserRelation>  deleteWrapper = Wrappers.lambdaUpdate();
            deleteWrapper.set(SystemMessageUserRelation::getMessageId, GlobalDeletedFlag.DELETED);
            deleteWrapper.eq(SystemMessageUserRelation::getMessageId, dto.getId());
            systemMessageUserRelationMapper.delete(deleteWrapper);

            insertUserRelation(dto);
        }else{
            SystemMessage systemMessage = new SystemMessage();
            systemMessage.setTitle(dto.getTitle());
            systemMessage.setContent(dto.getContent());
            systemMessage.setAllUser(dto.getAllUser());
            systemMessage.setStatus(0);
            systemMessage.setCreateTime(new Date());
            systemMessage.setCreateUserId(userId);
            systemMessage.setDeletedFlag(GlobalDeletedFlag.NORMAL);
            systemMessageMapper.insert(systemMessage);
            dto.setId(systemMessage.getId());

            insertUserRelation(dto);
        }
    }

    /**
     * 插入用户关联表
     * @param dto
     */
    private void insertUserRelation(MessageRecordAddDTO dto) {
        //插入用户
        if(CollectionUtil.isNotEmpty(dto.getReceiveList())){
            List<SystemMessageUserRelation> receiveList = Lists.newArrayList();
            dto.getReceiveList().stream().forEach(r->{
                SystemMessageUserRelation receive = new SystemMessageUserRelation();
                receive.setMessageId(dto.getId());
                receive.setUserId(r);
                receive.setIsSend(false);
                receive.setIsRead(false);
                receiveList.add(receive);
            });
            systemMessageUserRelationMapper.batchInsert(receiveList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void managerMessageIssue(MessageRecordIssueDTO dto) {
        if(dto.getIsNow() == 0 ){
            long time = dto.getDate().getTime();
            long now = System.currentTimeMillis();
            if(time - now < 600000){
                throw new MyRuntimeException("定时发送的时间需在系统时间10分钟后才能执行!");
            }
        }

        SystemMessage systemMessage = systemMessageMapper.selectById(dto.getId());

        if(systemMessage.getSendType() != null && systemMessage.getSendType() > 0){
            throw new MyRuntimeException("该消息已发送,不允许再次发送");
        }

        systemMessage.setSendType(dto.getIsNow());
        systemMessage.setSendTime(dto.getIsNow() == 1 ? new Date() : dto.getDate());
        systemMessage.setStatus(0);

        systemMessageMapper.updateById(systemMessage);
        if(dto.getIsNow() == 1 ){
            //消息发送中
            systemMessage.setStatus(2);
            systemMessageMapper.updateById(systemMessage);

            syncDataTaskExecutor.execute(() -> {
                try {
                    pushMsgJob.pushMessage(systemMessage);
                } catch (Throwable e) {
                    log.error("发送系统消息发生异常", e);
                    throw e;
                }
            });
        }
    }

    @Override
    public MessageRecordUnreadCountVO unreadCount() {
        Long userId = TokenData.takeFromRequest().getUserId();

        List<MessageRecord> messageRecords = messageRecordMapper.selectList(Wrappers.lambdaQuery(MessageRecord.class)
            .eq(MessageRecord::getReceiveUserId, userId).eq(MessageRecord::getIsRead, 0));
        Long noticeCount = 0L;
        Long alarmCount = 0L;
        Long warningCount = 0L;
        Long todoCount = 0L;
        long total = 0L;
        if (CollectionUtil.isNotEmpty(messageRecords)) {
            Map<MessageRecordTypeEnums, Long> recordTypeEnumsLongMap =
                messageRecords.stream().collect(Collectors.groupingBy(MessageRecord::getType, Collectors.counting()));
            noticeCount = recordTypeEnumsLongMap.get(MessageRecordTypeEnums.NOTICE);
            alarmCount = recordTypeEnumsLongMap.get(MessageRecordTypeEnums.ALARM);
            warningCount = recordTypeEnumsLongMap.get(MessageRecordTypeEnums.WARNING);
            todoCount = recordTypeEnumsLongMap.get(MessageRecordTypeEnums.TODO);
            total = messageRecords.size();
        }
        MessageRecordUnreadCountVO messageRecordUnreadCountVO = new MessageRecordUnreadCountVO();
        messageRecordUnreadCountVO.setNoticeCount(noticeCount);
        messageRecordUnreadCountVO.setAlarmCount(alarmCount);
        messageRecordUnreadCountVO.setWarningCount(warningCount);
        messageRecordUnreadCountVO.setTodoCount(todoCount);
        messageRecordUnreadCountVO.setTotal(total);
        return messageRecordUnreadCountVO;
    }

    @Override
    public MessageRecordVO read(Long id) {
        MessageRecord messageRecord = messageRecordMapper.selectById(id);
        if (messageRecord == null) {
            throw new RuntimeException("消息不存在！");
        }

        Long receiveUserId = messageRecord.getReceiveUserId();
        if (!Objects.equals(receiveUserId, TokenData.takeFromRequest().getUserId())) {
            throw new RuntimeException("当前消息无权访问！");
        }

        if (!Objects.equals(messageRecord.getIsRead(), 1)) {
            messageRecord.setIsRead(1);
            messageRecord.setReadTime(new Date());
            messageRecordMapper.updateById(messageRecord);
        }
        return MessageRecord.INSTANCE.fromModel(messageRecord);
    }

    @Override
    public void readBatch(MessageRecordReadDTO messageRecordReadDTO) {
        MessageRecordTypeEnums messageRecordType = messageRecordReadDTO.getMessageRecordType();
        List<Long> recordIds = messageRecordReadDTO.getRecordIds();
        if (messageRecordType == null) {
            throw new RuntimeException("缺少消息类型参数！");
        }
        update(Wrappers.lambdaUpdate(MessageRecord.class).set(MessageRecord::getReadTime, new Date())
            .set(MessageRecord::getIsRead, 1).eq(MessageRecord::getType, messageRecordType)
            .eq(MessageRecord::getReceiveUserId, TokenData.takeFromRequest().getUserId())
            .in(CollectionUtil.isNotEmpty(recordIds), MessageRecord::getId, recordIds).eq(MessageRecord::getIsRead, 0));
    }

    @Override
    public void delete(MessageRecordDeleteDTO messageRecordDeleteDTO) {
        MessageRecordTypeEnums messageRecordType = messageRecordDeleteDTO.getMessageRecordType();
        if (messageRecordType == null) {
            throw new RuntimeException("缺少消息类型参数！");
        }
        List<Long> recordIds = messageRecordDeleteDTO.getRecordIds();
        if (CollectionUtil.isNotEmpty(recordIds)) {
            messageRecordMapper.deleteBatchIds(recordIds);
        } else {
            messageRecordMapper.delete(Wrappers.lambdaQuery(MessageRecord.class)
                    .eq(MessageRecord::getType, messageRecordType)
                    .eq(MessageRecord::getReceiveUserId, TokenData.takeFromRequest().getUserId()));
        }
    }

    @Override
    public void managerMessageDelete(Long id) {
        systemMessageMapper.update(new SystemMessage(), Wrappers.lambdaUpdate(SystemMessage.class)
                .set(SystemMessage::getDeletedFlag, GlobalDeletedFlag.DELETED)
                .eq(SystemMessage::getId, id));
    }

    @Override
    public SystemMessageVO managerMessageDetail(Long id) {
        SystemMessage systemMessage = systemMessageMapper.selectById(id);
        SystemMessageVO systemMessageVO = SystemMessage.INSTANCE.fromModel(systemMessage);

        List<SystemMessageUserRelation> systemMessageUserRelations = systemMessageUserRelationMapper
                .selectList(Wrappers.lambdaQuery(SystemMessageUserRelation.class).eq(SystemMessageUserRelation::getMessageId, id));
        if(CollectionUtil.isNotEmpty(systemMessageUserRelations)){
            List<Long> userIds = systemMessageUserRelations.stream().map(SystemMessageUserRelation::getUserId).collect(Collectors.toList());
            List<SysUser> userList = sysUserService.listByIds(userIds);
            systemMessageVO.setUserList(SysUser.INSTANCE.fromModelList(userList));
        }

        return systemMessageVO;
    }
}
