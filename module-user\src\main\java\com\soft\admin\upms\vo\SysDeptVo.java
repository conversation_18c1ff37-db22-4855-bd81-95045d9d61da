package com.soft.admin.upms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * SysDeptVO视图对象。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
@ApiModel("SysDeptVO视图对象")
@Data
public class SysDeptVo {

    /**
     * 部门Id。
     */
    @ApiModelProperty(value = "部门Id")
    private Long deptId;


    @ApiModelProperty(value = "部门编号")
    private String deptCode;

    /**
     * 部门路径
     */
    private String deptPath;


    /**
     * 部门名称。
     */
    @ApiModelProperty(value = "部门名称")
    private String deptName;


    @ApiModelProperty(value = "部门描述")
    private String deptDesc;

    /**
     * 显示顺序。
     */
    @ApiModelProperty(value = "显示顺序")
    private Integer showOrder;

    /**
     * 父部门Id。
     */
    @ApiModelProperty(value = "父部门Id")
    private Long parentId;

    /**
     * 创建者Id。
     */
    @ApiModelProperty(value = "创建者Id")
    private Long createUserId;

    /**
     * 更新者Id。
     */
    @ApiModelProperty(value = "更新者Id")
    private Long updateUserId;

    /**
     * 创建时间。
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间。
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
