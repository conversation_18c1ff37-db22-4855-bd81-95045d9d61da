package com.soft.admin.upms.dto;

import com.soft.admin.upms.model.constant.SysUserStatus;
import com.soft.admin.upms.model.constant.SysUserType;
import com.soft.common.core.validator.AddGroup;
import com.soft.common.core.validator.ConstDictRef;
import com.soft.common.core.validator.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * SysUserDto对象。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
@ApiModel("SysUserDto对象")
@Data
public class SysUserDto {

    /**
     * 用户Id。
     */
    @ApiModelProperty(value = "用户Id", required = true)
    @NotNull(message = "数据验证失败，用户Id不能为空！", groups = {UpdateGroup.class})
    private Long userId;

    /**
     * 登录用户名。
     */
    @ApiModelProperty(value = "登录用户名", required = true)
    @NotBlank(message = "数据验证失败，登录用户名不能为空！")
    private String loginName;

    /**
     * 用户密码。
     */
    @ApiModelProperty(value = "用户密码", required = true)
    @NotBlank(message = "数据验证失败，用户密码不能为空！", groups = {AddGroup.class})
    private String password;

    /**
     * 用户显示名称。
     */
    @ApiModelProperty(value = "用户显示名称", required = true)
    @NotBlank(message = "数据验证失败，用户显示名称不能为空！")
    private String showName;


    // 性别：1男，2女，3未知
    private Integer sex;

    /**
     * 组织 id
     */
    @NotNull(message = "数据验证失败，组织不能为空！")
    private Long orgId;

    /**
     * 手机号
     */
    @NotBlank(message = "数据验证失败，手机号码不能为空！")
    private String phone;

    /**
     * 用户部门Id。
     */
    @ApiModelProperty(value = "用户部门Id", required = true)
    @NotNull(message = "数据验证失败，用户部门Id不能为空！")
    private Long deptId;

    /**
     * 用户类型(0: 管理员 1: 系统管理用户 2: 系统业务用户)。
     */
    @ApiModelProperty(value = "用户类型(0: 管理员 1: 系统管理用户 2: 系统业务用户)", required = true)
    @NotNull(message = "数据验证失败，用户类型(0: 管理员 1: 系统管理用户 2: 系统业务用户)不能为空！")
    @ConstDictRef(constDictClass = SysUserType.class, message = "数据验证失败，用户类型(0: 管理员 1: 系统管理用户 2: 系统业务用户)为无效值！")
    private Integer userType;

    /**
     * 用户头像的Url。
     */
    @ApiModelProperty(value = "用户头像的Url")
    private String headImageUrl;

    /**
     * 用户状态(0: 正常 1: 锁定)。
     */
    @ApiModelProperty(value = "用户状态(0: 正常 1: 锁定)", required = true)
    @NotNull(message = "数据验证失败，用户状态(0: 正常 1: 锁定)不能为空！")
    @ConstDictRef(constDictClass = SysUserStatus.class, message = "数据验证失败，用户状态(0: 正常 1: 锁定)为无效值！")
    private Integer userStatus;

    /**
     * 创建者Id。
     */
    @ApiModelProperty(value = "创建者Id")
    private Long createUserId;

    /**
     * 更新者Id。
     */
    @ApiModelProperty(value = "更新者Id")
    private Long updateUserId;

    /**
     * 创建时间。
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间。
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @ApiModelProperty(value = "createTime 范围过滤起始值(>=)")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @ApiModelProperty(value = "createTime 范围过滤结束值(<=)")
    private String createTimeEnd;

    /**
     * 人脸照
     */
    @ApiModelProperty(value = "人脸照")
    private String facePicture;

    /**
     * 一卡通
     */
    @ApiModelProperty(value = "一卡通")
    private String oneCardNo;
    /**
     * 客户标签
     */
    @ApiModelProperty(value = "客户标签")
    private String userTags;
}
