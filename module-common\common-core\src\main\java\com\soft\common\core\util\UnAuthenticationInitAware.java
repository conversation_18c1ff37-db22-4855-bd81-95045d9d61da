package com.soft.common.core.util;

import cn.hutool.core.util.ReUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.soft.common.core.annotation.NoAuthInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 在服务器初始化启动时，获取需要忽略权限校验的请求
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class UnAuthenticationInitAware implements InitializingBean, ApplicationContextAware {

    private static final Pattern PATTERN = Pattern.compile("\\{(.*?)\\}");

    private ApplicationContext applicationContext;

    /**
     * 不需要认证的请求路径
     */
    private final List<String> unAuthenticationUrls = new ArrayList<>();

    private final AntPathMatcher pathMatcher = new AntPathMatcher();


    @Override
    public void afterPropertiesSet() {
        // 获取所有的请求
        RequestMappingHandlerMapping mapping = applicationContext.getBean(RequestMappingHandlerMapping.class);
        Map<RequestMappingInfo, HandlerMethod> map = mapping.getHandlerMethods();

        // key是 {GET /login} 格式的数据
        Set<RequestMappingInfo> requestMappingInfos = map.keySet();
        for (RequestMappingInfo requestMappingInfo : requestMappingInfos) {
            HandlerMethod handlerMethod = map.get(requestMappingInfo);
            // 获取方法上边的注解 替代 path variable 为 *
            NoAuthInterface methodAnnotation = handlerMethod.getMethodAnnotation(NoAuthInterface.class);
            if (methodAnnotation == null) {
                methodAnnotation = handlerMethod.getBeanType().getAnnotation(NoAuthInterface.class);
            }
            saveIgnoreRequest(methodAnnotation, requestMappingInfo);
        }
        log.info("un authentication urls: {}", unAuthenticationUrls);
    }

    private void saveIgnoreRequest(NoAuthInterface noAuthInterface, RequestMappingInfo key) {
        Optional.ofNullable(noAuthInterface)
                .ifPresent(inner -> {
                    assert key.getPatternsCondition() != null;
                    key.getPatternsCondition().getPatterns()
                            // 将请求的 uri添加到集合中
                            .forEach(uri -> this.unAuthenticationUrls.add(ReUtil.replaceAll(uri, PATTERN, StringPool.ASTERISK)));
                });
    }

    public boolean pathMatcher(String url){
        for (String reqUrl : unAuthenticationUrls) {
            if (pathMatcher.match(reqUrl, url)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}