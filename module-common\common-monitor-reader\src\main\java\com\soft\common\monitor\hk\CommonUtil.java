

package com.soft.common.monitor.hk;

import java.time.LocalDateTime;

/**
 * <AUTHOR> @create 2022-03-22-11:13
 */
public class CommonUtil {

    //SDK时间解析
    public static String parseTime(int time) {
        int year=(time>>26)+2000;
        int month=(time>>22)&15;
        int day=(time>>17)&31;
        int hour=(time>>12)&31;
        int min=(time>>6)&63;
        int second=(time)&63;
        return year + "-" + month + "-" + day + "-" + hour + ":" + min + ":" + second;
    }

    public static LocalDateTime parseTime2LocalDateTime(int time) {
        int year=(time>>26)+2000;
        int month=(time>>22)&15;
        int day=(time>>17)&31;
        int hour=(time>>12)&31;
        int min=(time>>6)&63;
        int second=(time)&63;
        return LocalDateTime.of(year, month, day, hour, min, second);
    }
}
