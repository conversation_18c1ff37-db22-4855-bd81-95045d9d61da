package com.rutong.medical.admin.vo.monitor;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;

import lombok.Data;

@Data
@ColumnWidth(value = 20)
@HeadRowHeight(30)
@ContentRowHeight(25)
@ExcelIgnoreUnannotated
public class DeviceMonitorImportVO {

    @ExcelProperty("设备编号")
    private String monitorCode;

    @ExcelProperty("监控名称")
    private String monitorName;

    @ExcelProperty("监控类型")
    private String monitorType;

    @ExcelProperty("ip地址")
    private String ip;

    @ExcelProperty("端口")
    private Long port;

    @ExcelProperty("通道号")
    private String channelNum;

    @ExcelProperty("用户名")
    private String userCode;

    @ExcelProperty("密码")
    private String password;

    @ExcelProperty("生产厂家")
    private String factory;

}
