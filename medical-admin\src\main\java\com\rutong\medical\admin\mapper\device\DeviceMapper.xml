<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rutong.medical.admin.mapper.device.DeviceMapper">
    <resultMap type="com.rutong.medical.admin.entity.device.Device" id="SmDeviceResult">
        <result property="id" column="id" />
        <result property="deviceTerminalTypeId" column="device_terminal_type_id" />
        <result property="deviceCode" column="device_code" />
        <result property="deviceName" column="device_name" />
        <result property="businessCode" column="business_code" />
        <result property="spaceId" column="space_id" />
        <result property="spacePath" column="space_path" />
        <result property="spaceFullName" column="space_full_name" />
        <result property="x" column="x" />
        <result property="y" column="y" />
        <result property="z" column="z" />
        <result property="longitude" column="longitude" />
        <result property="latitude" column="latitude" />
        <result property="isOnline" column="is_online" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
        <result property="isDelete" column="is_delete" />
    </resultMap>

    <sql id="selectSmDeviceVo">
        select id, device_terminal_type_id, device_code, device_name, business_code, space_id, space_path, space_full_name, x, y, z, longitude, latitude, is_online, create_user_id, create_time, update_user_id, update_time, is_delete from sm_device
    </sql>

    <select id="list" resultType="com.rutong.medical.admin.entity.device.Device" parameterType="com.rutong.medical.admin.dto.station.DevicePageQueryDTO">
        select * from sm_device
        <where>
            <!-- 查询未删除的数据 -->
            and is_delete = 1

            <!-- 关键词搜索：基站名称或编号 -->
            <if test="keyWord != null and keyWord != ''">
                and (device_code like concat('%', #{keyWord}, '%')
                or device_name like concat('%', #{keyWord}, '%'))
            </if>

            <!-- 匹配所属楼层路径 -->
            <if test="spacePath != null and spacePath != ''">
                and space_path like concat('%', #{spacePath}, '%')
            </if>

            <if test="businessCode != null and businessCode != ''">
                and business_code like concat('%', #{businessCode}, '%')
            </if>

            <if test="deviceTerminalTypeIdList != null">
                and device_terminal_type_id in
                <foreach item="item" index="index" collection="deviceTerminalTypeIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 在线状态 -->
            <if test="isOnline != null">
                and is_online = #{isOnline}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="existsByDeviceNumberOrName" resultType="int">
        SELECT COUNT(*) FROM sm_device
        WHERE (device_code = #{param1} OR device_name =#{param2}) and is_delete = 1
    </select>


    <select id="existsByDeviceNumberOrNameExcludingId" resultType="int">
        SELECT COUNT(*) FROM sm_device
        WHERE (device_code = #{param1}OR device_name = #{param2})
          AND id != #{param3}  and is_delete = 1
    </select>



    <!-- 更新设备信息 -->
    <update id="updateDeviceById">
        UPDATE sm_device
        <set>
            <!-- 设备分类表ID -->
            <if test="deviceTerminalTypeId != null">device_terminal_type_id = #{deviceTerminalTypeId},</if>

            <!-- 设备编号 -->
            <if test="deviceCode != null">device_code = #{deviceCode},</if>

            <!-- 设备名称 -->
            <if test="deviceName != null">device_name = #{deviceName},</if>

            <!-- 业务系统编号 -->
            <if test="businessCode != null">business_code = #{businessCode},</if>

            <!-- 安装位置 -->
            <if test="spaceId != null">space_id = #{spaceId},</if>

            <!-- 所属楼层路径 -->
            <if test="spacePath != null">space_path = #{spacePath},</if>

            <!-- 所属楼层全名称 -->
            <if test="spaceFullName != null">space_full_name = #{spaceFullName},</if>

            <!-- x -->
            <if test="x != ''">x = #{x},</if>

            <!-- y -->
            <if test="y != ''">y = #{y},</if>

            <!-- z -->
            <if test="z != ''">z = #{z},</if>

            <!-- 经度（强制更新，即使为 null） -->
            <if test="longitude != ''">longitude = #{longitude},</if>

            <!-- 纬度（强制更新，即使为 null） -->
            <if test="latitude != ''">latitude = #{latitude},</if>

            <!-- 在线状态 -->
            <if test="isOnline != null">is_online = #{isOnline},</if>

            <!-- 终端sn -->
            <if test="deviceSn != null">device_sn = #{deviceSn},</if>

            <!-- 更新时间 -->
            <if test="updateTime != null">update_time = #{updateTime},</if>

            <!-- 更新人ID -->
            <if test="updateUserId != null">update_user_id = #{updateUserId},</if>
        </set>
        WHERE id = #{id}
    </update>

</mapper>