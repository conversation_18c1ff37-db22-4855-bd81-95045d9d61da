package com.rutong.medical.admin.vo.alarm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 报警配置VO
 */
@Data
@ApiModel(value = "报警配置VO")
public class SoundLightAlarmVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "报警类型(1:医护工卡,2:报警按钮,3:红外探测器,4:资产定位标签)")
    private Integer configType;

    @ApiModelProperty(value = "报警类型名称")
    private String configTypeName;

    @ApiModelProperty(value = "配置ID")
    private Long id;

    @ApiModelProperty(value = "配置编码")
    private String configCode;

    @ApiModelProperty(value = "配置名称")
    private String configName;

    @ApiModelProperty(value = "配置值")
    private Integer configValue;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
} 