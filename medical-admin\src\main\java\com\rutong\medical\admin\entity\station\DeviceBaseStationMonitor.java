package com.rutong.medical.admin.entity.station;

import com.rutong.medical.admin.vo.station.DeviceBaseStationMonitorVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.mapper.BaseModelMapper;

import lombok.Data;

/**
 * 基站监控关联对象 sm_device_base_station_monitor
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@TableName(value = "sm_device_base_station_monitor")
public class DeviceBaseStationMonitor {

    /**
     * 基站监控关联表ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 基站表ID
     */
    private Long deviceBaseStationId;

    /**
     * 视频监控表ID
     */
    private Long deviceMonitorId;

    @Mapper
    public interface SmDeviceBaseStationMonitorModelMapper
        extends BaseModelMapper<DeviceBaseStationMonitorVO, DeviceBaseStationMonitor> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        DeviceBaseStationMonitor toModel(DeviceBaseStationMonitorVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        DeviceBaseStationMonitorVO fromModel(DeviceBaseStationMonitor entity);
    }

    public static final SmDeviceBaseStationMonitorModelMapper INSTANCE =
        Mappers.getMapper(SmDeviceBaseStationMonitorModelMapper.class);
}
