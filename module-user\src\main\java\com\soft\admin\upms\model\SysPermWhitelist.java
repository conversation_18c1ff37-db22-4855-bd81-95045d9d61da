package com.soft.admin.upms.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 白名单实体对象。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
@Data
@TableName(value = "common_sys_perm_whitelist")
public class SysPermWhitelist {

    /**
     * 权限资源的URL。
     */
    @TableId(value = "perm_url")
    private String permUrl;

    /**
     * 权限资源所属模块名字(通常是Controller的名字)。
     */
    private String moduleName;

    /**
     * 权限的名称。
     */
    private String permName;
}
