package com.soft.common.wx.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

public class WXTicket implements Serializable {
    private static final long serialVersionUID = 9122336326462335907L;

    private String ticket;

    @JsonProperty("expires_in")
    private long expiresIn;

    private long expiresTill;

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    public long getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(long expiresIn) {
        this.expiresIn = expiresIn;
        this.expiresTill = System.currentTimeMillis() + (expiresIn * 1000) - 60000;
    }

    public long getExpiresTill() {
        return expiresTill;
    }

    public boolean expired() {
        return System.currentTimeMillis() > expiresTill;
    }
}