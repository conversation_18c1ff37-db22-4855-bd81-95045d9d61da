package com.rutong.medical.admin.mapper.station;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rutong.medical.admin.dto.station.DeviceBaseStationPageQueryDTO;
import com.rutong.medical.admin.entity.device.Device;
import com.rutong.medical.admin.entity.station.DeviceBaseStation;

import feign.Param;

/**
 * 基站Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface DeviceBaseStationMapper extends BaseMapper<DeviceBaseStation> {

    /**
     * 基站分页查询
     * 
     * @param deviceBaseStationPageQuery
     * @return
     */
    List<DeviceBaseStation> list(DeviceBaseStationPageQueryDTO deviceBaseStationPageQuery);

    /**
     * 设备编号或设备名称是否存在
     * 
     * @param code
     * @param name
     * @return
     */
    int existsByDeviceNumberOrNameUsingMap(
            @Param("deviceBaseStationCode") String deviceBaseStationCode,
            @Param("deviceBaseStationName") String deviceBaseStationName);

    /**
     * 设备编号或设备名称是否存在 不包括自身
     * 
     * @return
     */
    int existsByDeviceNumberOrNameExcludingId(@Param("deviceBaseStationCode") String deviceBaseStationCode,
        @Param("deviceBaseStationName") String deviceBaseStationName, @Param("id") Long id);


    /**
     * 根据更新设备信息
     *
     * @param id
     */
    void updateDeviceBaseStationById(DeviceBaseStation station);
}
