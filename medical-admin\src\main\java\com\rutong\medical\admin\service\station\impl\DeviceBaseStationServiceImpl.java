package com.rutong.medical.admin.service.station.impl;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.soft.common.core.constant.RedisKeyConstant;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.rutong.medical.admin.constant.OnlineStatusEnum;
import com.rutong.medical.admin.dto.station.DeviceBaseStationPageQueryDTO;
import com.rutong.medical.admin.dto.station.DeviceBaseStationSaveDTO;
import com.rutong.medical.admin.entity.station.DeviceBaseStation;
import com.rutong.medical.admin.entity.station.DeviceBaseStationMonitor;
import com.rutong.medical.admin.entity.station.DeviceBaseStationType;
import com.rutong.medical.admin.entity.system.Space;
import com.rutong.medical.admin.mapper.station.DeviceBaseStationMapper;
import com.rutong.medical.admin.mapper.station.DeviceBaseStationMonitorMapper;
import com.rutong.medical.admin.mapper.station.DeviceBaseStationTypeMapper;
import com.rutong.medical.admin.mapper.system.SpaceMapper;
import com.rutong.medical.admin.service.station.DeviceBaseStationMonitorService;
import com.rutong.medical.admin.service.station.DeviceBaseStationService;
import com.rutong.medical.admin.vo.station.DeviceBaseStationVO;
import com.soft.common.core.constant.GlobalDeletedFlag;
import com.soft.common.core.exception.ServiceException;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.TokenData;
import com.soft.common.core.util.MyModelUtil;
import com.soft.common.core.util.MyPageUtil;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 基站Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
@Slf4j
public class DeviceBaseStationServiceImpl extends ServiceImpl<DeviceBaseStationMapper, DeviceBaseStation>
        implements DeviceBaseStationService {

    @Autowired
    private DeviceBaseStationMapper deviceBaseStationMapper;
    @Resource
    private DeviceBaseStationService deviceBaseStationService;
    @Resource
    private DeviceBaseStationTypeMapper deviceBaseStationTypeMapper;
    @Autowired
    private DeviceBaseStationMonitorMapper smDeviceBaseStationMonitorMapper;
    @Resource
    private DeviceBaseStationMonitorService smDeviceBaseStationMonitorService;
    @Resource
    private SpaceMapper spaceMapper;

    @Override
    public MyPageData<DeviceBaseStationVO> page(DeviceBaseStationPageQueryDTO deviceBaseStationPageQuery) {
        Integer pageNum = deviceBaseStationPageQuery.getPageNum();
        Integer pageSize = deviceBaseStationPageQuery.getPageSize();

        // 选择了设备分类，查询分类或者子集
        List<Long> deviceBaseStationTypeIdList = new ArrayList<>();
        if (deviceBaseStationPageQuery.getDeviceBaseStationType() != null) {
            List<DeviceBaseStationType> deviceBaseStationTypes = deviceBaseStationTypeMapper.selectList(Wrappers
                    .lambdaQuery(DeviceBaseStationType.class)
                    .like(DeviceBaseStationType::getPathId, deviceBaseStationPageQuery.getDeviceBaseStationType())
                    .eq(DeviceBaseStationType::getIsDelete, GlobalDeletedFlag.NORMAL).select(DeviceBaseStationType::getId));
            deviceBaseStationTypeIdList =
                    deviceBaseStationTypes.stream().map(DeviceBaseStationType::getId).collect(Collectors.toList());
            deviceBaseStationPageQuery.setDeviceBaseStationTypeIdList(deviceBaseStationTypeIdList);
        }

        // 分页查询基站
        if (pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize);
        }
        List<DeviceBaseStation> deviceBaseStationList = deviceBaseStationMapper.list(deviceBaseStationPageQuery);
        if (CollectionUtils.isEmpty(deviceBaseStationList)) {
            return MyPageData.emptyPageData();
        }

        MyPageData<DeviceBaseStationVO> deviceBaseStationVOMyPageData =
                MyPageUtil.makeResponseData(deviceBaseStationList, DeviceBaseStation.INSTANCE);
        List<DeviceBaseStationVO> dataList = deviceBaseStationVOMyPageData.getDataList();
        if (CollectionUtils.isEmpty(dataList)) {
            return MyPageData.emptyPageData();
        }
        // 提取所有不重复的分类ID
        Set<Long> typeIds = dataList.stream().map(DeviceBaseStationVO::getDeviceBaseStationType)
                .filter(Objects::nonNull).collect(Collectors.toSet());

        // 查询分类名称并封装为Map
        Map<Long, String> typeNameMap = new HashMap<>();
        List<DeviceBaseStationType> deviceBaseStationTypes = deviceBaseStationTypeMapper
                .selectList(Wrappers.lambdaQuery(DeviceBaseStationType.class).in(DeviceBaseStationType::getId, typeIds)
                        .eq(DeviceBaseStationType::getIsDelete, GlobalDeletedFlag.NORMAL));
        typeNameMap = deviceBaseStationTypes.stream()
                .collect(Collectors.toMap(DeviceBaseStationType::getId, DeviceBaseStationType::getTypeName));

        // 转换 VO 并设置 typeName
        Map<Long, String> finalTypeNameMap = typeNameMap;
        dataList.forEach(e -> {
            e.setDeviceBaseStationTypeName(finalTypeNameMap.getOrDefault(e.getDeviceBaseStationType(), ""));
        });
        return deviceBaseStationVOMyPageData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        log.info("delete device base station id:{}", id);
        DeviceBaseStation deviceBaseStation = new DeviceBaseStation();
        deviceBaseStation.setId(id);
        deviceBaseStation.setUpdateTime(new Date());
        deviceBaseStation.setIsDelete(GlobalDeletedFlag.DELETED);
        deviceBaseStation.setUpdateUserId(TokenData.takeFromRequest().getUserId());
        deviceBaseStationMapper.updateById(deviceBaseStation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(DeviceBaseStationSaveDTO deviceBaseStationSave) {
        // 查询地址
        DeviceBaseStation deviceBaseStation = MyModelUtil.copyTo(deviceBaseStationSave, DeviceBaseStation.class);
        List<Long> deviceMonitorIdList = deviceBaseStationSave.getDeviceMonitorIdList();
        Long id = deviceBaseStationSave.getId();
        Long spaceId = deviceBaseStationSave.getSpaceId();
        if (spaceId != null) {
            Space space = spaceMapper.selectById(spaceId);
            if (space != null) {
                deviceBaseStation.setSpacePath(space.getPath());
                deviceBaseStation.setSpaceFullName(space.getFullName());
            }
        }

        // 新增基站
        if (id == null) {
            // 校验名称和编码是否重复
            int i = deviceBaseStationMapper.existsByDeviceNumberOrNameUsingMap(
                    deviceBaseStationSave.getDeviceBaseStationCode(), deviceBaseStationSave.getDeviceBaseStationName());
            if (i > 0) {
                throw new ServiceException("基站编号或基站名称已存在");
            }
            // 新增操作
            deviceBaseStation.setCreateTime(new Date());
            deviceBaseStation.setIsDelete(GlobalDeletedFlag.NORMAL);
            deviceBaseStation.setCreateTime(new Date());
            deviceBaseStation.setIsOnline(OnlineStatusEnum.OFFLINE.getValue());
            deviceBaseStation.setCreateUserId(TokenData.takeFromRequest().getUserId());
            deviceBaseStationService.save(deviceBaseStation);
        } else {
            // 修改操作 校验名称和编码是否重复
            int i = deviceBaseStationMapper.existsByDeviceNumberOrNameExcludingId(
                    deviceBaseStationSave.getDeviceBaseStationCode(), deviceBaseStationSave.getDeviceBaseStationName(),
                    deviceBaseStationSave.getId());
            if (i > 0) {
                throw new ServiceException("基站编号或基站名称已存在");
            }
            // 删除设备关联信息并根据新值重新创建
            DeviceBaseStationMonitor smDeviceBaseStationMonitor = new DeviceBaseStationMonitor();
            smDeviceBaseStationMonitor.setDeviceBaseStationId(deviceBaseStationSave.getId());
            smDeviceBaseStationMonitorMapper.delete(new QueryWrapper<>(smDeviceBaseStationMonitor));
        }
        deviceBaseStation.setUpdateTime(new Date());
        deviceBaseStation.setUpdateUserId(TokenData.takeFromRequest().getUserId());
        deviceBaseStationMapper.updateDeviceBaseStationById(deviceBaseStation);

        // 查询设备关联信息
        List<DeviceBaseStationMonitor> monitorList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(deviceMonitorIdList)) {
            for (Long relationId : deviceMonitorIdList) {
                DeviceBaseStationMonitor baseStationMonitor = new DeviceBaseStationMonitor();
                if (id != null) {
                    baseStationMonitor.setDeviceBaseStationId(deviceBaseStationSave.getId());
                } else {
                    baseStationMonitor.setDeviceBaseStationId(deviceBaseStation.getId());
                }
                baseStationMonitor.setDeviceMonitorId(relationId);
                monitorList.add(baseStationMonitor);
            }
        }
        if (CollectionUtils.isNotEmpty(monitorList)) {
            smDeviceBaseStationMonitorService.saveBatch(monitorList);
        }
    }

    @Override
    public DeviceBaseStationVO detail(Long id) {
        DeviceBaseStation deviceBaseStation = deviceBaseStationMapper.selectById(id);
        if (deviceBaseStation == null) {
            return null;
        }
        // 查询设备关联信息
        DeviceBaseStationVO deviceBaseStationVO = MyModelUtil.copyTo(deviceBaseStation, DeviceBaseStationVO.class);
        List<Long> deviceMonitorIdList = smDeviceBaseStationMonitorMapper
                .selectList(Wrappers.lambdaQuery(DeviceBaseStationMonitor.class)
                        .eq(DeviceBaseStationMonitor::getDeviceBaseStationId, id))
                .stream().map(DeviceBaseStationMonitor::getDeviceMonitorId).collect(Collectors.toList());
        deviceBaseStationVO.setDeviceMonitorIdList(deviceMonitorIdList);
        return deviceBaseStationVO;
    }

    @Override
    public List<DeviceBaseStationVO> list(DeviceBaseStationPageQueryDTO deviceBaseStationPageQuery) {
        List<DeviceBaseStation> deviceBaseStationList = deviceBaseStationMapper.list(deviceBaseStationPageQuery);
        if (CollectionUtils.isEmpty(deviceBaseStationList)) {
            return Collections.emptyList();
        }
        return MyModelUtil.copyCollectionTo(deviceBaseStationList, DeviceBaseStationVO.class);
    }

    @Override
//    @Cacheable(value = RedisKeyConstant.BASEINFO_BASE_STATION, key = "#deviceBaseStationCode",unless = "#result == null")
    public DeviceBaseStation getDeviceBaseStation(String deviceBaseStationCode) {
        DeviceBaseStation deviceBaseStation = null;
        try {
            deviceBaseStation = deviceBaseStationMapper.selectOne(Wrappers.lambdaQuery(DeviceBaseStation.class)
                    .eq(DeviceBaseStation::getDeviceBaseStationCode, deviceBaseStationCode));
        } catch (Exception e) {
            log.error("getDeviceBaseStation error", e);
        }
        return deviceBaseStation;
    }
}
