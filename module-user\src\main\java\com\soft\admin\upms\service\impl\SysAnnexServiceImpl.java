package com.soft.admin.upms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.admin.upms.dao.SysAnnexMapper;
import com.soft.admin.upms.dao.SysAnnexRelationMapper;
import com.soft.admin.upms.dao.SysUserMapper;
import com.soft.admin.upms.model.SysAnnex;
import com.soft.admin.upms.model.SysAnnexRelation;
import com.soft.admin.upms.model.SysUser;
import com.soft.admin.upms.service.SysAnnexService;
import com.soft.admin.upms.vo.SysAnnexVO;
import com.soft.common.core.constant.GlobalDeletedFlag;
import com.soft.common.core.object.FileInfoDTO;
import com.soft.common.core.object.TokenData;
import com.soft.common.core.util.MyModelUtil;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 公共附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-19
 */
@Service
public class SysAnnexServiceImpl extends ServiceImpl<SysAnnexMapper, SysAnnex> implements SysAnnexService {

    @Autowired
    private SysAnnexMapper sysAnnexMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysAnnexRelationMapper sysAnnexRelationMapper;

    @Override
    public SysAnnexVO add(FileInfoDTO fileInfoDTO) {
        SysAnnex annex = new SysAnnex();
        annex.setSize(fileInfoDTO.getFileSize());
        annex.setName(fileInfoDTO.getOriginalFileName());
        annex.setPath(fileInfoDTO.getFilePath());
        annex.setSuffix(fileInfoDTO.getSuffix());
        annex.setDeletedFlag(GlobalDeletedFlag.NORMAL);
        MyModelUtil.fillCommonsForInsert(annex);
        sysAnnexMapper.insert(annex);
        return SysAnnex.INSTANCE.fromModel(annex);
    }

    @Override
    public List<SysAnnexVO> list(List<Long> ids) {
        List<SysAnnexVO> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(ids)) {
            //设备列表
            List<SysAnnex> annexList = sysAnnexMapper.selectList(Wrappers.lambdaQuery(SysAnnex.class)
                    .in(SysAnnex::getId, ids)
                    .eq(SysAnnex::getDeletedFlag, GlobalDeletedFlag.NORMAL)
                    .orderByDesc(SysAnnex::getCreateTime));

            //转换vo
            list = MyModelUtil.copyCollectionTo(annexList, SysAnnexVO.class);
            if (CollectionUtils.isNotEmpty(list)) {
                initUser(list);
            }
        }
        return list;
    }

    @Override
    public List<SysAnnexVO> list(Long busiId, Class clazz) {
        List<SysAnnexRelation> sysAnnexRelations = sysAnnexRelationMapper.selectList(
                new LambdaQueryWrapper<SysAnnexRelation>()
                        .eq(SysAnnexRelation::getBusiTable, MyModelUtil.mapToTableName(clazz))
                        .eq(SysAnnexRelation::getBusiId, busiId)
        );
        List<Long> annexIds = sysAnnexRelations.stream().map(SysAnnexRelation::getAnnexId).collect(Collectors.toList());
        return this.list(annexIds);
    }

    /**
     * 初始化用户信息
     *
     * @param annexVOList
     */
    private void initUser(List<SysAnnexVO> annexVOList) {
        List<Long> userIds = annexVOList.stream().map(SysAnnexVO::getCreateUserId).collect(Collectors.toList());
        //用户
        List<SysUser> sysUserList = sysUserMapper.selectList(Wrappers.lambdaQuery(SysUser.class)
                .in(SysUser::getUserId, userIds)
                .eq(SysUser::getDeletedFlag, GlobalDeletedFlag.NORMAL));

        Map<Long, String> userMap = sysUserList.stream().collect(Collectors.toMap(SysUser::getUserId,
                SysUser::getShowName));

        annexVOList.stream().forEach(a -> {
            a.setCreateUserName(userMap.get(a.getCreateUserId()));
        });
    }

    @Override
    public SysAnnex getAnnex(Long fileId) {
        List<SysAnnex> annexList = sysAnnexMapper.selectList(Wrappers.lambdaQuery(SysAnnex.class)
                .eq(SysAnnex::getId, fileId)
                .eq(SysAnnex::getDeletedFlag, GlobalDeletedFlag.NORMAL));
        return CollectionUtils.isNotEmpty(annexList) ? annexList.get(0) : null;
    }

//    @Override
//    public List<SysAnnexVO> getListByBusi(Long busiId, String busiTable) {
//        List<SysAnnexRelation> relationList = sysAnnexRelationMapper.selectList(
//                new LambdaQueryWrapper<SysAnnexRelation>().eq(SysAnnexRelation::getBusiId, busiId)
//                        .eq(SysAnnexRelation::getBusiTable, busiTable)
//        );
//        List<SysAnnexVO> annexVOList = Lists.newArrayList();
//        if (CollectionUtils.isNotEmpty(relationList)) {
//            List<Long> annexIdList = relationList.stream().map(SysAnnexRelation::getAnnexId).collect(Collectors.toList());
//            annexVOList.addAll(this.list(annexIdList));
//        }
//        return annexVOList;
//    }
}