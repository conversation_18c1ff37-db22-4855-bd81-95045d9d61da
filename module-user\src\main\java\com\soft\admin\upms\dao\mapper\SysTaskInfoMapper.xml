<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.SysTaskInfoMapper">

	<select id="getListByIds" resultType="com.soft.admin.upms.model.SysTaskInfo">
		select job_desc,job_type,trigger_status,job_ids,ref_table,ref_id,create_time from common_sys_task_info where id in
		<foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
			#{id}
		</foreach>
	</select>

	<select id="getModelByParams" resultType="com.soft.admin.upms.model.SysTaskInfo">
		select id,job_desc,job_type,trigger_status,job_ids,ref_table,ref_id,create_time from common_sys_task_info where ref_table=#{refTable} and ref_id=#{refId}
		LIMIT 1
	</select>
	
	<update id="updateTriggerStatus">
		UPDATE common_sys_task_info set trigger_status = #{triggerStatus} where id in
		<foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
			#{id}
		</foreach>
	</update>

	<delete id="delByIds">
		DELETE FROM common_sys_task_info  where id in
		<foreach collection="ids" item="id" index="index"  open="(" separator="," close=")">
			#{id}
		</foreach>

	</delete>

    <!-- 修改 -->
    <update id="update" parameterType="com.soft.admin.upms.model.SysTaskInfo">
        UPDATE common_sys_task_info
        <set>
            <if test="jobDesc != '' and jobDesc != null">
                job_desc = #{jobDesc},
            </if>
            <if test="jobCron != '' and jobCron != null">
                job_cron = #{jobCron},
            </if>
            <if test="jobType != '' and jobType != null">
                job_type = #{jobType},
            </if>
            <if test="executorHandler != '' and executorHandler != null">
                executor_handler = #{executorHandler},
            </if>
            <if test="executorParam != '' and executorParam != null">
                executor_param = #{executorParam},
            </if>
            <if test="triggerStatus != '' and triggerStatus != null">
                trigger_status = #{triggerStatus},
            </if>
            <if test="jobId != '' and jobId != null">
                job_id = #{jobId},
            </if>
            <if test="refTable != '' and refTable != null">
                ref_table = #{refTable},
            </if>
            <if test="refIds != '' and refIds != null">
                ref_ids = #{refIds},
            </if>
            <if test="createTime != '' and createTime != null">
                create_time = #{createTime}
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <!-- 删除 -->
    <delete id="deleteById" parameterType="java.lang.String">
        DELETE FROM common_sys_task_info WHERE id = #{id}
    </delete>

    <!-- 查询 -->
	<select id="getList" parameterType="com.soft.admin.upms.model.SysTaskInfo" resultType="com.soft.admin.upms.model.SysTaskInfo">
		SELECT
			id,job_desc,job_type,trigger_status,job_ids,ref_table,ref_id,create_time
		FROM  common_sys_task_info s
        <where>
            <if test="jobDesc != null and jobDesc != ''">
                and s.job_desc like concat('%',#{jobDesc},'%')
            </if>
            <if test="triggerStatus != null and triggerStatus != ''">
                and s.triggerStatus = #{triggerStatus}
            </if>
            <if test="jobType != null and jobType != ''">
                and s.job_type = #{jobType}
            </if>
        </where>
	</select>

	<select id="queryNetworkTaskListByConfigId" resultType="com.soft.admin.upms.model.SysTaskInfo" parameterType="integer">
		SELECT
			id,job_desc,job_type,trigger_status,job_ids,ref_table,ref_id,create_time
		FROM
			common_sys_task_info s
		where
		  s.ref_table='base_network_points' and s.ref_id like CONCAT(#{configId}, '#%')
	</select>
</mapper>