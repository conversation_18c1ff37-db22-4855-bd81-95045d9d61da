package com.soft.admin.upms.dto.dintalk;

import lombok.Data;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Description OA用户DTO
 * @Date 0009, 2023年8月9日 14:13
 * <AUTHOR>
 **/
@Data
public class OaUserDTO {
    /**
     * 用户Id。
     */
    private String oaUserId;

    /**
     * 用户当前查找部门Id。
     */
    private String oaDeptId;
    /**
     * 用户所属部门集合
     */
    private List<Long> oaDeptIds = new ArrayList<>();
    /**
     * 用户所管部门集合
     */
    private List<Long> oaDeptLeaderIds = new ArrayList<>();

    /**
     * 是否领导
     */
    private Boolean leader;

    /**
     * 用户名称。
     */
    private String name;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 员工在当前开发者企业账号范围内的唯一标识
     */
    private String unionId;

    /**
     * 工号
     */
    private String jobNumber;

    /**
     * 是否离职
     */
    private Boolean isLeaved;


}
