package com.soft.admin.upms.controller;

import com.google.common.base.Charsets;
import com.google.common.io.Files;
import com.soft.admin.upms.enums.UploadTypeEnums;
import com.soft.admin.upms.model.SysAnnex;
import com.soft.admin.upms.service.SysAnnexService;
import com.soft.admin.upms.vo.SysAnnexVO;
import com.soft.common.core.constant.ErrorCodeEnum;
import com.soft.common.core.exception.ServiceException;
import com.soft.common.core.object.FileInfoDTO;
import com.soft.common.core.object.ResponseResult;
import com.soft.common.core.util.FileUploadUtils;
import com.soft.common.core.util.MimeTypeUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;

/**
 * @Description 文件接口
 * @Date 0028, 2023年2月28日 14:29
 * <AUTHOR>
 **/
@Api(tags = "文件相关接口")
@RequestMapping("/core/file")
@RestController
@Slf4j
public class SysFileController {

    @Value("${config.uploadPath}")
    private String uploadPath;

    @Autowired
    private SysAnnexService sysAnnexService;

    @ApiOperation(value = "上传", notes = "post请求，这里的ContentType=multipart/form-data;")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件实体", dataType = "String", dataTypeClass = MultipartFile.class)
    })
    @PostMapping("/upload")
    public ResponseResult upload(@RequestParam("file") MultipartFile file, @RequestParam(required = false) String type) throws Exception {
        UploadTypeEnums typeEnums = UploadTypeEnums.get(type);
        if (!file.isEmpty()) {
            return ResponseResult.success(FileUploadUtils.upload(uploadPath + typeEnums.getPath(), file, typeEnums.getMimeType()));
        }
        return ResponseResult.error(ErrorCodeEnum.INVALID_UPLOAD_FILE_IOERROR);
    }

    @ApiOperation(value = "附件上传", notes = "post请求，这里的ContentType=multipart/form-data;")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件实体", dataType = "String", dataTypeClass = MultipartFile.class)
    })
    @PostMapping("/annexUpload")
    public ResponseResult<SysAnnexVO> annexUpload(@RequestParam("file") MultipartFile file) throws Exception {
        if (!file.isEmpty()) {
            FileInfoDTO fileInfoDTO = FileUploadUtils.upload(uploadPath + "/annex", file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
            return ResponseResult.success(sysAnnexService.add(fileInfoDTO));
        }
        return ResponseResult.error(ErrorCodeEnum.INVALID_UPLOAD_FILE_IOERROR);
    }

    @ApiOperation(value = "附件下载")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileId", value = "附件ID", dataType = "Long", dataTypeClass = Long.class)
    })
    @GetMapping("/annexDownload")
    public ResponseEntity<byte[]> annexUpload(@RequestParam("fileId") Long fileId) throws IOException {
        SysAnnex annex = sysAnnexService.getAnnex(fileId);
        if (annex == null) {
            throw new ServiceException(ErrorCodeEnum.INVALID_DOWNLOAD_FILE_IOERROR.getErrorMessage());
        }
        byte[] bytes = Files.asByteSource(new File(uploadPath + annex.getPath().replace("/files", ""))).read();
        HttpHeaders headers = new HttpHeaders();
        // 设置MIME类型
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDisposition(ContentDisposition.builder("attachment").filename(annex.getName(), Charsets.UTF_8).build());
        return new ResponseEntity<>(bytes, headers, HttpStatus.OK);
    }

    @ApiOperation(value = "附件查看")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileId", value = "附件ID", dataType = "Long", dataTypeClass = Long.class)
    })
    @GetMapping("/annexIinfo")
    public ResponseResult annexIinfo(@RequestParam("fileId") Long fileId) {
        SysAnnex annex = sysAnnexService.getAnnex(fileId);
        if (annex == null) {
            throw new ServiceException(ErrorCodeEnum.INVALID_DOWNLOAD_FILE_IOERROR.getErrorMessage());
        }
        annex.setPath(annex.getPath());
        return ResponseResult.success(annex);
    }

    @ApiOperation(value = "上传人脸照图片", notes = "post请求，这里的ContentType=multipart/form-data;")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件实体", dataType = "String", dataTypeClass = MultipartFile.class)
    })
    @PostMapping("/upload/user/facePicture")
    public ResponseResult facePicture(@RequestParam("file") MultipartFile file) throws Exception {
        if (!file.isEmpty()) {
            return ResponseResult.success(FileUploadUtils.upload(uploadPath + "/user/face", file, MimeTypeUtils.IMAGE_EXTENSION).getFilePath());
        }
        return ResponseResult.error(ErrorCodeEnum.INVALID_UPLOAD_FILE_IOERROR);
    }


}
