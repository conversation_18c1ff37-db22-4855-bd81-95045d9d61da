package com.soft.admin.upms.api.dingtalk.service;


import com.soft.admin.upms.dto.dintalk.OaSendMessageDTO;

/**
 * @Description 工作通知接口
 * @Date 0009, 2023年8月9日 9:08
 * <AUTHOR>
 **/
public interface IWorkNoticeApi {

    /**
     * 创建OA钉钉工作通知
     * @param dto
     */
    void sendOAWorkNotice(OaSendMessageDTO dto);
    /**
     * 创建钉钉工作通知
     * @param dto
     */
    void sendMarkdownWorkNotice(OaSendMessageDTO dto);


    /**
     * 创建钉钉工作通知
     * @param dto
     */
    void sendActionCardWorkNotice(OaSendMessageDTO dto);

    /**
     * 更新钉钉工作通知 (只有OA消息可以更新状态)  对应的是 ding_talk_todo里面的message_Id
     */
    void updateWorkNotice(Long taskId,boolean status);


    /**
     * 根据busiId获取taskId
     * @param busiId
     * @return
     */
    Long getTaskId(Long busiId);
}
