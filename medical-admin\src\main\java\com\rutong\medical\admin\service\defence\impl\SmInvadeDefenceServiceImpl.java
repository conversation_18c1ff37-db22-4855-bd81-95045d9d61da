package com.rutong.medical.admin.service.defence.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.rutong.medical.admin.dto.defence.DefenceArmDTO;
import com.rutong.medical.admin.dto.defence.DefenceManageDTO;
import com.rutong.medical.admin.entity.defence.SmInvadeDefence;
import com.rutong.medical.admin.mapper.defence.SmInvadeDefenceMapper;
import com.rutong.medical.admin.service.defence.SmInvadeDefenceService;
import com.rutong.medical.admin.vo.defence.DetailVO;
import com.rutong.medical.admin.vo.defence.SmInvadeDefenceVO;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.util.MyPageUtil;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-07-21
 */
@Service
public class SmInvadeDefenceServiceImpl extends ServiceImpl<SmInvadeDefenceMapper, SmInvadeDefence> implements SmInvadeDefenceService {

    @Autowired
    private SmInvadeDefenceMapper smInvadeDefenceMapper;

    @Override
    public MyPageData<SmInvadeDefenceVO> page(DefenceManageDTO defenceManageDTO) {

        // 分页设置
        Integer pageNum = defenceManageDTO.getPageNum();
        Integer pageSize = defenceManageDTO.getPageSize();

        if (pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize);
        }

        // 查询防区列表
        List<SmInvadeDefenceVO> list = smInvadeDefenceMapper.selectDefencePage(defenceManageDTO);

        // 使用MyPageUtil工具类转换为分页数据
        return MyPageUtil.makeResponseData(list);
    }

    @Override
    public Boolean updateDefenceArm(DefenceArmDTO defenceArmDTO) {
        // 参数校验
        if (CollectionUtils.isEmpty(defenceArmDTO.getDefenceIds())) {
            throw new IllegalArgumentException("防区ID列表不能为空");
        }

        if (defenceArmDTO.getOperationType() == null) {
            throw new IllegalArgumentException("操作类型不能为空");
        }

        // 根据操作类型进行不同的处理
        if (defenceArmDTO.getOperationType() == 1) {
            // 手动布防/撤防
            return handleManualArm(defenceArmDTO);
        } else if (defenceArmDTO.getOperationType() == 2) {
            // 自动布防
            return handleAutoArm(defenceArmDTO);
        } else {
            throw new IllegalArgumentException("操作类型无效，只支持1（手动布防/撤防）或2（自动布防）");
        }
    }


    /**
     * 解除关联
     * @param id
     * @return
     */
    @Override
    public Boolean removeConnect(Long id) {



        smInvadeDefenceMapper.deleteConnect(id);



        return true;

    }



    /**
     * 处理手动布防/撤防
     */
    private Boolean handleManualArm(DefenceArmDTO defenceArmDTO) {
        if (defenceArmDTO.getDefenceState() == null) {
            throw new IllegalArgumentException("手动操作时布防状态不能为空");
        }

        if (defenceArmDTO.getDefenceState() != 0 && defenceArmDTO.getDefenceState() != 1) {
            throw new IllegalArgumentException("布防状态只能是0（撤防）或1（布防）");
        }

        // 批量更新防区状态
        for (Long defenceId : defenceArmDTO.getDefenceIds()) {
            SmInvadeDefence defence = new SmInvadeDefence();
            defence.setId(defenceId);
            defence.setDefenceState(defenceArmDTO.getDefenceState());
            defence.setUpdateTime(LocalDate.now());
            // TODO: 设置更新用户ID
            // defence.setUpdateUserId(getCurrentUserId());

            // 如果是撤防，清空自动布防时间
            if (defenceArmDTO.getDefenceState() == 0) {

                defence.setStartTime(null);
                defence.setEndTime(null);
            }

            updateById(defence);
        }

        return true;
    }

    /**
     * 处理自动布防
     */
    private Boolean handleAutoArm(DefenceArmDTO defenceArmDTO) {
        if (defenceArmDTO.getStartTime() == null || defenceArmDTO.getEndTime() == null) {
            throw new IllegalArgumentException("自动布防时开始时间和结束时间不能为空");
        }

        // 校验时间：开始时间不能比结束时间晚
        if (defenceArmDTO.getStartTime().isAfter(defenceArmDTO.getEndTime())) {
            throw new IllegalArgumentException("开始时间不能比结束时间晚");
        }

        // 批量设置自动布防
        for (Long defenceId : defenceArmDTO.getDefenceIds()) {
            SmInvadeDefence defence = new SmInvadeDefence();
            defence.setId(defenceId);
            defence.setDefenceState(1); // 自动布防默认设置为布防状态
            defence.setStartTime(defenceArmDTO.getStartTime().toLocalDate());
            defence.setEndTime(defenceArmDTO.getEndTime().toLocalDate());
            defence.setUpdateTime(LocalDate.now());
            // TODO: 设置更新用户ID
            // defence.setUpdateUserId(getCurrentUserId());

            updateById(defence);
        }

        return true;
    }


    @Override
    public Boolean batchDeleteDefence(List<Long> defenceIds) {
        if (CollectionUtils.isEmpty(defenceIds)) {
            throw new IllegalArgumentException("防区ID列表不能为空");
        }

        // 软删除：将is_delete字段更新为1
        for (Long defenceId : defenceIds) {
            SmInvadeDefence defence = new SmInvadeDefence();
            defence.setId(defenceId);
            defence.setIsDelete(1); // 1表示已删除
            defence.setUpdateTime(LocalDate.now()); // 更新修改时间
            updateById(defence);
        }

        return true;
    }


    @Override
    public MyPageData<DetailVO> getAllDevice(DefenceManageDTO defenceManageDTO) {

        Integer pageNum = defenceManageDTO.getPageNum();
        Integer pageSize = defenceManageDTO.getPageSize();

        if (pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize);
        }

        List<DetailVO> list = smInvadeDefenceMapper.selectDevicePage(defenceManageDTO);

        return MyPageUtil.makeResponseData(list);

    }

}
