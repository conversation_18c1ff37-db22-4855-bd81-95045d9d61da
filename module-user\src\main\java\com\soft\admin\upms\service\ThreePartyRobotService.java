package com.soft.admin.upms.service;

import com.soft.admin.upms.dto.ThreePartyRobotDTO;
import com.soft.admin.upms.model.ThreePartyRobot;
import com.soft.admin.upms.vo.ThreePartyRobotVO;
import com.soft.common.core.base.service.IBaseService;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.MyPageParam;

/**
 * 三方机器人配置Service接口
 *
 * <AUTHOR>
 * @date 2023-05-23
 */
public interface ThreePartyRobotService extends IBaseService<ThreePartyRobot, Long> {


    /**
     * 新增机器人配置
     *
     * @param addDTO
     */
    void addRobot( ThreePartyRobotDTO addDTO);

    /**
     * 修改机器人配置
     *
     * @param updateDTO
     */
    void updateRobot(ThreePartyRobotDTO updateDTO);

    /**
     * 删除机器人配置
     *
     * @param id
     */
    void deleteRobot( Long id);

    /**
     * 获取机器人配置分页
     *
     * @return
     */
    MyPageData<ThreePartyRobotVO> getRobotPage(MyPageParam pageParam);
}
