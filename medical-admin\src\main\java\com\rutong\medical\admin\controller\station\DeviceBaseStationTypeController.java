package com.rutong.medical.admin.controller.station;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.rutong.medical.admin.dto.station.DeviceBaseStationTypeDTO;
import com.rutong.medical.admin.dto.station.DeviceBaseStationTypeQueryDTO;
import com.rutong.medical.admin.service.station.DeviceBaseStationTypeService;
import com.soft.common.core.object.ResponseResult;

import cn.hutool.core.lang.tree.Tree;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 基站分类控制器类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */

@Api(tags = "基站分类")
@RestController
@RequestMapping("/device/base/station/type")
public class DeviceBaseStationTypeController {

    @Autowired
    private DeviceBaseStationTypeService deviceBaseStationTypeService;

    /**
     * 获取基站类型树结构列表
     *
     * @param deviceBaseStationTypeQuery
     * @return
     */
    @ApiOperation(value = "获取基站类型树结构列表")
    @GetMapping("/list/tree")
    public ResponseResult<List<Tree<Long>>> getTreeList(DeviceBaseStationTypeQueryDTO deviceBaseStationTypeQuery) {
        return ResponseResult.success(deviceBaseStationTypeService.getTreeList(deviceBaseStationTypeQuery));
    }

    /**
     * 新增基站类型
     *
     * @param deviceBaseStationType
     */
    @ApiOperation(value = "新增基站类型")
    @PostMapping("/save")
    public ResponseResult save(@RequestBody @Validated DeviceBaseStationTypeDTO deviceBaseStationType) {
        deviceBaseStationTypeService.save(deviceBaseStationType);
        return ResponseResult.success();
    }

    /**
     * 更新基站类型
     *
     * @param deviceBaseStationType
     */
    @ApiOperation(value = "更新基站类型")
    @PostMapping("/update")
    public ResponseResult update(@RequestBody @Validated DeviceBaseStationTypeDTO deviceBaseStationType) {
        deviceBaseStationTypeService.update(deviceBaseStationType);
        return ResponseResult.success();
    }

    /**
     * 删除基站类型
     *
     * @param id
     */
    @ApiOperation(value = "删除基站类型")
    @PostMapping("/delete")
    public ResponseResult delete(@RequestParam Long id) {
        deviceBaseStationTypeService.delete(id);
        return ResponseResult.success();
    }
}
