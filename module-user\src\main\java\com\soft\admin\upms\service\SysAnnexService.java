package com.soft.admin.upms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.admin.upms.model.SysAnnex;
import com.soft.admin.upms.vo.SysAnnexVO;
import com.soft.common.core.object.FileInfoDTO;

import java.util.List;

/**
 * 公共附件Service接口
 *
 * <AUTHOR>
 * @date 2023-09-19
 */
public interface SysAnnexService extends IService<SysAnnex> {

    /**
     * 增加附件
     *
     * @param fileInfoDTO
     * @return
     */
    SysAnnexVO add(FileInfoDTO fileInfoDTO);

    /**
     * 根据id查询列表
     *
     * @param ids
     * @return
     */
    List<SysAnnexVO> list(List<Long> ids);

    /**
     * 根据id查询列表
     *
     * @param id    要查的表名关联的ID
     * @param clazz 要查的表名
     * @return
     */
    List<SysAnnexVO> list(Long busiId, Class clazz);


    /**
     * 获取附件明细
     *
     * @param fileId
     * @return
     */
    SysAnnex getAnnex(Long fileId);
}