package com.soft.admin.upms.api.dingtalk;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.soft.admin.upms.enums.OaTypeEnums;
import com.soft.admin.upms.service.OaSyncService;
import com.soft.common.core.annotation.NoAuthInterface;
import com.soft.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * ISV 小程序回调信息处理
 */
@RestController
@RequestMapping("/dingtalk")
@Slf4j
public class DingtalkCallbackController {


    public static final String LOCK_KEY_HTTPSYNC_DATA = "oa:httpsyncData:lock";
    @Resource
    private DingTalkConfig dingTalkConfig;

    @Autowired
    private OaSyncService oaSyncService;

    @Resource
    private RedissonClient redissonClient;

    @PostMapping(value = "/callback/event")
    @NoAuthInterface
    public Map<String,String> callBack(@RequestParam(value = "msg_signature", required = false) String msg_signature,
                                       @RequestParam(value = "timestamp", required = false) String timeStamp,
                                       @RequestParam(value = "nonce", required = false) String nonce,
                                       @RequestBody(required = false) JSONObject json) {

        // 获取同步锁，防止用户多次点击同步数据
        RLock lock = redissonClient.getLock(LOCK_KEY_HTTPSYNC_DATA);
        if (lock.isLocked()) {
            throw new ServiceException("数据正在同步中，请稍后重试");
        }
        try {
            lock.lock();

            //初始化参数
            oaSyncService.init();

            // 1. 从http请求中获取加解密参数
            // 2. 使用加解密类型
            // Constant.OWNER_KEY 说明：
            // 1、开发者后台配置的订阅事件为应用级事件推送，此时OWNER_KEY为应用的APP_KEY。
            // 2、调用订阅事件接口订阅的事件为企业级事件推送，
            //      此时OWNER_KEY为：企业的appkey（企业内部应用）或SUITE_KEY（三方应用）

            DingCallbackCrypto callbackCrypto = new DingCallbackCrypto(dingTalkConfig.getHttpCallbackToken(), dingTalkConfig.getHttpCallbackAesKey() , dingTalkConfig.getAppKey());
            String encryptMsg = json.getString("encrypt");
            String decryptMsg = callbackCrypto.getDecryptMsg(msg_signature, timeStamp, nonce, encryptMsg);

            // 3. 反序列化回调事件json数据
            JSONObject bizData = JSON.parseObject(decryptMsg);
            String eventType = bizData.getString("EventType");
            log.info("DingTalkEventCllback2: {} : {}", eventType, bizData);

            //处理事件
            switch (DingTalkEventEnums.getByValue(eventType)){
                case DEPT_CREATE:
                case DEPT_MODIFY:
                    oaSyncService.syncDeptByOaDeptIdsCallback(OaTypeEnums.DING_TALK, bizData.getJSONArray("DeptId").toJavaList(String.class));
                    break;
                case USER_ADD_ORG:
                case USER_MODIFY_ORG:
                    oaSyncService.syncUserByOaUserIds(OaTypeEnums.DING_TALK, bizData.getJSONArray("UserId").toJavaList(String.class));
                    break;
                case USER_LEAVE_ORG:
                    oaSyncService.leaveUserByOaUserIds(OaTypeEnums.DING_TALK, bizData.getJSONArray("UserId").toJavaList(String.class));
                    break;
                default:
                    log.info("ding talk callback event no handler:{}", eventType);
            }

            // 5. 返回success的加密数据
            Map<String, String> successMap = callbackCrypto.getEncryptedMap("success");
            return successMap;

        } catch (DingCallbackCrypto.DingTalkEncryptException e) {
            log.error("钉钉http回调出错");
            log.error(e.getMessage(),e);
        }finally {
            // 取消同步锁
            lock.unlock();
            log.info("同步(http)钉钉用户完成");
        }
        return null;
    }
}