<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rutong.medical.admin.mapper.station.DeviceBaseStationMapper">
    <resultMap type="com.rutong.medical.admin.entity.station.DeviceBaseStation" id="DeviceBaseStationResult">
        <result property="id" column="id" />
        <result property="deviceBaseStationCode" column="device_base_station_code" />
        <result property="deviceBaseStationType" column="device_base_station_type" />
        <result property="deviceBaseStationName" column="device_base_station_name" />
        <result property="protocol" column="protocol" />
        <result property="ip" column="ip" />
        <result property="spaceId" column="space_id" />
        <result property="x" column="x" />
        <result property="y" column="y" />
        <result property="z" column="z" />
        <result property="longitude" column="longitude" />
        <result property="latitude" column="latitude" />
        <result property="isOnline" column="is_online" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
        <result property="isDelete" column="is_delete" />
    </resultMap>

    <sql id="selectDeviceBaseStationVo">
        select id, device_base_station_code, device_base_station_type, device_base_station_name, protocol, ip, space_id, x, y, z, longitude, latitude, is_online, create_user_id, create_time, update_user_id, update_time, is_delete from device_base_station
    </sql>



    <select id="list" resultType="com.rutong.medical.admin.entity.station.DeviceBaseStation" parameterType="com.rutong.medical.admin.dto.station.DeviceBaseStationPageQueryDTO">
        select * from sm_device_base_station
        <where>
            <!-- 查询未删除的数据 -->
            and is_delete = 1

            <!-- 关键词搜索：基站名称或编号 -->
            <if test="keyWord != null and keyWord != ''">
                and (device_base_station_name like concat('%', #{keyWord}, '%')
                or device_base_station_code like concat('%', #{keyWord}, '%'))
            </if>

            <if test="deviceBaseStationTypeIdList != null">
                and device_base_station_type in
                <foreach item="item" index="index" collection="deviceBaseStationTypeIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <!-- 匹配所属楼层路径 -->
            <if test="spacePath != null and spacePath != ''">
                and space_path like concat('%', #{spacePath}, '%')
            </if>


            <!-- 在线状态 -->
            <if test="isOnline != null">
                and is_online = #{isOnline}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="existsByDeviceNumberOrNameUsingMap" resultType="int">
        SELECT COUNT(*) FROM sm_device_base_station
        WHERE (device_base_station_code = #{param1}
            OR device_base_station_name = #{param2})
          AND is_delete = 1
    </select>



    <select id="existsByDeviceNumberOrNameExcludingId" resultType="int">
        SELECT COUNT(*) FROM sm_device_base_station
        WHERE (device_base_station_code = #{param1}
            OR device_base_station_name = #{param2})
          AND id != #{param3}
          AND is_delete = 1
    </select>


    <!-- 更新基站信息 -->
    <update id="updateDeviceBaseStationById">
        UPDATE sm_device_base_station
        <set>
            <!-- 基站编号 -->
            <if test="deviceBaseStationCode != null and deviceBaseStationCode != ''">
                device_base_station_code = #{deviceBaseStationCode},
            </if>

            <!-- 基站分类表ID -->
            <if test="deviceBaseStationType != null">
                device_base_station_type = #{deviceBaseStationType},
            </if>

            <!-- 基站名称 -->
            <if test="deviceBaseStationName != null and deviceBaseStationName != ''">
                device_base_station_name = #{deviceBaseStationName},
            </if>

            <!-- 支持协议 -->
            <if test="protocol != null and protocol != ''">
                protocol = #{protocol},
            </if>

            <!-- IP地址 -->
            <if test="ip != null ">
                ip = #{ip},
            </if>

            <!-- 所属楼层id -->
            <if test="spaceId != null">
                space_id = #{spaceId},
            </if>

            <!-- 所属楼层路径 -->
            <if test="spacePath != null and spacePath != ''">
                space_path = #{spacePath},
            </if>

            <!-- 所属楼层全名称 -->
            <if test="spaceFullName != null and spaceFullName != ''">
                space_full_name = #{spaceFullName},
            </if>

            <!-- X坐标 -->
            <if test="x != ''">
                x = #{x},
            </if>

            <!-- Y坐标 -->
            <if test="y != ''">
                y = #{y},
            </if>

            <!-- Z坐标 -->
            <if test="z != ''">
                z = #{z},
            </if>

            <!-- 经度 -->
            <if test="longitude != ''">
                longitude = #{longitude},
            </if>

            <!-- 纬度 -->
            <if test="latitude != ''">
                latitude = #{latitude},
            </if>

            <!-- 在线状态(1:在线,0:离线) -->
            <if test="isOnline != null">
                is_online = #{isOnline},
            </if>

            <!-- 更新时间 -->
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>

            <!-- 更新人ID -->
            <if test="updateUserId != null">
                update_user_id = #{updateUserId},
            </if>
        </set>
        WHERE id = #{id}
    </update>
</mapper>