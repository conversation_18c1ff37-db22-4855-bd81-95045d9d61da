<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>module-common</artifactId>
        <groupId>com.soft</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>common-redis</artifactId>
    <version>1.0.0</version>
    <name>common-redis</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-core</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>${redisson.version}</version>
        </dependency>
    </dependencies>
</project>