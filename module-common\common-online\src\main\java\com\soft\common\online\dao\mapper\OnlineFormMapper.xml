<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.common.online.dao.OnlineFormMapper">
    <resultMap id="BaseResultMap" type="com.soft.common.online.model.OnlineForm">
        <id column="form_id" jdbcType="BIGINT" property="formId"/>
        <result column="page_id" jdbcType="BIGINT" property="pageId"/>
        <result column="form_code" jdbcType="VARCHAR" property="formCode"/>
        <result column="form_name" jdbcType="VARCHAR" property="formName"/>
        <result column="form_kind" jdbcType="INTEGER" property="formKind"/>
        <result column="form_type" jdbcType="INTEGER" property="formType"/>
        <result column="master_table_id" jdbcType="BIGINT" property="masterTableId"/>
        <result column="widget_json" jdbcType="LONGVARCHAR" property="widgetJson"/>
        <result column="params_json" jdbcType="LONGVARCHAR" property="paramsJson"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.soft.common.online.dao.OnlineFormMapper.inputFilterRef"/>
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="onlineFormFilter != null">
            <if test="onlineFormFilter.pageId != null">
                AND zz_online_form.page_id = #{onlineFormFilter.pageId}
            </if>
            <if test="onlineFormFilter.formCode != null and onlineFormFilter.formCode != ''">
                AND zz_online_form.form_code = #{onlineFormFilter.formCode}
            </if>
            <if test="onlineFormFilter.formName != null and onlineFormFilter.formName != ''">
                AND zz_online_form.form_name = #{onlineFormFilter.formName}
            </if>
            <if test="onlineFormFilter.formType != null">
                AND zz_online_form.form_type = #{onlineFormFilter.formType}
            </if>
        </if>
    </sql>

    <select id="getOnlineFormList" resultMap="BaseResultMap" parameterType="com.soft.common.online.model.OnlineForm">
        SELECT * FROM zz_online_form
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
