package com.rutong.medical.admin.service.station;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rutong.medical.admin.dto.station.DeviceBaseStationPageQueryDTO;
import com.rutong.medical.admin.dto.station.DeviceBaseStationSaveDTO;
import com.rutong.medical.admin.entity.station.DeviceBaseStation;
import com.rutong.medical.admin.vo.station.DeviceBaseStationVO;
import com.soft.common.core.object.MyPageData;

/**
 * 基站Service接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface DeviceBaseStationService extends IService<DeviceBaseStation> {
    /**
     * 基站分页查询
     * 
     * @param deviceBaseStationPageQuery
     * @return
     */
    MyPageData<DeviceBaseStationVO> page(DeviceBaseStationPageQueryDTO deviceBaseStationPageQuery);

    /**
     * 删除基站
     * 
     * @param id
     */
    void delete(Long id);

    /**
     * 新增或者修改基站
     * 
     * @param deviceBaseStationSave
     */
    void saveOrUpdate(DeviceBaseStationSaveDTO deviceBaseStationSave);

    /**
     * 基站详情接口
     * 
     * @param id
     * @return
     */
    DeviceBaseStationVO detail(Long id);

    /**
     * 基站查询列表
     * 
     * @param deviceBaseStationPageQuery
     * @return
     */
    List<DeviceBaseStationVO> list(DeviceBaseStationPageQueryDTO deviceBaseStationPageQuery);

    /**
     * 根据基站编号查询基站信息
     *
     * @param deviceBaseStationCode
     * @return
     */
    DeviceBaseStation getDeviceBaseStation(String deviceBaseStationCode);
}
