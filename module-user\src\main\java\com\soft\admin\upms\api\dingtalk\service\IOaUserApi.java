package com.soft.admin.upms.api.dingtalk.service;

import com.soft.admin.upms.dto.dintalk.OaUserDTO;

import java.util.List;

/**
 * @Description OA系统用户接口
 * @Date 0009, 2023年8月9日 9:08
 * <AUTHOR>
 **/
public interface IOaUserApi {

    /**
     * 根据部门获取用户详情列表
     *
     * @return
     */
    List<OaUserDTO> getUsersByOaDeptId(String oaDeptId, Long pageNum, Long pageSize);

    /**
     * 根据用户id获取详情
     *
     * @return
     */
    OaUserDTO getUserByOaUserId(String oaUserId);

    /**
     * 根据用户id列表获取oa用户列表
     *
     * @param oaUserIds
     * @return
     */
    List<OaUserDTO> getUsersByOaUserIds(List<String> oaUserIds);

    /**
     * 根据code获取oa用户id
     *
     * @param code
     * @return
     */
    String getOaUserIdByCode(String code);

    /**
     * 推送考勤记录
     *
     * @param oaUserId
     * @param deviceId
     * @param deviceName
     */
    void pushPunchRecords(String oaUserId, String deviceId, String deviceName);
}
