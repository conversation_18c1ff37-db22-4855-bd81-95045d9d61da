package com.soft.common.core.util;


import com.soft.common.core.constant.Constants;
import com.soft.common.core.object.FileInfoDTO;
import org.apache.commons.io.FilenameUtils;
import org.apache.tomcat.util.http.fileupload.impl.FileSizeLimitExceededException;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.Iterator;
import java.util.Objects;

/**
 * 文件上传工具类
 *
 * <AUTHOR>
 */
public class FileUploadUtils {
    /**
     * 默认大小 50M
     */
    public static final long DEFAULT_MAX_SIZE = 50 * 1024 * 1024;

    /**
     * 默认的文件名最大长度 100
     */
    public static final int DEFAULT_FILE_NAME_LENGTH = 100;

    /**
     * 默认上传的地址
     */
    private static String defaultBaseDir = "./files";

    public static void setDefaultBaseDir(String defaultBaseDir) {
        FileUploadUtils.defaultBaseDir = defaultBaseDir;
    }

    public static String getDefaultBaseDir() {
        return defaultBaseDir;
    }

    /**
     * 以默认配置进行文件上传
     *
     * @param file 上传的文件
     * @return 文件名称
     * @throws Exception
     */
    public static final FileInfoDTO upload(MultipartFile file) throws IOException {
        try {
            return upload(getDefaultBaseDir(), file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 根据文件路径上传
     *
     * @param baseDir 相对应用的基目录
     * @param file    上传的文件
     * @return 文件名称
     * @throws IOException
     */
    public static final FileInfoDTO upload(String baseDir, MultipartFile file) throws IOException {
        try {
            return upload(baseDir, file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 文件上传
     *
     * @param baseDir          相对应用的基目录
     * @param file             上传的文件
     * @param allowedExtension 上传文件类型
     * @return 返回上传成功的文件名
     * @throws FileSizeLimitExceededException 如果超出最大大小
     * @throws IOException                    比如读写文件出错时
     */
    public static final FileInfoDTO upload(String baseDir, MultipartFile file, String[] allowedExtension)
            throws FileSizeLimitExceededException, IOException {
        FileInfoDTO fileInfo = new FileInfoDTO();

        int fileNameLength = Objects.requireNonNull(file.getOriginalFilename()).length();

        assertAllowed(file, allowedExtension);

        String fileName = extractFilename(file);

        File absoluteFile = getAbsoluteFile(baseDir, fileName);
        String absPath = absoluteFile.getAbsolutePath();
        file.transferTo(Paths.get(absPath));
        fileInfo.setFilePath(getPathFileName(baseDir, fileName));
        fileInfo.setFileSize((int) Math.rint(absoluteFile.length() / 1024));
        fileInfo.setOriginalFileName(file.getOriginalFilename());
        fileInfo.setFileName(fileName);
        fileInfo.setSuffix(getExtension(file));
        fileInfo.setFullFilePath(absoluteFile.getCanonicalPath());
        return fileInfo;
    }

    /**
     * 编码文件名
     */
    public static final String extractFilename(MultipartFile file) {
        return StringUtils.format("{}/{}.{}", DateUtils.datePath(),
                Seq.getId(Seq.uploadSeqType), getExtension(file));
    }

    public static final File getAbsoluteFile(String uploadDir, String fileName) throws IOException {
        File desc = new File(uploadDir + File.separator + fileName);

        if (!desc.exists()) {
            if (!desc.getParentFile().exists()) {
                desc.getParentFile().mkdirs();
            }
        }
        return desc;
    }

    public static final File getAbsoluteFile(String filePath) throws IOException {
        File desc = new File(filePath);

        if (!desc.exists()) {
            if (!desc.getParentFile().exists()) {
                desc.getParentFile().mkdirs();
            }
        }
        return desc;
    }

    public static final String getPathFileName(String uploadDir, String fileName) throws IOException {
        int dirLastIndex = defaultBaseDir.length() + 1;
        String currentDir = StringUtils.substring(uploadDir, dirLastIndex);
        return Constants.RESOURCE_PREFIX + "/" + currentDir + "/" + fileName;
    }

    /**
     * 文件大小校验
     *
     * @param file 上传的文件
     * @return
     * @throws FileSizeLimitExceededException 如果超出最大大小
     */
    public static final void assertAllowed(MultipartFile file, String[] allowedExtension)
            throws FileSizeLimitExceededException {
        long size = file.getSize();

        String fileName = file.getOriginalFilename();
        String extension = getExtension(file);
        if (allowedExtension != null && !isAllowedExtension(extension, allowedExtension)) {

        }
    }

    /**
     * 判断MIME类型是否是允许的MIME类型
     *
     * @param extension
     * @param allowedExtension
     * @return
     */
    public static final boolean isAllowedExtension(String extension, String[] allowedExtension) {
        for (String str : allowedExtension) {
            if (str.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取文件名的后缀
     *
     * @param file 表单文件
     * @return 后缀名
     */
    public static final String getExtension(MultipartFile file) {
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        if (StringUtils.isEmpty(extension)) {
            extension = MimeTypeUtils.getExtension(Objects.requireNonNull(file.getContentType()));
        }
        return extension;
    }


    public static final FileInfoDTO uploadAndCompression(String baseDir, MultipartFile file, String[] allowedExtension)
            throws FileSizeLimitExceededException, IOException {
        FileInfoDTO fileInfo = new FileInfoDTO();

        assertAllowed(file, allowedExtension);

        String fileName = extractFilename(file);

        File absoluteFile = getAbsoluteFile(baseDir, fileName);
        String absPath = absoluteFile.getAbsolutePath();

        resize(file.getInputStream(), absPath);

        fileInfo.setFilePath(getPathFileName(baseDir, fileName));
        fileInfo.setOriginalFileName(file.getOriginalFilename());
        fileInfo.setFileName(fileName);
        return fileInfo;
    }

    public static void resize(InputStream in, String dest) throws IOException {
        Image img = null;
        BufferedImage tempPNG = null;

        img = ImageIO.read(in);
        tempPNG = resizeImage(img, ((BufferedImage) img).getWidth(), ((BufferedImage) img).getHeight());

        //裁剪不压缩,存储
        // ImageIO.write(tempPNG, "png", new File(dest));
        //压缩后输出
        compress(tempPNG, dest);
    }

    public static void compress(BufferedImage image, String dest) throws IOException {

        File compressedImageFile = new File(dest);
        OutputStream os = new FileOutputStream(compressedImageFile);
        Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("jpg");

        ImageWriter writer = writers.next();
        ImageOutputStream ios = ImageIO.createImageOutputStream(os);
        writer.setOutput(ios);
        ImageWriteParam param = writer.getDefaultWriteParam();
        param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
        //压缩图片的质量,数值越小压缩率越高,图片质量越低
        param.setCompressionQuality(0.8f);
        writer.write(null, new IIOImage(image, null, null), param);

        os.close();
        ios.close();
        writer.dispose();
    }

    public static BufferedImage resizeImage(final Image image, int width, int height) {

        final BufferedImage bufferedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        //创建graph2D,用来将image写入bufferedImage
        final Graphics2D graphics2D = bufferedImage.createGraphics();
        graphics2D.setComposite(AlphaComposite.Src);
//        //below three lines are for RenderingHints for better image quality at cost of higher processing time
//        graphics2D.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
//        graphics2D.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
//        graphics2D.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        graphics2D.drawImage(image, 0, 0, width, height, null);
        graphics2D.dispose();
        return bufferedImage;
    }



    /**
     * 图片转换成base64字符串（不带图片头data:image/jpg;base64,）
     * @param imagePath
     * @return
     * @throws IOException
     */
    public static String convertImageToBase64(String imagePath) throws IOException {

        // 读取图片文件到字节数组
        Path path = new File(imagePath).toPath();
        byte[] imageBytes = Files.readAllBytes(path);

        // 将字节数组编码为Base64字符串
        String base64ImageString = Base64.getEncoder().encodeToString(imageBytes);

        return base64ImageString;
    }
}
