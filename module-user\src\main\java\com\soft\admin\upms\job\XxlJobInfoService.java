package com.soft.admin.upms.job;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.rutong.xxl.job.client.StandaloneAdminBizClient;
import com.xxl.job.core.biz.client.AdminBizClient;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.model.XxlJobGroup;
import com.xxl.job.core.model.XxlJobInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @ClassName com.iricto.soft.xxl.service.XxlJobInfoService
 * @Description 执行任务表 服务类
 * @Date 2021-08-03
 */
@Service
public class XxlJobInfoService {

    private static Logger logger = LoggerFactory.getLogger(XxlJobInfoService.class);

    @Resource
    private StandaloneAdminBizClient adminBizClient;

    @Value("${xxl.job.executor.appname}")
    private String appname;


    /**
     * 创建定时任务
     * @param jobDesc 任务描述
     * @param syncTaskCron cron表达式
     * @param executorHandler 执行器处理方法
     * @param params 任务参数
     * @return
     */
    public String createJob(String jobDesc,String syncTaskCron,String executorHandler,JSONObject params) {
        XxlJobInfo jobInfo = new XxlJobInfo();
        ReturnT<XxlJobGroup> jobGroup = AdminBizClient.getInstance().getJobGroupByAppname(appname);
        jobInfo.setJobGroup(jobGroup.getContent().getId());
        jobInfo.setJobDesc(jobDesc);
        jobInfo.setAuthor(appname);
        jobInfo.setScheduleType("CRON");
        jobInfo.setScheduleConf(syncTaskCron);
        jobInfo.setMisfireStrategy("FIRE_ONCE_NOW");
        jobInfo.setExecutorRouteStrategy("ROUND");
        jobInfo.setExecutorHandler(executorHandler);

        // 定时任务参数
        if(params != null){
            jobInfo.setExecutorParam(params.toJSONString());
        }

        jobInfo.setExecutorBlockStrategy("SERIAL_EXECUTION");
        jobInfo.setExecutorTimeout(5);
        jobInfo.setExecutorFailRetryCount(0);
        jobInfo.setGlueType("BEAN");
        jobInfo.setTriggerStatus(1);
        ReturnT<String> result = adminBizClient.addJobInfo(jobInfo);
        return result.getContent();
    }

    /**
     * 获取任务详情
     * @param id
     * @return
     */
    public XxlJobInfo getXxlJobInfo(String id) {
        try{
            ReturnT<XxlJobInfo> jobInfo = adminBizClient.getJobInfoById(Integer.valueOf(id));
            if (jobInfo.getCode() == ReturnT.SUCCESS_CODE) {
                return jobInfo.getContent();
            }
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
        return null;
    }

    public void updateJobInfo(XxlJobInfo jobInfo){
        adminBizClient.updateJobInfo(jobInfo);
    }

    /**
     * 删除任务
     * @param id
     */
    public void removeJobInfo(String id){
        adminBizClient.removeJobInfo(Integer.valueOf(id));
    }


    /**
     * @return
     * @Description: 批量新增定时任务
     * @Author: 苏承敏
     * @Date: 2021-08-04
     * @Param [jobInfos]
     */
    public List<Integer> batchInsertJob(List<XxlJobInfo> jobInfos) {
        if (jobInfos != null && jobInfos.size() > 0) {
            for (XxlJobInfo xxlJobInfo : jobInfos) {
                paramSetValue(xxlJobInfo);
            }
        }
        ReturnT<List<Integer>> returnT = adminBizClient.batchAddJobInfo(jobInfos);
        return returnT.getContent();
    }

    /**
     * @return
     * @Description: 对象值设置
     * @Author: 苏承敏
     * @Date: 2021-08-04
     */
    private void paramSetValue(XxlJobInfo xxlJobInfo) {
        xxlJobInfo.setAddTime(new Date());
        xxlJobInfo.setJobGroup(adminBizClient.getJobGroupByAppname(appname).getContent().getId());
        xxlJobInfo.setExecutorRouteStrategy("FIRST");
        xxlJobInfo.setGlueType("BEAN");
        xxlJobInfo.setExecutorRouteStrategy("ROUND");
        xxlJobInfo.setExecutorBlockStrategy("SERIAL_EXECUTION");
        xxlJobInfo.setMisfireStrategy("FIRE_ONCE_NOW");
        xxlJobInfo.setAuthor("admin");
        xxlJobInfo.setScheduleType("CRON");
        xxlJobInfo.setExecutorTimeout(5);
        xxlJobInfo.setGlueRemark("GLUE代码初始化");
        xxlJobInfo.setExecutorFailRetryCount(0);
        xxlJobInfo.setTriggerStatus(xxlJobInfo.getTriggerStatus());
    }

    /*
     *
     * @Description 批量启动
     * <AUTHOR>
     * @Date 2021/8/5
     * @param ids
     * @return com.iricto.job.core.biz.model.ReturnT<java.lang.String>
     **/
    public boolean batchStart(int[] ids) {
        List<Integer> integers = new ArrayList<Integer>();
        for (int id:ids){
            integers.add(id);
        }
        ReturnT<List<Integer>> returnT = adminBizClient.batchStartJob(integers);
        if ( returnT.getCode() == ReturnT.SUCCESS_CODE) {
            return true;
        }
        return false;
    }

    public boolean start(Integer id) {
        ReturnT<List<Integer>> returnT = adminBizClient.batchStartJob(Lists.newArrayList(id));
        if ( returnT.getCode() == ReturnT.SUCCESS_CODE) {
            return true;
        }
        return false;
    }

    /*
     *
     * @Description 批量暂停
     * <AUTHOR>
     * @Date 2021/8/5
     * @param ids
     * @return com.iricto.job.core.biz.model.ReturnT<java.lang.String>
     **/
    public boolean batchStop(int[] ids) {
        List<Integer> integers = new ArrayList<Integer>();
        for (int id:ids){
            integers.add(id);
        }
        ReturnT<List<Integer>> returnT = adminBizClient.batchStopJob(integers);
        if ( returnT.getCode() == ReturnT.SUCCESS_CODE) {
            return true;
        }
        return false;
    }

    public boolean stop(Integer id) {
        ReturnT<List<Integer>> returnT = adminBizClient.batchStopJob(Lists.newArrayList(id));
        if ( returnT.getCode() == ReturnT.SUCCESS_CODE) {
            return true;
        }
        return false;
    }

    /*
     *
     * @Description 批量删除
     * <AUTHOR>
     * @Date 2021/8/5
     * @param ids
     * @return com.iricto.job.core.biz.model.ReturnT<java.lang.String>
     **/
    public boolean batchDel(int[] ids) {
        List<Integer> integers = new ArrayList<Integer>();
        for (int id:ids){
            integers.add(id);
        }
        ReturnT<List<Integer>> returnT = adminBizClient.batchRemoveJob(integers);
        if ( returnT.getCode() == ReturnT.SUCCESS_CODE) {
            return true;
        }
        return false;
    }
}
