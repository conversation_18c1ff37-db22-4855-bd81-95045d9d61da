package com.soft.admin.upms.service.apiService.imp;

import com.github.pagehelper.PageHelper;
import com.soft.admin.upms.dao.SysUserMapper;
import com.soft.admin.upms.dto.ApiSysUserDto;
import com.soft.admin.upms.model.SysUser;
import com.soft.admin.upms.service.apiService.UserApiService;
import com.soft.admin.upms.vo.ApiSysUserVo;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.util.MyPageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class UserApiServiceImpl implements UserApiService {

    @Autowired
    private SysUserMapper sysUserMapper;

    @Override
    public MyPageData<ApiSysUserVo> list(ApiSysUserDto sysUserDto) {
        Integer pageNum  = sysUserDto.getPageNum();
        Integer pageSize = sysUserDto.getPageSize();
        if (pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize).setReasonable(false);
        }
        SysUser sysUserFilter = new SysUser();
        sysUserFilter.setDeletedFlag(1);
        sysUserFilter.setCreateTimeStart(sysUserDto.getCreateTimeStart());
        sysUserFilter.setCreateTimeEnd(sysUserDto.getCreateTimeEnd());
        List<ApiSysUserVo> userList = sysUserMapper.getSysUserApiList(sysUserFilter,"create_time desc");
        return MyPageUtil.makeResponseData(userList);
    }
}
