package com.soft.admin.upms.controller;

import com.soft.admin.upms.dto.ThreePartyRobotDTO;
import com.soft.admin.upms.service.ThreePartyRobotService;
import com.soft.admin.upms.vo.ThreePartyRobotVO;
import com.soft.common.core.object.IdDTO;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.MyPageParam;
import com.soft.common.core.object.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Description 三方机器人配置控制器类
 * @Date 0023, 2023年5月23日 14:14
 * <AUTHOR>
 **/
@Api("三方机器人配置接口")
@RestController
@RequestMapping("/system/threeParty/robot")
public class ThreePartyRobotController {

    @Resource
    private ThreePartyRobotService threePartyRobotService;

    /**
     * 新增机器人配置
     *
     * @param addDTO
     * @return
     */
    @ApiOperation(value = "新增机器人配置")
    @PostMapping("/add")
    public ResponseResult add( @RequestBody ThreePartyRobotDTO addDTO) {
        threePartyRobotService.addRobot( addDTO);
        return ResponseResult.success();
    }

    /**
     * 修改机器人配置
     *
     * @param updateDTO
     * @return
     */
    @ApiOperation(value = "修改机器人配置")
    @PostMapping("/update")
    public ResponseResult update( @RequestBody ThreePartyRobotDTO updateDTO) {
        threePartyRobotService.updateRobot( updateDTO);
        return ResponseResult.success();
    }

    /**
     * 删除机器人配置
     *
     * @param idDTO
     * @return
     */
    @ApiOperation(value = "删除机器人配置")
    @PostMapping("/delete")
    public ResponseResult delete( @RequestBody IdDTO idDTO) {
        threePartyRobotService.deleteRobot( idDTO.getId());
        return ResponseResult.success();
    }

    /**
     * 查询机器人配置（分页）
     *
     * @param pageParam
     * @return
     */
    @ApiOperation(value = "查询机器人配置（分页）")
    @GetMapping("/page")
    public ResponseResult<MyPageData<ThreePartyRobotVO>> page( MyPageParam pageParam) {
        return ResponseResult.success(threePartyRobotService.getRobotPage( pageParam));
    }
}
