package com.soft.admin.upms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.soft.admin.upms.dao.ThreePartyRobotMapper;
import com.soft.admin.upms.dto.ThreePartyRobotDTO;
import com.soft.admin.upms.model.ThreePartyRobot;
import com.soft.admin.upms.service.ThreePartyRobotService;
import com.soft.admin.upms.vo.ThreePartyRobotVO;
import com.soft.common.core.base.dao.BaseDaoMapper;
import com.soft.common.core.base.service.BaseService;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.MyPageParam;
import com.soft.common.core.util.MyPageUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * 三方机器人配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-23
 */
@Service
public class ThreePartyRobotServiceImpl extends BaseService<ThreePartyRobot, Long> implements ThreePartyRobotService {

    @Autowired
    private ThreePartyRobotMapper threePartyRobotMapper;

    @Override
    protected BaseDaoMapper<ThreePartyRobot> mapper() {
        return threePartyRobotMapper;
    }

    @Override
    public void addRobot( ThreePartyRobotDTO addDTO) {
        ThreePartyRobot robot = new ThreePartyRobot();
        buildModel( addDTO, robot);
        threePartyRobotMapper.insert(robot);
    }

    @Override
    public void updateRobot( ThreePartyRobotDTO updateDTO) {
        ThreePartyRobot robot = threePartyRobotMapper.selectById(updateDTO.getId());
        Assert.notNull(robot, "该机器人配置不存在");
        buildModel( updateDTO, robot);
        threePartyRobotMapper.updateById(robot);
    }

    @Override
    public void deleteRobot( Long id) {
        threePartyRobotMapper.delete(new LambdaQueryWrapper<ThreePartyRobot>()
                .eq(ThreePartyRobot::getId, id));
    }

    @Override
    public MyPageData<ThreePartyRobotVO> getRobotPage( MyPageParam pageParam) {
        PageHelper.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        Page<ThreePartyRobot> page = (Page<ThreePartyRobot>) threePartyRobotMapper
                .selectList(new LambdaQueryWrapper<ThreePartyRobot>());
        return MyPageUtil.makeResponseData(ThreePartyRobot.INSTANCE.fromModelList(page), page.getTotal());
    }

    private void buildModel( ThreePartyRobotDTO addDTO, ThreePartyRobot robot) {
        robot.setName(addDTO.getName());
        robot.setType(addDTO.getType());
        robot.setWebHook(addDTO.getWebHook());
        robot.setSecret(addDTO.getSecret());
        robot.setRemark(addDTO.getRemark());
        robot.setPlatform(addDTO.getPlatform());
    }
}
