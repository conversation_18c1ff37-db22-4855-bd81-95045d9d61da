package com.rutong.medical.admin.controller.alarm;

import com.rutong.medical.admin.service.alarm.AlarmDetailService;
import com.rutong.medical.admin.vo.alarm.AlarmDetailTDVO;
import com.soft.common.core.object.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName AlarmDetailController
 * @Description
 * <AUTHOR>
 * @Date 2025/7/22 17:07
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Api(tags = "报警记录")
@RestController
@RequestMapping("/alarm")
@AllArgsConstructor
public class AlarmDetailController {

    private AlarmDetailService alarmDetailService;

    /**
     * 获取报警记录列表
     * @return
     */
    @ApiOperation("获取所有报警记录")
    @GetMapping("/getAlarmDetailList")
    public ResponseResult<List<AlarmDetailTDVO>> getAlarmDetailList(){
        return ResponseResult.success(alarmDetailService.getAlarmDetailList());
    }
}
