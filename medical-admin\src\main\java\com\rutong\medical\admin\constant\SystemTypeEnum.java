package com.rutong.medical.admin.constant;

import lombok.Data;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统类型枚举
 */
@Getter
public enum SystemTypeEnum {

    // 枚举项
    MEDICAL_SECURITY("medical_security", "平安医护管理系统"),
    WIRELESS_ALARM("wireless_alarm", "无线入侵报警系统"),
    ASSET_TRACKING("asset_tracking", "资产定位系统"),
    DEVICE_EFFICIENCY("device_efficiency", "设备能效分析系统"),
    VISUAL_PATROL("visual_patrol", "可视化巡更系统"),
    VITAL_MONITOR("vital_monitor", "体征监测系统"),
    SPECIAL_CARE("special_care", "特殊病患看护系统"),
    MATERNAL_CHILD("maternal_child", "母婴守护系统"),
    SMART_INFUSION("smart_infusion", "智慧输液系统");

    private final String code;
    private final String name;

    SystemTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 获取所有系统的 code 和 name 列表
     */
    public static List<Map<String, String>> getAllSystems() {
        List<Map<String, String>> list = new ArrayList<>();
        for (SystemTypeEnum system : SystemTypeEnum.values()) {
            Map<String, String> map = new HashMap<>();
            map.put("typeCode", system.getCode());
            map.put("name", system.getName());
            list.add(map);
        }
        return list;
    }

    /**
     * 根据 code 获取对应的 name
     */
    public static String getNameByCode(String code) {
        for (SystemTypeEnum system : SystemTypeEnum.values()) {
            if (system.getCode().equals(code)) {
                return system.getName();
            }
        }
        return null;
    }
}
