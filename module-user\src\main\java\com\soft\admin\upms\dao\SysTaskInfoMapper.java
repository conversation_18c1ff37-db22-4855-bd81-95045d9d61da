package com.soft.admin.upms.dao;


import com.soft.common.core.base.dao.BaseDaoMapper;
import com.soft.admin.upms.model.SysTaskInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName: SysTaskInfoMapper
 * @Description: by CodeGenerate
 * @date 2021-4-12
 */
public interface SysTaskInfoMapper extends BaseDaoMapper<SysTaskInfo> {

    /*
     *
     * @Description 查询list
     * <AUTHOR>
     * @Date 2021/4/13
     * @param ids
     * @return java.util.List<com.iricto.soft.system.entity.SysTaskInfo>
     **/
    public List<SysTaskInfo> getListByIds(@Param("ids") Integer[] ids);

    /*
     *
     * @Description 批量更新任务状态
     * <AUTHOR>
     * @Date 2021/4/14
     * @param list
     * @return int
     **/
    public int updateTriggerStatus(@Param("ids") Integer[] ids, @Param("triggerStatus") Integer triggerStatus);

    /*
     *
     * @Description 查询
     * <AUTHOR>
     * @Date 2021/4/15
     * @param refTable
     * @param refId
     * @return com.iricto.soft.system.entity.SysTaskInfo
     **/
    public SysTaskInfo getModelByParams(@Param("refTable") String refTable, @Param("refId") String refId);

    /*
     *
     *
     * @Description 批量删除
     * <AUTHOR>
     * @Date 2021/4/14
     * @param ids
     * @return int
     **/
    public int delByIds(@Param("ids") Long[] ids);

    /*
     *
     *
     * @Description 查询网关定时任务数据
     * <AUTHOR>
     * @Date 2021/4/20
     * @param configId
     * @return java.util.List<com.iricto.soft.system.entity.SysTaskInfo>
     **/
    public List<SysTaskInfo> queryNetworkTaskListByConfigId(Integer configId);

    /*
     *
     *
     * @Description //TODO 方法描述
     * <AUTHOR>
     * @Date 2021/8/30
     * @param taskInfo
     * @return java.util.List<com.iricto.soft.sys.bean.SysTaskInfo>
     **/
    public List<SysTaskInfo> getList(SysTaskInfo taskInfo);

}