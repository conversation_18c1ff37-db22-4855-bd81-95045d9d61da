<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.ThreePartyRobotMapper">
    <resultMap type="com.soft.admin.upms.model.ThreePartyRobot" id="ThreePartyRobotResult">
        <result property="id" column="id" />
        <result property="name" column="name" />
        <result property="type" column="type" />
        <result property="webHook" column="web_hook" />
        <result property="secret" column="secret" />
        <result property="remark" column="remark" />
        <result property="createTime" column="create_time" />
        <result property="createUserId" column="create_user_id" />
        <result property="updateTime" column="update_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="platform" column="platform"/>
    </resultMap>

    <sql id="selectThreePartyRobotVo">
        select id, platform, name, type, web_hook, secret, remark, create_time, create_user_id, update_time, update_user_id from sp_three_party_robot
    </sql>

</mapper>