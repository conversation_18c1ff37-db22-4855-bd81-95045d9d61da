package com.soft.common.core.config;

import com.soft.common.core.util.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.InitBinder;

import java.beans.PropertyEditorSupport;
import java.text.ParseException;
import java.util.Date;


/**
 * 全局 GET 请求实体中时间类型字段 数据绑定 问题
 */
@ControllerAdvice
public class GlobalGetDateConverterAdvice {

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) throws ParseException {
        if (str == null) {
            return null;
        }
        return DateUtils.parseDate(str.toString(), "yyyy-MM-dd HH:mm");
    }

    /**
     * 将前台传递过来的日期格式的字符串，自动转化为Date类型
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // Date 类型转换
        binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                // 适配：Long转Date
                if (StringUtils.isNotEmpty(text) && StringUtils.isNumeric(text)) {
                    setValue(new Date(Long.parseLong(text)).toInstant());
                    return;
                }
                try {
                    setValue(parseDate(text));
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }
        });
    }

}