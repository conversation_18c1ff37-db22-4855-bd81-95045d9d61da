package com.soft.admin.upms.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 组织架构信息
 * @date 2023-06-27  16:39:01
 */
@Data
public class SysOrganizationVO {

    @ApiModelProperty(value = "根节点")
    private String rootId;

    @ApiModelProperty(value = "组织架构节点")
    private List<Node> nodes;

    @ApiModelProperty(value = "节点连线")
    private List<Line> lines;


    /**
     * 节点
     */
    @Data
    public static class Node {

        private String id;

        private String text;

        private String color;

        private List<ComponentUser> data;
    }

    /**
     * 成员用户
     */
    @Data
    public static class ComponentUser {

        @ApiModelProperty(value = "用户名称")
        private String username;

        @ApiModelProperty(value = "手机号码")
        private String phone;

        @ApiModelProperty(value = "岗位名称")
        private List<String> postNames;
    }

    /**
     * 连线
     */
    @Data
    public static class Line {

        private String from;

        private String to;

        private String text;
    }
}
