package com.soft.admin.upms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class SysInfoDTO {

    @ApiModelProperty(value = "主键ID")
    @NotNull(message = "主键ID不能为空！")
    private Long id;

    @ApiModelProperty(value = "信息配置内容")
    @NotBlank(message = "信息配置内容不能为空！")
    private String content;

}
