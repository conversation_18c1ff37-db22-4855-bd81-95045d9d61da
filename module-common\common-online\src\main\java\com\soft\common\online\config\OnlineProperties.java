package com.soft.common.online.config;

import com.soft.common.core.config.CoreProperties;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * 在线表单的配置对象。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
@Data
@ConfigurationProperties(prefix = "common-online")
public class OnlineProperties {

    /**
     * 数据库类型。
     */
    private String databaseType = CoreProperties.MYSQL_TYPE;
    /**
     * 仅以该前缀开头的数据表才会成为动态表单的候选数据表，如: zz_。如果为空，则所有表均可被选。
     */
    private String tablePrefix;
    /**
     * 在线表单业务操作的URL前缀。
     */
    private String operationUrlPrefix;
    /**
     * 上传文件的根路径。
     */
    private String uploadFileBaseDir;
    /**
     * 1: minio 2: aliyun-oss 3: qcloud-cos。
     * 0是本地系统，不推荐使用。
     */
    private Integer distributeStoreType;
    /**
     * 在线表单查看权限的URL列表。
     */
    private List<String> viewUrlList;
    /**
     * 在线表单编辑权限的URL列表。
     */
    private List<String> editUrlList;
}
