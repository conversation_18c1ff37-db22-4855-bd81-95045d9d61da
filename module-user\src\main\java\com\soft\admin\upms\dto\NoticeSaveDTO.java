package com.soft.admin.upms.dto;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * NoticeDTO对象
 *
 * <AUTHOR>
 * @date 2023-07-03
 */
@ApiModel("NoticeSaveDTO对象")
@Data
public class NoticeSaveDTO {

    @ApiModelProperty(value = "id")
    @NotNull(message = "数据验证失败，id不能为空！", groups = {UpdateGroup.class})
    private Long id;



    @ApiModelProperty(value = "类型")
    private String type;


    @ApiModelProperty(value = "标题")
    private String title;


    @ApiModelProperty(value = "内容")
    private String content;


    @ApiModelProperty(value = "状态：发布1，未发布0")
    private Integer status;


    @ApiModelProperty(value = "有效期：开始时间")
    private Date beginTime;


    @ApiModelProperty(value = "有效期：结束时间")
    private Date endTime;


    @ApiModelProperty(value = "排序")
    private Integer showOrder;


    @ApiModelProperty(value = "用户id")
    private List<Long> userIdList;

}
