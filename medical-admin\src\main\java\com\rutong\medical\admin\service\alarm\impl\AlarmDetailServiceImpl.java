package com.rutong.medical.admin.service.alarm.impl;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.date.DateUtil;
import com.rutong.medical.admin.mapper.alarm.AlarmDetailTDMapper;
import com.rutong.medical.admin.vo.alarm.AlarmDetailTDVO;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rutong.medical.admin.entity.alarm.AlarmDetail;
import com.rutong.medical.admin.mapper.alarm.AlarmDetailMapper;
import com.rutong.medical.admin.service.alarm.AlarmDetailService;

/**
 * @ClassName AlarmDetailServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/7/15 10:24
 * @Version 1.0
 * @Copyright © 2025 All Rights Reserved
 */
@Service
@AllArgsConstructor
public class AlarmDetailServiceImpl extends ServiceImpl<AlarmDetailMapper, AlarmDetail> implements AlarmDetailService {

    private AlarmDetailMapper alarmDetailMapper;
    private AlarmDetailTDMapper alarmDetailTDMapper;

    @Override
    public Map<String, String> getDeviceStatus(List<String> deviceSns) {
        // 查询所有未处理的告警信息
        List<AlarmDetail> alarmDetails = alarmDetailMapper.selectList(Wrappers.lambdaQuery(AlarmDetail.class)
            .in(AlarmDetail::getDeviceSn, deviceSns).eq(AlarmDetail::getDisposeState, 0));

        // 将 alarmDetails 转为 Map<String, List<AlarmDetail>>，key 是 deviceSn
        Map<String, List<AlarmDetail>> alarmMap =
            alarmDetails.stream().collect(Collectors.groupingBy(AlarmDetail::getDeviceSn));

        // 构建结果Map
        Map<String, String> result = new HashMap<>();
        for (String deviceSn : deviceSns) {
            List<AlarmDetail> alarmDetailList = alarmMap.get(deviceSn);
            if (CollectionUtils.isNotEmpty(alarmDetailList)) {
                result.put(deviceSn, deviceSn);
            }

        }
        return result;
    }

    @Override
    public List<AlarmDetailTDVO> getAlarmDetailList() {
        Date beginOfDay = DateUtil.beginOfDay(new Date());
        Date endOfDay = DateUtil.endOfDay(new Date());
        return alarmDetailTDMapper.selectAlarmDetailList(beginOfDay,endOfDay);
    }

}
