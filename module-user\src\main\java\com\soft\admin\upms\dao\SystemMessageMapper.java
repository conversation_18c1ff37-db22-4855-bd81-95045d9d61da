package com.soft.admin.upms.dao;

import com.soft.common.core.base.dao.BaseDaoMapper;
import com.soft.admin.upms.model.SystemMessage;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 系统消息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-06-26
 */
public interface SystemMessageMapper extends BaseDaoMapper<SystemMessage> {

    /**
     * 批量新增系统消息
     * @param list
     * @return
     */
    int batchInsert(@Param("list") List<SystemMessage> list);

    List<SystemMessage> selectByIds(@Param("idList") Set<Long> idList);
}
