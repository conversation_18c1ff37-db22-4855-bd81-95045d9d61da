package com.soft.admin.upms.dto;

import com.soft.common.core.validator.UpdateGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class SysUserSaveOrUpdateDTO {

    @NotNull(message = "用户id不能为空！", groups = {UpdateGroup.class})
    private Long userId;
    /**
     * 终端信息
     */
    private String deviceSn;

    // 登录账号
    private String loginName;

    // 用户姓名
    private String showName;

    // 密码
    private String password;

    //人脸照
    private String facePicture;

    // 确认密码
    private String confirmPassword;

    // 性别：1男，2女，3未知
    private Integer sex;

    // 手机号
    private String phone;

    /**
     * 手机短号
     */
    @ApiModelProperty(value = "手机短号")
    private String shortPhone;

    /**
     * 头像
     */
    @ApiModelProperty(value = "头像")
    private String photoUrl;
    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String employeeNumber;

    /**
     * 分机号
     */
    @ApiModelProperty(value = "分机号")
    private String extensionNumber;

    /**
     * 身份证号
     */
    private String cardNo;

    // 部门
    private List<Long> deptId;

    // 角色
    private List<Long> roleIds;

    // 岗位
    private List<Long> deptPostIds;

    // 标签
    private List<Long> tagIds;

    // 权限组 id，注意：权限组在 sub 模块，无法直接调用
    // 所以用户保存成功后，再调用保存 用户权限组 的接口
    // private List<Long> powerGroupIds;
}
