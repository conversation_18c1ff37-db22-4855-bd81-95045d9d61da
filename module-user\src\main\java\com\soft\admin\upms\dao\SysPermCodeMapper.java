package com.soft.admin.upms.dao;

import com.soft.admin.upms.model.SysPermCode;
import com.soft.common.core.base.dao.BaseDaoMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 权限字数据访问操作接口。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
public interface SysPermCodeMapper extends BaseDaoMapper<SysPermCode> {

    /**
     * 获取用户的所有权限字列表。
     *
     * @param userId 用户Id。
     * @return 该用户的权限字列表。
     */
    List<String> getPermCodeListByUserId(@Param("userId") Long userId);


    /**
     * 获取用户的所有权限组列表。
     *
     * @param userId 用户Id。
     * @return 该用户的权限字列表。
     */
    List<String> getGroupPermListByUserId(@Param("userId") Long userId,@Param("permType") String permType);


    /**
     * 获取用户是否有全部权限组列表。
     *
     * @return 包含用户关联的权限组。
     */
    List<Map<String, BigDecimal>> getAllGroupPermByUserId(@Param("userId") Long userId);


    /**
     * 查询权限字的用户列表。同时返回详细的分配路径。
     *
     * @param permCodeId 权限字Id。
     * @param loginName  登录名。
     * @return 包含从权限字到用户的完整权限分配路径信息的查询结果列表。
     */
    List<Map<String, Object>> getSysUserListWithDetail(
            @Param("permCodeId") Long permCodeId, @Param("loginName") String loginName);

    /**
     * 查询权限字的角色列表。同时返回详细的分配路径。
     *
     * @param permCodeId 权限字Id。
     * @param roleName   角色名。
     * @return 包含从权限字到角色的权限分配路径信息的查询结果列表。
     */
    List<Map<String, Object>> getSysRoleListWithDetail(
            @Param("permCodeId") Long permCodeId, @Param("roleName") String roleName);

    /**
     * 插入权限组到用户关联表。
     * @param userId
     * @param permGroupId
     */
    void insertUserPermGroup( @Param("userId") Long userId,@Param("permGroupId") String permGroupId);
}
