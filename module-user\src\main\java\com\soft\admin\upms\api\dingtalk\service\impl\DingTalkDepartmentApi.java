package com.soft.admin.upms.api.dingtalk.service.impl;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiV2DepartmentGetRequest;
import com.dingtalk.api.request.OapiV2DepartmentListsubRequest;
import com.dingtalk.api.response.OapiV2DepartmentGetResponse;
import com.dingtalk.api.response.OapiV2DepartmentListsubResponse;
import com.soft.admin.upms.api.dingtalk.DingTalkConfig;
import com.soft.admin.upms.api.dingtalk.DingTalkConstants;
import com.soft.admin.upms.api.dingtalk.service.IOaDepartmentApi;
import com.soft.admin.upms.dto.dintalk.OaDepartmentDTO;
import com.soft.common.core.exception.ServiceException;
import com.soft.common.core.util.StringUtils;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description 钉钉部门api
 * @Date 0009, 2023年8月9日 14:01
 * <AUTHOR>
 **/
@Slf4j
@Service
public class DingTalkDepartmentApi implements IOaDepartmentApi {
    @Resource
    private DingTalkConfig dingTalkConfig;
    @Resource
    private DingTalkOauth2Api dingTalkOauth2Api;

    @Override
    public List<OaDepartmentDTO> getDeptSubList(String oaDeptId) {
        // 添加200ms休眠
        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        DingTalkClient client = new DefaultDingTalkClient(
                dingTalkConfig.getServerUrl(DingTalkConstants.API_GET_DEPT_SUB_LIST));
        OapiV2DepartmentListsubRequest req = new OapiV2DepartmentListsubRequest();
        if (StringUtils.isBlank(oaDeptId)) {
            req.setDeptId(1L);
        } else {
            req.setDeptId(Long.parseLong(oaDeptId));
        }

        req.setLanguage("zh_CN");
        OapiV2DepartmentListsubResponse rsp = null;
        try {
            rsp = client.execute(req, dingTalkOauth2Api.getAccessToken());
        } catch (ApiException e) {
            log.error("ding talk api getDeptList error: {}", e.getErrMsg());
            throw new ServiceException(e.getErrMsg());
        }
        log.info("ding talk api getDeptList: {}", rsp.getBody());
        if (!rsp.isSuccess()) {
            throw new ServiceException(rsp.getErrmsg());
        }
        List<OaDepartmentDTO> oaDepartmentList = Lists.newArrayList();
        for (OapiV2DepartmentListsubResponse.DeptBaseResponse deptBaseResponse : rsp.getResult()) {
            OaDepartmentDTO oaDepartmentDTO = new OaDepartmentDTO();
            oaDepartmentDTO.setName(deptBaseResponse.getName());
            oaDepartmentDTO.setOaDeptId(deptBaseResponse.getDeptId().toString());
            oaDepartmentDTO.setOaParentId(oaDeptId.toString());
            oaDepartmentList.add(oaDepartmentDTO);
        }
        return oaDepartmentList;
    }

    @Override
    public OaDepartmentDTO getDeptById(String oaDeptId) {
        DingTalkClient client = new DefaultDingTalkClient(
                dingTalkConfig.getServerUrl(DingTalkConstants.API_GET_DEPT));
        OapiV2DepartmentGetRequest req = new OapiV2DepartmentGetRequest();
        req.setDeptId(Long.parseLong(oaDeptId));
        req.setLanguage("zh_CN");
        OapiV2DepartmentGetResponse rsp = null;
        try {
            rsp = client.execute(req, dingTalkOauth2Api.getAccessToken());
        } catch (ApiException e) {
            log.error("ding talk api getDeptById error: {}", e.getErrMsg());
            throw new ServiceException(e.getErrMsg());
        }
        log.info("ding talk api getDeptById: {}", rsp.getBody());
        if (!rsp.isSuccess()) {
            throw new ServiceException(rsp.getErrmsg());
        }
        OaDepartmentDTO oaDepartmentDTO = new OaDepartmentDTO();
        oaDepartmentDTO.setName(rsp.getResult().getName());
        oaDepartmentDTO.setOaDeptId(rsp.getResult().getDeptId().toString());
        if (Objects.nonNull(rsp.getResult().getParentId())) {
            oaDepartmentDTO.setOaParentId(rsp.getResult().getParentId().toString());
        }
        return oaDepartmentDTO;
    }

    @Override
    public List<OaDepartmentDTO> getDeptListByIds(List<String> oaDeptIds) {
        return oaDeptIds.stream().map(this::getDeptById).collect(Collectors.toList());
    }
}
