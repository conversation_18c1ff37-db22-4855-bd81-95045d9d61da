package com.rutong.medical.admin.constant;

/**
 * 在线状态枚举
 */
public enum OnlineStatusEnum {

    OFFLINE(0, "离线"),
    ONLINE(1, "在线");

    private final int value;
    private final String desc;

    OnlineStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据值获取描述
     */
    public static String getDescByValue(int value) {
        for (OnlineStatusEnum status : values()) {
            if (status.getValue() == value) {
                return status.getDesc();
            }
        }
        return "未知状态";
    }
}
