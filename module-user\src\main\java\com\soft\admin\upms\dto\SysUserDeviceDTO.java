package com.soft.admin.upms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * @className: SysUserDevice
 * @author: <PERSON>ZeYu
 * @date: 2024/9/13 上午9:21
 * @description:
 */
@Data
public class SysUserDeviceDTO {
    /**
     * 设备id
     */
    @NotBlank(message = "设备id不能为空")
    @ApiModelProperty(value = "设备id", required = true)
    private String registrationId;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id", required = false)
    private Long userId;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", required = false)
    private Date createTime;
}
