package com.rutong.medical.admin.entity.alarm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025-07-15
 * 报警设置表实体类
 */
@Data
@TableName("sm_alarm_config")
@ApiModel(value = "报警设置表")
public class SmAlarmConfig {
    
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "报警设置表ID")
    private Long id;
    
    @ApiModelProperty(value = "编号")
    private String configCode;
    
    @ApiModelProperty(value = "名称")
    private String configName;
    
    @ApiModelProperty(value = "值")
    private Integer configValue;
    
    @ApiModelProperty(value = "报警类型(1:医护工卡,2:报警按钮,3:红外探测器,4:资产定位标签)")
    private Integer configType;
    
    @ApiModelProperty(value = "修改人")
    private Long updateUserId;
    
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}
