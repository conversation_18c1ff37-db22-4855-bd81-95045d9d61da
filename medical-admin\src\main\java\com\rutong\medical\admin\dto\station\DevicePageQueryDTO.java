package com.rutong.medical.admin.dto.station;

import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("DevicePageQueryDTO")
@Data
public class DevicePageQueryDTO extends MyPageParam {

    @ApiModelProperty(value = "名称/编号")
    private String keyWord;

    @ApiModelProperty(value = "位置")
    private String spacePath;

    @ApiModelProperty(value = "设备分类表ID")
    private Long deviceTerminalTypeId;

    @ApiModelProperty(value = "在线状态(1:在线,0:离线) ")
    private Integer isOnline;

    @ApiModelProperty(value = "业务系统编号")
    private String businessCode;

    private List<Long> deviceTerminalTypeIdList;
}
