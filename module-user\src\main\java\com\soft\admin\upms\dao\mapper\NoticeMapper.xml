<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.NoticeMapper">
    <resultMap type="com.soft.admin.upms.model.Notice" id="NoticeResult">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="status" column="status"/>
        <result property="beginTime" column="begin_time"/>
        <result property="endTime" column="end_time"/>
        <result property="showOrder" column="show_order"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUserId" column="update_user_id"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectNoticeVo">
        id, `type`, title, content, status, begin_time, end_time, show_order, create_user_id, create_time, update_user_id, update_time
    </sql>

    <select id="queryList" resultType="com.soft.admin.upms.vo.NoticeVO"
            parameterType="com.soft.admin.upms.dto.NoticeQueryDTO">
        select<include refid="selectNoticeVo"/>, user_ids from
        (
        select<include refid="selectNoticeVo"/>,
        (select GROUP_CONCAT(user_id) from sp_notice_user_relation where notice_id = t.id) user_ids
        from sp_notice t
        ) t1
        <where>
            <if test="type != null and type != ''">
                and `type` = #{type}
            </if>
            <if test="title != null and title != ''">
                and title like concat('%', #{title}, '%')
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="source == 'app'">
                and
                (
                (type = '公告' and (CURDATE() between begin_time and end_time))
                or
                (type = '通知' and FIND_IN_SET(#{userId}, user_ids))
                )
            </if>
        </where>
        order by create_time desc
    </select>

</mapper>