package com.soft.admin.upms.dto;

import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description 系统消息分页查询DTO
 * @Date 0026, 2023年6月26日 14:01
 * <AUTHOR>
 **/
@ApiModel("系统消息分页查询DTO")
@Data
public class SystemMessagePageDTO extends MyPageParam {

    /**
     * 是否已读
     */
    @ApiModelProperty("是否已读")
    private Boolean isRead;

    @ApiModelProperty(hidden = true)
    private Long userId;

    @ApiModelProperty("时间")
    private String date;
}
