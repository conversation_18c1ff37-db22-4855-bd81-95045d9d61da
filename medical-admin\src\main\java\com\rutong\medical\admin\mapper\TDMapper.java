package com.rutong.medical.admin.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rutong.medical.admin.entity.AlarmDetailTD;
import com.rutong.medical.admin.entity.Test;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * @ClassName TDMapper
 * @Description
 * <AUTHOR>
 * @Date 2025/7/11 15:04
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@DS("tdengine")
public interface TDMapper extends BaseMapper<AlarmDetailTD> {

//    @Select("SELECT alarm_date,is_alarm,is_key,alarm_detail_id FROM medical_iot.alarm_detail WHERE alarm_date >= #{start} AND alarm_date <= #{end}")
    List<AlarmDetailTD> selectByTimeRange(@Param("start") Timestamp start,
                                          @Param("end") Timestamp end);
}
