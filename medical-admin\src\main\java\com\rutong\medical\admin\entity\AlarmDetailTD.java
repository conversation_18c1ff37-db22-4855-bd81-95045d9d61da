package com.rutong.medical.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

/**
 * @ClassName AlarmDetailTD
 * @Description
 * <AUTHOR>
 * @Date 2025/7/11 15:09
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Data
@TableName("alarm_detail")
public class AlarmDetailTD {

    @TableId(value = "alarmDate", type = IdType.INPUT)
    private Timestamp alarmDate;
    private String alarmDetailId;
    private Byte isAlarm;
    private Byte isKey;
}
