
sever:
  port: 8081

spring:
  datasource:
    # 动态数据源配置
    dynamic:
      datasource:
        operation-log:
          type: com.alibaba.druid.pool.DruidDataSource
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **************************************************************************************************************
          username: root
          password: root123456

redis:
  # redisson的配置。每个服务可以自己的配置文件中覆盖此选项。
  redisson:
    # 如果该值为false，系统将不会创建RedissionClient的bean。
    enabled: true
    # mode的可用值为，single/cluster/sentinel/master-slave
    mode: single
    # single: 单机模式
    address: redis://*************:6379
    # 库
    database: 1
    # 链接超时，单位毫秒。
    timeout: 6000
    # 单位毫秒。分布式锁的超时检测时长。
    # 如果一次锁内操作超该毫秒数，或在释放锁之前异常退出，Redis会在该时长之后主动删除该锁使用的key。
    lockWatchdogTimeout: 60000
    # redis 密码，空可以不填。
    password: rutong123456
    pool:
      # 连接池数量。
      poolSize: 20
      # 连接池中最小空闲数量。
      minIdle: 5

mqtt:
  hostUrl: tcp://*************:1883
  username: admin
  password: public
  clientId: smart_park_local_${random.uuid}
  cleanSession: true
  reconnect: true
  timeout: 100
  keepAlive: 100


#流媒体服务，视频转码使用
mediaService:
  ip: ***************
  secret: rutong@123456
