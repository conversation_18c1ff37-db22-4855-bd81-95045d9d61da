package com.soft.admin.upms.api.robot;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.google.common.collect.Maps;
import com.soft.admin.upms.enums.ThreePartyPlatformEnums;
import com.taobao.api.ApiException;
import groovy.lang.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description 钉钉机器人
 * @Date 0025, 2023年5月25日 10:20
 * <AUTHOR>
 **/
@Slf4j
@Service
public class DingDingRobot implements IThreePartyRobot{

    /**
     * sign缓存
     */
    private static final Map<String, Tuple2<Long, String>> SIGN_CACHE_MAP = Maps.newHashMap();
    /**
     * sign有效期，单位ms
     * 钉钉有效期1小时
     */
    private static final long OFFSET_TIME = 1800000L;
    /**
     * sign提前刷新时间，单位ms
     */
    private static final long REFRESH_OFFSET_TIME = 60000L;

    @Override
    public String getPlatform() {
        return ThreePartyPlatformEnums.DINGDING.name();
    }

    @Override
    public void send(String webhook, String secret, String message, List<String> atUserList) {
        Tuple2<Long, String> signCache = null;
        try {
            signCache = getSign(secret);
        } catch (Exception e) {
            log.error("robot - dingding - getSign error", e);
            return;
        }
        Long timestamp = signCache.getV1();
        String sign = signCache.getV2();
        DingTalkClient client = new DefaultDingTalkClient(MessageFormat.format(webhook + "&timestamp={0}&sign={1}", timestamp.toString() ,sign));
        OapiRobotSendRequest request = new OapiRobotSendRequest();
        request.setMsgtype("text");
        OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
        text.setContent(message);
        request.setText(text);
        if(CollectionUtils.isNotEmpty(atUserList)) {
            OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
            at.setAtMobiles(atUserList);
            at.setIsAtAll(Boolean.FALSE);
            request.setAt(at);
        }
        try {
            OapiRobotSendResponse response = client.execute(request);
            if(!response.isSuccess()) {
                log.error("robot - dingding - error code:{}, message:{}", response.getErrcode(), response.getErrmsg());
            }
        } catch (ApiException e) {
            log.error("robot - dingding - send error", e);
        }
    }

    private Tuple2<Long, String> getSign(String secret) throws UnsupportedEncodingException, NoSuchAlgorithmException, InvalidKeyException {
        Tuple2<Long, String> signCache = SIGN_CACHE_MAP.get(secret);
        if(Objects.isNull(signCache) || (signCache.getV1() - REFRESH_OFFSET_TIME) <= System.currentTimeMillis()) {
            Long timestamp = System.currentTimeMillis() + OFFSET_TIME;
            signCache = new Tuple2<>(timestamp, getSign(secret, timestamp));
            SIGN_CACHE_MAP.put(secret, signCache);
        }
        return signCache;
    }


    private String getSign(String secret, Long timestamp) throws NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException {
        String stringToSign = timestamp + "\n" + secret;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256"));
        byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
        return URLEncoder.encode(new String(Base64.encodeBase64(signData)),"UTF-8");
    }


//        request.setMsgtype("link");
//        OapiRobotSendRequest.Link link = new OapiRobotSendRequest.Link();
//        link.setMessageUrl("https://www.dingtalk.com/");
//        link.setPicUrl("");
//        link.setTitle("时代的火车向前开");
//        link.setText("这个即将发布的新版本，创始人xx称它为红树林。而在此之前，每当面临重大升级，产品经理们都会取一个应景的代号，这一次，为什么是红树林");
//        request.setLink(link);

//        request.setMsgtype("markdown");
//        OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
//        markdown.setTitle("杭州天气");
//        markdown.setText("#### 杭州天气 @156xxxx8827\n" +
//                "> 9度，西北风1级，空气良89，相对温度73%\n\n" +
//                "> ![screenshot](https://gw.alicdn.com/tfs/TB1ut3xxbsrBKNjSZFpXXcXhFXa-846-786.png)\n"  +
//                "> ###### 10点20分发布 [天气](http://www.thinkpage.cn/) \n");
//        request.setMarkdown(markdown);
}
