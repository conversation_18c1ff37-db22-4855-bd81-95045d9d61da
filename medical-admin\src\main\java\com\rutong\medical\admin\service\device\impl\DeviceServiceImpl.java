package com.rutong.medical.admin.service.device.impl;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.rutong.medical.admin.constant.DeviceTypeEnum;
import com.rutong.medical.admin.constant.OnlineStatusEnum;
import com.rutong.medical.admin.constant.SystemTypeEnum;
import com.rutong.medical.admin.constant.WhetherConstant;
import com.rutong.medical.admin.dto.device.DeviceTerminalSaveDTO;
import com.rutong.medical.admin.dto.station.DevicePageQueryDTO;
import com.rutong.medical.admin.entity.device.AlarmLocationDetailTD;
import com.rutong.medical.admin.entity.device.Device;
import com.rutong.medical.admin.entity.device.DeviceTD;
import com.rutong.medical.admin.entity.device.DeviceTerminalType;
import com.rutong.medical.admin.entity.system.Space;
import com.rutong.medical.admin.mapper.device.DeviceMapper;
import com.rutong.medical.admin.mapper.device.DeviceTDMapper;
import com.rutong.medical.admin.mapper.device.DeviceTerminalTypeMapper;
import com.rutong.medical.admin.mapper.system.SpaceMapper;
import com.rutong.medical.admin.service.alarm.AlarmDetailService;
import com.rutong.medical.admin.service.device.DeviceService;
import com.rutong.medical.admin.vo.device.DeviceVO;
import com.soft.common.core.constant.GlobalDeletedFlag;
import com.soft.common.core.exception.ServiceException;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.TokenData;
import com.soft.common.core.util.MyModelUtil;
import com.soft.common.core.util.MyPageUtil;
import com.soft.common.core.util.StringUtils;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 设备Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Service
@Slf4j
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, Device> implements DeviceService {

    @Autowired
    private DeviceMapper smDeviceMapper;
    @Resource
    private DeviceTerminalTypeMapper deviceTypeMapper;
    @Resource
    private SpaceMapper spaceMapper;
    @Resource
    private DeviceTDMapper deviceTDMapper;
    @Resource
    private AlarmDetailService alarmDetailService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        log.info("delete device id:{}", id);
        Device device = smDeviceMapper.selectById(id);
        if (device == null) {
            throw new ServiceException("设备不存在");
        }
        device.setUpdateTime(new Date());
        device.setIsDelete(GlobalDeletedFlag.DELETED);
        device.setUpdateUserId(TokenData.takeFromRequest().getUserId());
        smDeviceMapper.updateById(device);

        DeviceServiceImpl bean = SpringUtil.getBean(DeviceServiceImpl.class);
        bean.deleteAsync(device.getDeviceSn());
    }

    public DeviceServiceImpl() {
        super();
    }

    @Override
    public MyPageData<DeviceVO> page(DevicePageQueryDTO devicePageQueryDTO) {
        List<Long> deviceBaseStationTypeIdList = new ArrayList<>();
        if (devicePageQueryDTO.getDeviceTerminalTypeId() != null) {
            List<DeviceTerminalType> deviceTerminalTypes =
                deviceTypeMapper.selectList(Wrappers.lambdaQuery(DeviceTerminalType.class)
                    .like(DeviceTerminalType::getPathId, devicePageQueryDTO.getDeviceTerminalTypeId())
                    .eq(DeviceTerminalType::getIsDelete, GlobalDeletedFlag.NORMAL).select(DeviceTerminalType::getId));
            deviceBaseStationTypeIdList =
                deviceTerminalTypes.stream().map(DeviceTerminalType::getId).collect(Collectors.toList());
            devicePageQueryDTO.setDeviceTerminalTypeIdList(deviceBaseStationTypeIdList);

        }
        Integer pageNum = devicePageQueryDTO.getPageNum();
        Integer pageSize = devicePageQueryDTO.getPageSize();
        if (pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize);
        }
        List<Device> deviceList = smDeviceMapper.list(devicePageQueryDTO);
        if (CollectionUtils.isEmpty(deviceList)) {
            return MyPageData.emptyPageData();
        }
        List<String> deviceSnSet = deviceList.stream().map(Device::getDeviceSn).collect(Collectors.toList());
        Map<String, String> deviceStatusMap = alarmDetailService.getDeviceStatus(deviceSnSet);
        MyPageData<DeviceVO> pageData = MyPageData.emptyPageData();
        MyPageData<Device> deviceVOMyPageData = MyPageUtil.makeResponseData(deviceList);

        List<Device> dataList = deviceVOMyPageData.getDataList();
        if (CollectionUtils.isEmpty(dataList)) {
            return MyPageData.emptyPageData();
        }

        List<DeviceVO> deviceVOS = MyModelUtil.copyCollectionTo(dataList, DeviceVO.class);
        // 提取所有不重复的分类ID
        Set<Long> typeIds = deviceVOS.stream().map(DeviceVO::getDeviceTerminalTypeId).filter(Objects::nonNull)
            .collect(Collectors.toSet());

        // 查询分类名称并封装为Map
        Map<Long, String> typeNameMap = new HashMap<>();
        List<DeviceTerminalType> deviceBaseStationTypes =
            deviceTypeMapper.selectList(Wrappers.lambdaQuery(DeviceTerminalType.class)
                .in(DeviceTerminalType::getId, typeIds).eq(DeviceTerminalType::getIsDelete, GlobalDeletedFlag.NORMAL));
        typeNameMap = deviceBaseStationTypes.stream()
            .collect(Collectors.toMap(DeviceTerminalType::getId, DeviceTerminalType::getTypeName));

        // 转换 VO 并设置 typeName
        String tableNamePrefix = "alarm_location_detail";
        Map<Long, String> finalTypeNameMap = typeNameMap;
        deviceVOS.forEach(e -> {
            e.setDeviceTerminalTypeName(finalTypeNameMap.getOrDefault(e.getDeviceTerminalTypeId(), ""));
            String deviceSnStatus = deviceStatusMap.get(e.getDeviceSn());
            if (StringUtils.isNotBlank(deviceSnStatus)) {
                e.setDeviceStatus(WhetherConstant._NO.toString());
            } else {
                e.setDeviceStatus(WhetherConstant._YES.toString());
            }
            String tableName =
                tableNamePrefix + "_" + e.getDeviceSn() + "_" + DeviceTypeEnum.CARD_LORA.getCode().toString();

            try {
                AlarmLocationDetailTD spaceFullName = deviceTDMapper.getSpaceFullName(e.getDeviceSn(), tableName,
                    DeviceTypeEnum.CARD_LORA.getCode().toString());
                if (spaceFullName != null) {
                    String fullSpaceName = spaceFullName.getBuildingName() + " - " + spaceFullName.getFloorName()
                        + " - " + spaceFullName.getPointName();
                    e.setSpaceFullName(fullSpaceName);
                }
            } catch (Exception ex) {
                // 表不存在或查询失败，不中断主流程
                log.warn("查询 TDengine 表 {} 失败，设备 SN: {}", tableName, e.getDeviceSn(), ex);
                // 或者设置为空字符串 ""
                e.setSpaceFullName(null);
            }
        });

        pageData.setDataList(deviceVOS);
        pageData.setTotalCount(deviceVOMyPageData.getTotalCount());
        return pageData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(DeviceTerminalSaveDTO deviceTerminalSaveDTO) {
        // 新增操作
        Device device = MyModelUtil.copyTo(deviceTerminalSaveDTO, Device.class);
        Long id = deviceTerminalSaveDTO.getId();
        Long spaceId = deviceTerminalSaveDTO.getSpaceId();

        // 查询位置
        if (spaceId != null) {
            Space space = spaceMapper.selectById(spaceId);
            if (space != null) {
                device.setSpacePath(space.getPath());
                device.setSpaceFullName(space.getFullName());
            }
        }

        if (id == null) {
            Map<String, Object> paramsInsert = getStringObjectMap(device);

            // 校验设备编号或设备名称已存在
            if (smDeviceMapper.existsByDeviceNumberOrName(deviceTerminalSaveDTO.getDeviceCode(),
                deviceTerminalSaveDTO.getDeviceName()) > 0) {
                throw new ServiceException("设备编号或设备名称已存在");
            }
            // 新增操作
            device.setIsOnline(OnlineStatusEnum.OFFLINE.getValue());
            device.setCreateTime(new Date());
            device.setIsDelete(GlobalDeletedFlag.NORMAL);
            device.setCreateTime(new Date());
            device.setCreateUserId(TokenData.takeFromRequest().getUserId());
            smDeviceMapper.insert(device);
            paramsInsert.put("id", device.getId());
            DeviceServiceImpl bean = SpringUtil.getBean(DeviceServiceImpl.class);
            bean.insertIntoDeviceTable(paramsInsert, device.getId());
        } else {
            // 修改操作
            if (smDeviceMapper.existsByDeviceNumberOrNameExcludingId(deviceTerminalSaveDTO.getDeviceCode(),
                deviceTerminalSaveDTO.getDeviceName(), deviceTerminalSaveDTO.getId()) > 0) {
                throw new ServiceException("设备编号或设备名称已存在");
            }
            device.setUpdateTime(new Date());
            device.setUpdateUserId(TokenData.takeFromRequest().getUserId());
            smDeviceMapper.updateDeviceById(device);

            Map<String, Object> paramsInsert = getStringObjectMap(device);
            paramsInsert.put("id", device.getId());
            DeviceServiceImpl bean = SpringUtil.getBean(DeviceServiceImpl.class);
            bean.updateDeviceTable(paramsInsert);
        }
    }

    /**
     * 封装对象
     * 
     * @param device
     * @return
     */
    private static @NotNull Map<String, Object> getStringObjectMap(Device device) {
        Long userId = TokenData.takeFromRequest().getUserId();
        Map<String, Object> deviceData = new HashMap<>();
        deviceData.put("id", device.getId());
        deviceData.put("deviceTerminalTypeId", device.getDeviceTerminalTypeId());
        deviceData.put("deviceCode", device.getDeviceCode());
        deviceData.put("deviceSn", device.getDeviceSn());
        deviceData.put("deviceName", device.getDeviceName());
        deviceData.put("businessCode", device.getBusinessCode());
        deviceData.put("spaceId", device.getSpaceId());
        deviceData.put("x", device.getX());
        deviceData.put("y", device.getY());
        deviceData.put("z", device.getZ());
        deviceData.put("longitude", device.getLongitude());
        deviceData.put("latitude", device.getLatitude());
        deviceData.put("createUserId", userId);
        Timestamp timestamp = new Timestamp(new Date().getTime());
        deviceData.put("createTime", timestamp);
        deviceData.put("isOnline", OnlineStatusEnum.OFFLINE.getValue());
        deviceData.put("updateTime", timestamp);
        deviceData.put("updateUserId", userId);
        // 组装参数Map
        Map<String, Object> paramsInsert = new HashMap<>();
        paramsInsert.put("tableName", "device_" + device.getDeviceSn());
        paramsInsert.put("deviceData", deviceData);
        return paramsInsert;
    }

    @Override
    public List<Map<String, String>> listSystem() {
        return SystemTypeEnum.getAllSystems();
    }

    @Override
    public DeviceVO detail(Long id) {
        Device device = smDeviceMapper.selectById(id);
        if (device == null) {
            return null;
        }
        DeviceVO deviceVO = MyModelUtil.copyTo(device, DeviceVO.class);
        return deviceVO;
    }

    @Override
    public List<DeviceVO> list(DevicePageQueryDTO devicePageQueryDTO) {
        List<Device> deviceList = smDeviceMapper.list(devicePageQueryDTO);
        if (CollectionUtils.isEmpty(deviceList)) {
            return Collections.emptyList();
        }
        return MyModelUtil.copyCollectionTo(deviceList, DeviceVO.class);
    }

    /**
     * 异步新增设备到时序数据库
     * 
     * @param paramsInsert
     */
    @Async("applicationTaskExecutor")
    public void insertIntoDeviceTable(Map<String, Object> paramsInsert, Long id) {
        try {
            deviceTDMapper.insertIntoDeviceTable(paramsInsert);
        } catch (Exception e) {
            log.error("TDengine insert failed, rollback MySQL", e);
            smDeviceMapper.deleteById(id);
        }
    }

    /**
     * 异步更新设备到时序数据库
     * 
     * @param paramsInsert
     */
    @Async("applicationTaskExecutor")
    public void updateDeviceTable(Map<String, Object> paramsInsert) {
        Map<String, Object> deviceData = (Map<String, Object>)paramsInsert.get("deviceData");
        Long id = (Long)deviceData.get("id");
        String deviceSn = (String)deviceData.get("deviceSn");
        try {
            DeviceTD deviceTD = deviceTDMapper.selectById(id);
            if (deviceTD != null) {
                deviceTDMapper.deleteByDeviceSn("device_" + deviceSn);
            }
            paramsInsert.put("createTime", deviceTD.getCreateTime());
            deviceTDMapper.insertIntoDeviceTable(paramsInsert);
        } catch (Exception e) {
            log.error("TDengine insert failed, rollback MySQL", e);
            smDeviceMapper.deleteById(id);
        }
    }

    @Async("applicationTaskExecutor")
    public void deleteAsync(String deviceSn) {
        try {
            deviceTDMapper.deleteByDeviceSn("device_" + deviceSn);
        } catch (Exception e) {
            log.error("TDengine insert failed, rollback MySQL", e);
        }
    }
}
