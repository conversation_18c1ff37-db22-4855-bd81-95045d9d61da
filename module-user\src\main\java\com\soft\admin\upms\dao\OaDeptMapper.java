package com.soft.admin.upms.dao;

import com.soft.admin.upms.model.OaDept;
import com.soft.common.core.base.dao.BaseDaoMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * OA系统部门关系Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
public interface OaDeptMapper extends BaseDaoMapper<OaDept> {


    /**
     * 根据oaDeptIds查询
     *
     * @param oaDeptIds
     * @return
     */
    List<OaDept> selectByOaDeptIds(@Param("oaDeptIds") List<String> oaDeptIds);

    void batchInsert(@Param("list") List<OaDept> list);

    /**
     * 查询oaDeptId列表
     *
     * @param oaType
     */
    List<String> selectAllOaDeptIdList(@Param("oaType") String oaType);

    List<OaDept> selectAll();
}
