package com.soft.common.core.util;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * @Description: MD5加密工具
 * @Author: gaoyh
 * @Date: 2023/8/10 13:58
 */
public class MD5Utils {
    public static String stringToMD5(String plainText) {
        byte[] mdBytes = null;
        try {
            mdBytes = MessageDigest.getInstance("MD5").digest(
                    plainText.getBytes());
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5算法不存在！");
        }
        String mdCode = new BigInteger(1, mdBytes).toString(16);

        if(mdCode.length() < 32) {
            int a = 32 - mdCode.length();
            for (int i = 0; i < a; i++) {
                mdCode = "0"+mdCode;
            }
        }
       return mdCode;
    }

    public static void main(String[] args) {
        System.out.println(stringToMD5("1027d408-cb99-11e8-aaa2-00163e04694963e97916ce654894bea02e6d690373231691646526"));
    }

}
