<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.SysUserRoleMapper">
    <resultMap id="BaseResultMap" type="com.soft.admin.upms.model.SysUserRole">
        <id column="user_id" jdbcType="BIGINT" property="userId"/>
        <id column="role_id" jdbcType="BIGINT" property="roleId"/>
    </resultMap>
    <insert id="batchInsert">
        insert into
            common_sys_user_role(user_id, role_id)
        values
            <foreach collection="list" item="item" separator=",">
                (
                 #{item.userId},
                 #{item.roleId}
                )
            </foreach>
    </insert>
</mapper>
