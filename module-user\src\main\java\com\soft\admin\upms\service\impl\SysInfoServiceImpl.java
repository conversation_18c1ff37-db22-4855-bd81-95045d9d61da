package com.soft.admin.upms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.admin.upms.dao.SysInfoMapper;
import com.soft.admin.upms.dto.SysInfoDTO;
import com.soft.admin.upms.model.SysInfo;
import com.soft.admin.upms.service.SysInfoService;
import com.soft.admin.upms.vo.SysInfoVO;
import com.soft.common.core.util.MyModelUtil;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName SysInfoServiceImpl
 * @description:
 * @date 2025年05月19日
 */
@Service
public class SysInfoServiceImpl extends ServiceImpl<SysInfoMapper, SysInfo> implements SysInfoService {

    @Override
    public List<SysInfoVO> getSysInfoList() {
        List<SysInfo> list = this.list();
        return MyModelUtil.copyCollectionTo(list, SysInfoVO.class);
    }

    @Override
    public void update(SysInfoDTO updateDTO) {
        SysInfo sysInfo = MyModelUtil.copyTo(updateDTO, SysInfo.class);
        this.updateById(sysInfo);
    }
}
