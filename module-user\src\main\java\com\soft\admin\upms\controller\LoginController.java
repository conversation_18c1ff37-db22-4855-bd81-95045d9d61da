package com.soft.admin.upms.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.soft.admin.upms.config.UserApplicationConfig;
import com.soft.admin.upms.dto.OaNoPasswordLoginDTO;
import com.soft.admin.upms.model.SysUser;
import com.soft.admin.upms.service.*;
import com.soft.common.core.annotation.DisableDataFilter;
import com.soft.common.core.annotation.MyRequestBody;
import com.soft.common.core.annotation.NoAuthInterface;
import com.soft.common.core.object.ResponseResult;
import com.soft.common.core.object.TokenData;
import com.soft.common.core.upload.BaseUpDownloader;
import com.soft.common.core.upload.UpDownloaderFactory;
import com.soft.common.core.upload.UploadStoreInfo;
import com.soft.common.core.util.MyModelUtil;
import com.soft.common.core.util.RedisKeyUtil;
import com.soft.common.log.annotation.OperationLog;
import com.soft.common.log.model.constant.SysOperationLogType;
import com.soft.common.redis.cache.SessionCacheHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 登录接口控制器类。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
@ApiSupport(order = 1)
@Api(tags = "用户登录接口")
@Slf4j
@RestController
@RequestMapping("/admin/upms/login")
public class LoginController {
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysMenuService sysMenuService;
    @Autowired
    private SysPermService sysPermService;
    @Autowired
    private SysDataPermService sysDataPermService;
    @Autowired
    private UserApplicationConfig appConfig;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private SessionCacheHelper cacheHelper;
    @Autowired
    private UpDownloaderFactory upDownloaderFactory;
    @Resource
    private SysPermissionService sysPermissionService;


    /**
     * 登录接口。
     *
     * @param loginName 登录名。
     * @param password  密码。
     * @return JWT的Token
     */
    @ApiImplicitParams({
            // 这里包含密码密文，仅用于方便开发期间的接口测试，集成测试和发布阶段，需要将当前注解去掉。
            // 如果您重新生成了公钥和私钥，请替换password的缺省值。
            @ApiImplicitParam(name = "loginName", defaultValue = "admin"), @ApiImplicitParam(name = "password", defaultValue = "IP3ccke3GhH45iGHB5qP9p7iZw6xUyj28Ju10rnBiPKOI35sc%2BjI7%2FdsjOkHWMfUwGYGfz8ik31HC2Ruk%2Fhkd9f6RPULTHj7VpFdNdde2P9M4mQQnFBAiPM7VT9iW3RyCtPlJexQ3nAiA09OqG%2F0sIf1kcyveSrulxembARDbDo%3D"),})
    @NoAuthInterface
    @DisableDataFilter
    @OperationLog(type = SysOperationLogType.LOGIN, saveResponse = false)
    @PostMapping("/doLogin")
    public ResponseResult<JSONObject> doLogin(@MyRequestBody String loginName, @MyRequestBody String password) throws Exception {
        return sysUserService.doLogin(loginName, password);
    }

    /**
     * OA系统免密登录
     *
     * @param loginParam
     * @return
     */
    @NoAuthInterface
    @DisableDataFilter
    @OperationLog(type = SysOperationLogType.LOGIN, saveResponse = false)
    @PostMapping("/noPassword/login")
    public ResponseResult<String> noPasswordLogin(@RequestBody OaNoPasswordLoginDTO loginParam) {
        return sysUserService.noPasswordLogin(loginParam);
    }

    /**
     * 登出操作。同时将Session相关的信息从缓存中删除。
     *
     * @return 应答结果对象。
     */
    @OperationLog(type = SysOperationLogType.LOGOUT)
    @PostMapping("/doLogout")
    public ResponseResult<Void> doLogout() {
        String sessionId = TokenData.takeFromRequest().getSessionId();
        String sessionIdKey = RedisKeyUtil.makeSessionIdKey(sessionId);
        redissonClient.getBucket(sessionIdKey).delete();
        redissonClient.getBucket(RedisKeyUtil.makeSessionWhiteListPermKey(sessionId)).deleteAsync();
        String menuPermPattern = RedisKeyUtil.getSessionMenuPermPrefix(sessionId) + "*";
        redissonClient.getKeys().deleteByPatternAsync(menuPermPattern);
        sysDataPermService.removeDataPermCache(sessionId);
        sysPermService.removeUserSysPermCache(sessionId);
        sysPermService.removePermGroupCache(sessionId);
        cacheHelper.removeAllSessionCache(sessionId);
        return ResponseResult.success();
    }

    /**
     * 在登录之后，通过token再次获取登录信息。
     * 用于在当前浏览器登录系统后，在新tab页中可以免密登录。
     *
     * @return 应答结果对象，其中包括JWT的Token数据，以及菜单列表。
     */
    @GetMapping("/getLoginInfo")
    public ResponseResult<JSONObject> getLoginInfo() {
        TokenData tokenData = TokenData.takeFromRequest();
        // 这里解释一下为什么没有缓存menuList和permCodeList。
        // 1. 该操作和权限验证不同，属于低频操作。
        // 2. 第一次登录和再次获取登录信息之间，如果修改了用户的权限，那么本次获取的是最新权限。
        // 3. 上一个问题无法避免，因为即便缓存也是有过期时间的，过期之后还是要从数据库获取的。
        JSONObject jsonData = new JSONObject();
        jsonData.put("userId", tokenData.getUserId());
        jsonData.put("showName", tokenData.getShowName());
        jsonData.put("phone", tokenData.getPhone());
        jsonData.put("cardNo", tokenData.getCardNo());
        jsonData.put("sex", tokenData.getSex());
        jsonData.put("isAdmin", tokenData.getIsAdmin());
        if (StrUtil.isNotBlank(tokenData.getHeadImageUrl())) {
            jsonData.put("headImageUrl", tokenData.getHeadImageUrl());
        }
        jsonData.put("facePicture", tokenData.getFacePicture());
        jsonData.put("roleIds", StringUtils.split(tokenData.getRoleIds()));
        jsonData.put("perms", sysPermissionService.getMenuPermission(tokenData));
        jsonData.put("orgId", tokenData.getOrgId());
        jsonData.put("deptId", tokenData.getDeptId());
        jsonData.put("deptName", tokenData.getDeptName());
        jsonData.put("postIds", tokenData.getPostIds());
        jsonData.put("posts", tokenData.getPosts());
        jsonData.put("jobNumber", tokenData.getJobNumber());
        jsonData.put("shortPhone", tokenData.getShortPhone());
        jsonData.put("departments", tokenData.getDepartments());
        return ResponseResult.success(jsonData);
    }

    /**
     * 用户修改自己的密码。
     *
     * @param oldPass 原有密码。
     * @param newPass 新密码。
     * @return 应答结果对象。
     */
    @PostMapping("/changePassword")
    public ResponseResult<Void> changePassword(@MyRequestBody String oldPass, @MyRequestBody String newPass) throws Exception {
        return sysUserService.updatePassword(oldPass, newPass);
    }

    /**
     * 上传并修改用户头像。
     *
     * @param filePath 上传的头像文件。
     */
    @PostMapping("/changeHeadImage")
    public ResponseResult<Void> changeHeadImage(String filePath) {
        sysUserService.changeHeadImage(TokenData.takeFromRequest().getUserId(), filePath);
        return ResponseResult.success();
    }

    /**
     * 修改短号
     *
     * @param
     */
    @GetMapping("/changeShortPhone")
    public ResponseResult<Void> changeShortPhone(String shortPhone) throws Exception {
        return sysUserService.changeShortPhone(shortPhone);
    }


    /**
     * 修改姓名
     *
     * @param
     */
    @GetMapping("/changeName")
    public ResponseResult<Void> changeName(String showName) throws Exception {
        return sysUserService.changeShowName(showName);
    }


    /**
     * 下载用户头像。
     *
     * @param filename 文件名。如果没有提供该参数，就从当前记录的指定字段中读取。
     * @param response Http 应答对象。
     */
    @GetMapping("/downloadHeadImage")
    public void downloadHeadImage(String filename, HttpServletResponse response) {
        try {
            SysUser user = sysUserService.getById(TokenData.takeFromRequest().getUserId());
            if (user == null) {
                ResponseResult.output(HttpServletResponse.SC_NOT_FOUND);
                return;
            }
            if (StrUtil.isBlank(user.getHeadImageUrl())) {
                ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST);
                return;
            }
            if (!BaseUpDownloader.containFile(user.getHeadImageUrl(), filename)) {
                ResponseResult.output(HttpServletResponse.SC_FORBIDDEN);
                return;
            }
            String fieldName = "headImageUrl";
            UploadStoreInfo storeInfo = MyModelUtil.getUploadStoreInfo(SysUser.class, fieldName);
            BaseUpDownloader upDownloader = upDownloaderFactory.get(storeInfo.getStoreType());
            upDownloader.doDownload(appConfig.getUploadFileBaseDir(), SysUser.class.getSimpleName(), fieldName, filename, true, response);
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 查询用户菜单
     *
     * @param
     * @return
     */
    @GetMapping("/getRouter")
    public ResponseResult<List<Tree<String>>> getRouter(@RequestParam(value = "menuId", required = false) String menuId) {
        List<Tree<String>> menuTree = sysMenuService.getMenuTree(TokenData.takeFromRequest(), Boolean.TRUE);
        if (CollectionUtil.isEmpty(menuTree)) {
            return ResponseResult.success(new ArrayList<>());
        }

        if (StringUtils.isNotBlank(menuId)) {
            Optional<Tree<String>> optionalTree = menuTree.stream().filter(t -> Objects.equals(t.getId(), menuId)).findFirst();
            if (optionalTree.isPresent()) {
                Tree<String> tree = optionalTree.get();
                return ResponseResult.success(tree.getChildren());
            }
//            return ResponseResult.success(findMenuId(menuTree, menuId));
        } else {
            return ResponseResult.success(new ArrayList<>(menuTree));
        }
        return ResponseResult.success(new ArrayList<>());
    }

    private List<Tree<String>> findMenuId(List<Tree<String>> menuTrees, String menuId) {
        for (Tree<String> menuTree : menuTrees) {
            if (Objects.equals(menuTree.getId(), menuId)) {
                return menuTree.getChildren();
            } else {
                return findMenuId(menuTree.getChildren(), menuId);
            }
        }
        return new ArrayList<>();
    }

//    /**
//     * 上传人脸照。
//     */
//    @ApiOperation(value = "上传人脸照")
//    @PostMapping("/uploadFacePicture")
//    public ResponseResult<Void> uploadFacePicture(@RequestBody UploadFacePictureDTO params) {
//        params.setUserId(TokenData.takeFromRequest().getUserId());
//        sysUserService.uploadFacePicture(params);
//        return ResponseResult.success();
//    }
}
