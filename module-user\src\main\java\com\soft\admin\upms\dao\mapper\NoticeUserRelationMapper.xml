<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.NoticeUserRelationMapper">
    <resultMap type="com.soft.admin.upms.model.NoticeUserRelation" id="NoticeUserRelationResult">
        <result property="userId" column="user_id" />
        <result property="noticeId" column="notice_id" />
    </resultMap>

    <sql id="selectNoticeUserRelationVo">
        select user_id, notice_id from sp_notice_user_relation
    </sql>

    <insert id="batchInsert" parameterType="com.soft.admin.upms.model.NoticeUserRelation">
        insert into sp_notice_user_relation(user_id, notice_id)
        values
        <foreach collection="list" item="model" separator=",">
            (#{model.userId}, #{model.noticeId})
        </foreach>
    </insert>
    
</mapper>