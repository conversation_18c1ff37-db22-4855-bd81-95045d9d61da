package com.soft.admin.upms.enums;

import com.soft.common.core.util.MimeTypeUtils;
import com.soft.common.core.util.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 通行人员类型
 * @date 2023-07-11  09:41:02
 */
public enum UploadTypeEnums {
    头像("head", "/head", MimeTypeUtils.IMAGE_EXTENSION),
    图片("ec", "/ec", MimeTypeUtils.IMAGE_EXTENSION),
    人脸("face", "/user/face" , MimeTypeUtils.IMAGE_EXTENSION),
    附件("annex", "/annex" , MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION),

    ;

    private final String name;

    /**
     * 路径
     */
    private final String path;

    /**
     * 文件类型
     */
    private final String[] mimeType;

    UploadTypeEnums(String name, String path,String[] mimeType) {
        this.name = name;
        this.path = path;
        this.mimeType = mimeType;
    }

    public String getName() {
        return name;
    }

    public String getPath() {
        return path;
    }

    public String[] getMimeType() {
        return mimeType;
    }


    public static UploadTypeEnums get(String name) {
        if(StringUtils.isBlank(name)){
            return 图片;
        }
        for (UploadTypeEnums typeEnums : values()) {
            if (Objects.equals(typeEnums.getName(), name) ) {
                return typeEnums;
            }
        }
        return 图片;
    }
}
