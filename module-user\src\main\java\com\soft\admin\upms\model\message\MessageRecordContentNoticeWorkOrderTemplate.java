package com.soft.admin.upms.model.message;

import cn.hutool.core.date.DateUtil;
import com.soft.admin.upms.api.dingtalk.enums.DingTalkMessageType;
import com.soft.common.core.util.StringUtils;
import com.soft.admin.upms.enums.MessageRecordBusiTypeEnums;
import com.soft.admin.upms.enums.MessageRecordTypeEnums;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class MessageRecordContentNoticeWorkOrderTemplate extends MessageRecordContentNoticeTemplate {

    private static final MessageRecordBusiTypeEnums BUSI_TYPE = MessageRecordBusiTypeEnums.WORK_ORDER;

    /**
     * 报单
     *
     * @param orderType      工单类别：维修工单、临时保洁、临时运送
     * @param orderCode      工单编号
     * @param busiId         业务 id
     * @param level          优先级
     * @param sendUserId     发送人
     * @param receiveUserIds 接收人
     * @return
     */
    public MessageRecordContent pushOrderOfReport(String space, String describe, String time, String orderType, String orderCode, Long busiId, String level, Long sendUserId, Long... receiveUserIds) {
        String url = "/operation/workManage/maintainDetail?id=${id}&type=1";
        if ("临时运送工单".equals(orderType)) {
            url = "/operation/workManage/transportDetail?id=${id}&type=1";
        } else if ("公寓保洁工单".equals(orderType)) {
            url = "/operation/workManage/inspectionDetail?id=${id}&type=1";
        }
        // 代办描述
        String content;
        if (StringUtils.isNotEmpty(space)) {
            String[] parts = space.split("/", 2);
            space = parts[parts.length - 1].replace("/", "-");
        }

        switch (orderType) {
            case "维修工单":
                // #位置（楼栋-楼层-点位）#问题描述#
                content = space.concat(describe);
                break;
            case "临时保洁工单":
                // #位置（楼栋-楼层-点位）#保洁内容#
                content = space.concat(describe);
                break;
            case "临时运送工单":
                // #运送时间（月日时分）#起点#起点位置（楼栋-楼层-点位）#运送#运送场景#
                content = time.concat("起点").concat(space).concat(describe);
                break;
            default:
                content = "您有一条新的" + orderType + orderCode + "，请及时处理！";
        }

        messageRecordContent.setTitle("工单待处理");
        messageRecordContent.setBusiId(busiId);
        messageRecordContent.setLevel(level);
//        messageRecordContent.setContent("您有一条新的" + orderType + orderCode + "，请及时处理！");
        messageRecordContent.setContent(content);
        messageRecordContent.setSendUserId(sendUserId);
        messageRecordContent.setReceiveUserIds(Stream.of(receiveUserIds).collect(Collectors.toList()));
        messageRecordContent.setHyperlink(url.replace("${id}", busiId.toString()));
        messageRecordContent.setType(MessageRecordTypeEnums.TODO);
        messageRecordContent.setBusiType(BUSI_TYPE);
        pushJiGuangMessage(Boolean.TRUE);
        push();
        callback(messageRecordContent);
        return messageRecordContent;
    }

    /**
     * 工单已完成
     *
     * @param orderType      工单类别：维修工单、临时保洁、临时运送
     * @param orderCode      工单编号
     * @param busiId         业务 id
     * @param level          优先级
     * @param sendUserId     发送人
     * @param receiveUserIds 接收人
     * @return
     */
    public MessageRecordContent pushComplete(String orderType, String orderCode, Long busiId, String level, Long sendUserId, Long... receiveUserIds) {
        String url = "/operation/workManage/maintainDetail?id=${id}&type=2";
        if ("临时运送工单".equals(orderType)) {
            url = "/operation/workManage/transportDetail?id=${id}&type=2";
        }
        messageRecordContent.setTitle("工单待评价");
        messageRecordContent.setBusiId(busiId);
        messageRecordContent.setLevel(level);
        messageRecordContent.setContent("您上报的" + orderType + orderCode + "已完成，请及时评价！");
        messageRecordContent.setSendUserId(sendUserId);
        messageRecordContent.setReceiveUserIds(Stream.of(receiveUserIds).collect(Collectors.toList()));
        messageRecordContent.setHyperlink(url.replace("${id}", busiId.toString()));
        messageRecordContent.setType(MessageRecordTypeEnums.TODO);
        messageRecordContent.setBusiType(BUSI_TYPE);
        pushJiGuangMessage(Boolean.FALSE);
        push();
        callback(messageRecordContent);
        return messageRecordContent;
    }

    /**
     * 工单已驳回
     *
     * @param userName       驳回人
     * @param orderCode      工单编号
     * @param busiId         业务id
     * @param level          优先级
     * @param sendUserId     发送人
     * @param receiveUserIds 接收人
     * @return
     */
    public MessageRecordContent pushReject(String userName, String orderCode, Long busiId, String level, Long sendUserId, List<Long> receiveUserIds) {
        messageRecordContent.setTitle("工单驳回");
        messageRecordContent.setBusiId(busiId);
        messageRecordContent.setLevel(level);
        messageRecordContent.setContent(userName + "驳回了维修工单" + orderCode + "，请及时处理！");
        messageRecordContent.setSendUserId(sendUserId);
        messageRecordContent.setReceiveUserIds(receiveUserIds);
        messageRecordContent.setHyperlink("/operation/workManage/maintainDetail?id=${id}&type=0".replace("${id}", busiId.toString()));
        messageRecordContent.setType(MessageRecordTypeEnums.TODO);
        messageRecordContent.setBusiType(BUSI_TYPE);
        pushJiGuangMessage(Boolean.FALSE);
        push();
        callback(messageRecordContent);
        return messageRecordContent;
    }


    /**
     * 工单已评价
     *
     * @param orderType      工单类别：维修工单、临时保洁、临时运送
     * @param orderCode      工单编号
     * @param score          评分
     * @param busiId         业务id
     * @param level          优先级
     * @param sendUserId     发送人
     * @param receiveUserIds 接收人
     * @return
     */
    public MessageRecordContent pushReview(String orderType, String orderCode, int score, Long busiId, String level, Long sendUserId, Long... receiveUserIds) {
        messageRecordContent.setTitle("工单已评价");
        messageRecordContent.setBusiId(busiId);
        messageRecordContent.setLevel(level);
        messageRecordContent.setContent("由您处理的" + orderType + orderCode + "获得了" + score + "星评价，辛苦了，请再接再厉！");
        messageRecordContent.setSendUserId(sendUserId);
        messageRecordContent.setReceiveUserIds(Stream.of(receiveUserIds).collect(Collectors.toList()));
        messageRecordContent.setHyperlink("");
        messageRecordContent.setType(TYPE);
        messageRecordContent.setBusiType(BUSI_TYPE);
        messageRecordContent.setDingTalkMessageType(DingTalkMessageType.MARKDOWN);
        pushJiGuangMessage(Boolean.FALSE);
        push();
//        callback(messageRecordContent);
        workNotice(messageRecordContent);
        return messageRecordContent;
    }


    /**
     * 报价
     *
     * @param orderCode      工单编号
     * @param busiId         业务id
     * @param level          优先级
     * @param sendUserId     发送人
     * @param receiveUserIds 接收人
     * @return
     */
    public MessageRecordContent pushQuote(String orderCode, Long busiId, String level, Long sendUserId, List<Long> receiveUserIds) {
        messageRecordContent.setTitle("工单审计");
        messageRecordContent.setBusiId(busiId);
        messageRecordContent.setLevel(level);
        messageRecordContent.setContent("您有一条新的维修工单" + orderCode + "待审批" + "，请及时处理！");
        messageRecordContent.setSendUserId(sendUserId);
        messageRecordContent.setReceiveUserIds(receiveUserIds);
        messageRecordContent.setHyperlink("/operation/audit/auditDetail?orderId=${id}".replace("${id}", busiId.toString()));
        messageRecordContent.setType(MessageRecordTypeEnums.TODO);
        messageRecordContent.setBusiType(BUSI_TYPE);
        pushJiGuangMessage(Boolean.FALSE);
        push();
        callback(messageRecordContent);
        return messageRecordContent;
    }


    /**
     * 退回
     *
     * @param workUserName   执行人
     * @param orderCode      工单编号
     * @param busiId         业务id
     * @param level          优先级
     * @param sendUserId     发送人
     * @param receiveUserIds 接收人
     * @return
     */
    public MessageRecordContent pushBack(String workUserName, String orderCode, Long busiId, String level, Long sendUserId, List<Long> receiveUserIds) {
        messageRecordContent.setTitle("工单退回");
        messageRecordContent.setBusiId(busiId);
        messageRecordContent.setLevel(level);
        messageRecordContent.setContent(workUserName + "退回了维修工单" + orderCode + "，请及时派单！");
        messageRecordContent.setSendUserId(sendUserId);
        messageRecordContent.setReceiveUserIds(receiveUserIds);
        messageRecordContent.setHyperlink("/operation/workManage/maintainDetail?id=${id}&type=0".replace("${id}", busiId.toString()));
        messageRecordContent.setType(MessageRecordTypeEnums.TODO);
        messageRecordContent.setBusiType(BUSI_TYPE);
        pushJiGuangMessage(Boolean.FALSE);
        push();
        callback(messageRecordContent);
        return messageRecordContent;
    }


    /**
     * 由计划生成的工单
     *
     * @param orderType      工单类别
     * @param orderCode      工单编号
     * @param checkStartTime 检查开始时间
     * @param busiId         业务id
     * @param level          优先级
     * @param sendUserId     发送人
     * @param receiveUserIds 接收人
     * @return
     */
    public MessageRecordContent pushOrderOfPlan(String space, String describe, String time, String orderType, String orderCode, Date checkStartTime, Long busiId, String level, Long sendUserId, Long... receiveUserIds) {
        messageRecordContent.setContent("您有一条新的" + orderType + orderCode + "于" + DateUtil.format(checkStartTime, "yyyy年MM月dd日 HH:mm") + "开始，请及时处理！");
        String url = "/operation/workManage/inspectionDetail?id=${id}&type=1";
        if ("运送工单".equals(orderType)) {
            url = "/operation/workManage/transportDetail?id=${id}&type=1";
            if (StringUtils.isNotEmpty(space)) {
                String[] split = space.split("#");
                space = split[split.length - 1].replace("/", "-");
            }
            messageRecordContent.setContent("您有一条新的运送工单".concat(describe).concat("于").concat(time).concat("开始,").concat("起点").concat(space).concat(",请及时处理！"));
        }
        messageRecordContent.setTitle("工单待处理");
        messageRecordContent.setBusiId(busiId);
        messageRecordContent.setLevel(level);
        messageRecordContent.setSendUserId(sendUserId);
        messageRecordContent.setReceiveUserIds(Stream.of(receiveUserIds).collect(Collectors.toList()));
        messageRecordContent.setHyperlink(url.replace("${id}", busiId.toString()));
        messageRecordContent.setType(MessageRecordTypeEnums.TODO);
        messageRecordContent.setBusiType(BUSI_TYPE);
        pushJiGuangMessage(Boolean.FALSE);
        push();
        callback(messageRecordContent);
        return messageRecordContent;
    }

}
