package com.soft.admin.upms.dao;

import com.soft.admin.upms.model.SysDept;
import com.soft.common.core.base.dao.BaseDaoMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 部门管理数据操作访问接口。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
public interface SysDeptMapper extends BaseDaoMapper<SysDept> {

    /**
     * 批量插入对象列表。
     *
     * @param sysDeptList 新增对象列表。
     */
    void insertList(List<SysDept> sysDeptList);

    /**
     * 获取过滤后的对象列表。
     *
     * @param sysDeptFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<SysDept> getSysDeptList(
            @Param("sysDeptFilter") SysDept sysDeptFilter, @Param("orderBy") String orderBy);

    List<SysDept> getSysDeptApiList(
            @Param("sysDeptFilter") SysDept sysDeptFilter, @Param("orderBy") String orderBy);

    void batchUpdate(@Param("list") List<SysDept> list);


    List<SysDept> getDeptPath (@Param("list")Set<String> list);

    /**
     * 根据用户Id获取部门信息
     * @param userId
     * @param deptCode
     * @return
     */
    SysDept getSecurityByUserId(@Param("userId") Long userId,@Param("deptCode") String deptCode);

    /**
     * 获取所有保安信息
     * @param deptCode
     * @return
     */
    List<SysDept> getSecurityAll(@Param("deptCode") String deptCode);

}
