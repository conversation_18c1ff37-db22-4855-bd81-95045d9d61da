package com.soft.common.monitor.hk;

import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.IntByReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;

import static com.soft.common.monitor.hk.HCNetSDK.NET_DVR_GET_PDC_RESULT;
import static com.soft.common.monitor.hk.HCNetSDK.NET_SDK_GET_NEXT_STATUS_FAILED;

@Slf4j
@Component
public class HikUtils {

    /**
     * 接口的实例，通过接口实例调用外部dll/so的函数
     */
    public HCNetSDK hCNetSDK;

    /**
     * 用户登录返回句柄(操作唯一id)
     */
    static int lUserID;


    /**
     * 设备登录
     *
     * @param m_sDeviceIP 设备ip地址
     * @param wPort       端口号，设备网络SDK登录默认端口8000
     * @param m_sUsername 用户名
     * @param m_sPassword 密码
     */
    public Integer Login_V40(String m_sDeviceIP, short wPort, String m_sUsername, String m_sPassword) {
        /* 注册 */
        // 设备登录信息
        HCNetSDK.NET_DVR_USER_LOGIN_INFO m_strLoginInfo = new HCNetSDK.NET_DVR_USER_LOGIN_INFO();
        // 设备信息
        HCNetSDK.NET_DVR_DEVICEINFO_V40 m_strDeviceInfo = new HCNetSDK.NET_DVR_DEVICEINFO_V40();

        // 设备 ip
        m_strLoginInfo.sDeviceAddress = new byte[HCNetSDK.NET_DVR_DEV_ADDRESS_MAX_LEN];
        System.arraycopy(m_sDeviceIP.getBytes(), 0, m_strLoginInfo.sDeviceAddress, 0, m_sDeviceIP.length());
        // 设备端口号
        m_strLoginInfo.wPort = wPort;
        // 设备用户名
        m_strLoginInfo.sUserName = new byte[HCNetSDK.NET_DVR_LOGIN_USERNAME_MAX_LEN];
        System.arraycopy(m_sUsername.getBytes(), 0, m_strLoginInfo.sUserName, 0, m_sUsername.length());
        // 设备密码
        m_strLoginInfo.sPassword = new byte[HCNetSDK.NET_DVR_LOGIN_PASSWD_MAX_LEN];
        System.arraycopy(m_sPassword.getBytes(), 0, m_strLoginInfo.sPassword, 0, m_sPassword.length());
        // 是否异步登录：false- 否，true- 是
        m_strLoginInfo.bUseAsynLogin = false;
        // 登录方式：ISAPI登录
        m_strLoginInfo.byLoginMode = 0;
        // write()调用后数据才写入到内存中
        m_strLoginInfo.write();

        lUserID = hCNetSDK.NET_DVR_Login_V40(m_strLoginInfo, m_strDeviceInfo);
        if (lUserID == -1) {
            // 打印错误信息
            int errorCode = hCNetSDK.NET_DVR_GetLastError();
            IntByReference errorInt = new IntByReference(errorCode);
            log.error("[HK] login fail errorCode:{}, errMsg:{}", errorCode, hCNetSDK.NET_DVR_GetErrorMsg(errorInt));
            return lUserID;
        } else {
            // read()后，结构体中才有对应的数据
            m_strDeviceInfo.read();
            return lUserID;
        }
    }

    public void logout(int lUserID) {
        if (lUserID > -1) {
            if (hCNetSDK.NET_DVR_Logout(lUserID)) {
                log.info("注销成功");
            }
        }
    }

    public void destroySDK() {
        hCNetSDK.NET_DVR_Cleanup();
    }

    public String getPic(Integer loginUserId, String finalPath) {
        //图片质量
        HCNetSDK.NET_DVR_JPEGPARA jpeg = new HCNetSDK.NET_DVR_JPEGPARA();
        //设置图片分辨率
        jpeg.wPicSize = 0;
        //设置图片质量
        jpeg.wPicQuality = 0;
        boolean is = hCNetSDK.NET_DVR_CaptureJPEGPicture(loginUserId, 01, jpeg, finalPath.getBytes());
        if (!is) {
            log.info("抓图失败,错误码:{}", hCNetSDK.NET_DVR_GetLastError());
            return null;
        }
        return null;
    }

    /**
     * 查询客流统计信息
     *
     * @param ip
     * @param port
     * @param username
     * @param password
     * @param startTime
     * @param endTime
     * @param reportType
     * @throws InterruptedException
     */
    public HikPassengerFlow getPassengerFlowStatistics(String ip, short port, String username, String password, LocalDateTime startTime, LocalDateTime endTime, byte reportType) throws InterruptedException {
        HikPassengerFlow hikPassengerFlow = new HikPassengerFlow();

        // 初始化设备
        initSDK();

        // 注册设备
        Integer lUserID = Login_V40(ip, port, username, password);
        if (lUserID == null) {
            throw new RuntimeException("hik vision device sdk login fail！");
        }

        HCNetSDK.NET_DVR_PDC_QUERY_COND netDvrPdcQueryCond = new HCNetSDK.NET_DVR_PDC_QUERY_COND();
        netDvrPdcQueryCond.dwSize = netDvrPdcQueryCond.size();
        netDvrPdcQueryCond.dwChannel = 1;
        //查询开始时间
        netDvrPdcQueryCond.struStartTime.wYear = (short) startTime.getYear();
        netDvrPdcQueryCond.struStartTime.byMonth = (byte) startTime.getMonthValue();
        netDvrPdcQueryCond.struStartTime.byDay = (byte) startTime.getDayOfMonth();
        netDvrPdcQueryCond.struStartTime.byHour = (byte) startTime.getHour();
        netDvrPdcQueryCond.struStartTime.byMinute = (byte) startTime.getMinute();
        netDvrPdcQueryCond.struStartTime.bySecond = (byte) startTime.getSecond();

        //查询结束时间
        netDvrPdcQueryCond.struEndTime.wYear = (short) endTime.getYear();
        netDvrPdcQueryCond.struEndTime.byMonth = (byte) endTime.getMonthValue();
        netDvrPdcQueryCond.struEndTime.byDay = (byte) endTime.getDayOfMonth();
        netDvrPdcQueryCond.struEndTime.byHour = (byte) endTime.getHour();
        netDvrPdcQueryCond.struEndTime.byMinute = (byte) endTime.getMinute();
        netDvrPdcQueryCond.struEndTime.bySecond = (byte) endTime.getSecond();

        netDvrPdcQueryCond.byReportType = reportType; //查询类型：0- 无效值，1- 日报表，2- 周报表，3- 月报表，4- 年报表
        log.info("hikvision net dvr start remote config！");
        int m_lHandle = hCNetSDK.NET_DVR_StartRemoteConfig(lUserID, NET_DVR_GET_PDC_RESULT, netDvrPdcQueryCond.getPointer(), 1024, null, null);
        if (m_lHandle >= 0) {
            int iNextRet = 0;
            HCNetSDK.NET_DVR_PDC_RESULT netDvrPdcResult = new HCNetSDK.NET_DVR_PDC_RESULT();
            while (true) {
                log.info("hikvision net dvr get next remote config！");
                iNextRet = hCNetSDK.NET_DVR_GetNextRemoteConfig(m_lHandle, netDvrPdcResult.getPointer(), 1024);
                log.info("hikvision net dvr get next remote config, result is: {}", iNextRet);
                if (iNextRet == HCNetSDK.NET_SDK_GET_NEXT_STATUS_SUCCESS) {    //成功查找到数据
                    log.info("客流统计，进入人数：{}；离开人数：{}；经过人数：{}", netDvrPdcResult.dwEnterNum, netDvrPdcResult.dwLeaveNum, netDvrPdcResult.dwPeoplePassing);
                } else {
                    if (iNextRet == HCNetSDK.NET_SDK_GET_NEXT_STATUS_NEED_WAIT) {   //需等待设备发送数据
                        Thread.sleep(5);
                        continue;
                    }
                    if (iNextRet == HCNetSDK.NET_SDK_NEXT_STATUS__FINISH) { //数据全部查找结束
                        log.info("客流量数据查询结束!\n");
                        break;
                    } else if (iNextRet == NET_SDK_GET_NEXT_STATUS_FAILED) {    //查找出现异常
                        log.error("查找异常!");
                        break;
                    } else {
                        log.warn("未知状态!");
                        break;
                    }
                }
            }
            hikPassengerFlow.setDwEnterNum(netDvrPdcResult.dwEnterNum);
            hikPassengerFlow.setDwLeaveNum(netDvrPdcResult.dwLeaveNum);
            hikPassengerFlow.setDwPeoplePassing(netDvrPdcResult.dwPeoplePassing);
        } else {
            log.error("客流量数据查询失败! 错误号: {}", hCNetSDK.NET_DVR_GetLastError());
        }

        if (m_lHandle >= 0) {
            if (!hCNetSDK.NET_DVR_StopRemoteConfig(m_lHandle)) {
                log.error("停止客流量数据查询失败! 错误号: {}", hCNetSDK.NET_DVR_GetLastError());
            }
        }
        //注销用户
        log.info("hikvision net dvr logout！");
        hCNetSDK.NET_DVR_Logout(lUserID);
        //释放SDK资源，只需要退出时调用一次
        hCNetSDK.NET_DVR_Cleanup();
        return hikPassengerFlow;
    }


    //  =================================== 初始化 HIK SDK =====================================

    @PostConstruct
    public void beforeInit() {
        try{
            if (hCNetSDK == null) {
                boolean b = CreateSDKInstance();
                if (!b) {
                    log.error("SDK对象加载失败");
                }
            }
            // 初始化 配置
            setSDKInitConfig();
        }catch (Exception e){
            log.error("初始化海康SDK失败");
            log.error(e.getMessage(),e);
        }

    }

    /**
     * 动态库加载
     *
     * @return
     */
    private boolean CreateSDKInstance() {
        if (hCNetSDK == null) {
            synchronized (HCNetSDK.class) {
                String strDllPath = "";
                try {
                    //win系统加载库路径
                    if (osSelect.isWindows()) {
                        strDllPath = "lib\\hk\\window\\HCNetSDK.dll";
                        //Linux系统加载库路径
                    } else if (osSelect.isLinux()) {
                        strDllPath = System.getProperty("user.dir") + "/lib/libhcnetsdk.so";
                    }
                     hCNetSDK = (HCNetSDK) Native.loadLibrary(strDllPath, HCNetSDK.class);
                } catch (Exception ex) {
                    log.error("load hik vision Library is error, load path: 【{}】 errorMessage: 【{}】", strDllPath, ex.getMessage());
                    return false;
                }
             }
        }
        return true;
    }

    private void setSDKInitConfig() {
        if (osSelect.isLinux()) {
            /* ------------------------------- 初始化 ----------------------------------- */
            String userDir = System.getProperty("user.dir");
            // 这里是库的绝对路径，请根据实际情况修改，注意改路径必须有访问权限
            // 设置 libcrypto.so所在路径
            String strPath1 = userDir +"/lib/libcrypto.so.1.1";
            HCNetSDK.BYTE_ARRAY ptrByteArray1 = new HCNetSDK.BYTE_ARRAY(256);
            System.arraycopy(strPath1.getBytes(), 0, ptrByteArray1.byValue, 0, strPath1.length());
            ptrByteArray1.write();
            hCNetSDK.NET_DVR_SetSDKInitCfg(HCNetSDK.NET_SDK_INIT_CFG_LIBEAY_PATH, ptrByteArray1.getPointer());

            // 设置libssl.so所在路径
            HCNetSDK.BYTE_ARRAY ptrByteArray2 = new HCNetSDK.BYTE_ARRAY(256);
            String strPath2 = userDir + "/lib/libssl.so.1.1";
            System.arraycopy(strPath2.getBytes(), 0, ptrByteArray2.byValue, 0, strPath2.length());
            ptrByteArray2.write();
            hCNetSDK.NET_DVR_SetSDKInitCfg(HCNetSDK.NET_SDK_INIT_CFG_SSLEAY_PATH, ptrByteArray2.getPointer());

            // 设置HCNetSDKCom组件库所在路径,注意只设置到HCNetSDKCom的上一层，我们SDK会从设置的路径下寻找HCNetSDKCom文件夹的
            String strPathCom = userDir + "/lib/";
            HCNetSDK.NET_DVR_LOCAL_SDK_PATH struComPath = new HCNetSDK.NET_DVR_LOCAL_SDK_PATH();
            System.arraycopy(strPathCom.getBytes(), 0, struComPath.sPath, 0, strPathCom.length());
            struComPath.write();
            hCNetSDK.NET_DVR_SetSDKInitCfg(HCNetSDK.NET_SDK_INIT_CFG_SDK_PATH, struComPath.getPointer());
        }
    }

    /**
     * 初始化 SDK
     */
    public void initSDK() {
        try {
            // 初始化sdk
            boolean isOk = hCNetSDK.NET_DVR_Init();
            // 设置连接时间与重连时间
            hCNetSDK.NET_DVR_SetConnectTime(2000, 1);
            hCNetSDK.NET_DVR_SetReconnect(10000, true);

            // 启动SDK写日志
            hCNetSDK.NET_DVR_SetLogToFile(3, "./sdkLog", false);
            if (!isOk) {
                log.error("=================== InitSDK init fail ===================");
            } else {
                log.info("============== InitSDK init success ====================");
            }
        } catch (Exception e) {
            log.error("InitSDK-error", e);
        }
    }
}
