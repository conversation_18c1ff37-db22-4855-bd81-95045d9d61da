package com.soft.admin.upms.api.dingtalk;

/**
 * @Description 钉钉常量
 * @Date 0009, 2023年8月9日 13:54
 * <AUTHOR>
 **/
public class DingTalkConstants {

    public static final String GET = "GET";
    public static final String POST = "POST";
    public static final int EARLY_REFRESH_SECONDS = 1800;

    public static final String CACHE_KEY_ACCESS_TOKEN = "oa:dingtalk:accessToken:String";

    public static final String API_GET_ACCESS_TOKEN = "/gettoken";
    public static final String API_GET_AUTH_SCOPE = "/auth/scopes";
    public static final String API_GET_DEPT_SUB_LIST = "/topapi/v2/department/listsub";
    public static final String API_GET_DEPT = "/topapi/v2/department/get";
    public static final String API_GET_USER_LIST_BY_DEPT = "/topapi/v2/user/list";
    public static final String API_GET_USER = "/topapi/v2/user/get";
    public static final String API_GET_USER_BY_CODE = "/topapi/v2/user/getuserinfo";
    public static final String API_UPLOAD_PUNCH_RECORDS = "/topapi/attendance/record/upload";
}
