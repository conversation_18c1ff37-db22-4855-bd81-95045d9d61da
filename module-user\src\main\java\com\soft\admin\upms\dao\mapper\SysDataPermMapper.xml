<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.SysDataPermMapper">
    <resultMap id="BaseResultMap" type="com.soft.admin.upms.model.SysDataPerm">
        <id column="data_perm_id" jdbcType="BIGINT" property="dataPermId"/>
        <result column="data_perm_name" jdbcType="VARCHAR" property="dataPermName"/>
        <result column="rule_type" jdbcType="INTEGER" property="ruleType"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <resultMap id="BaseResultMapEx" type="com.soft.admin.upms.model.SysDataPerm" extends="BaseResultMap">
        <collection property="dataPermDeptList" column="data_perm_id" javaType="ArrayList"
                    ofType="com.soft.admin.upms.model.SysDataPermDept" notNullColumn="dept_id"
                    resultMap="com.soft.admin.upms.dao.SysDataPermDeptMapper.BaseResultMap">
        </collection>
        <collection property="dataPermMenuList" column="data_perm_id" javaType="ArrayList"
                    ofType="com.soft.admin.upms.model.SysDataPermMenu" notNullColumn="menu_id"
                    resultMap="com.soft.admin.upms.dao.SysDataPermMenuMapper.BaseResultMap">
        </collection>
    </resultMap>

    <sql id="filterRef">
        <if test="sysDataPermFilter != null">
            <if test="sysDataPermFilter.ruleType != null">
                AND common_sys_data_perm.rule_type = #{sysDataPermFilter.ruleType}
            </if>
            <if test="sysDataPermFilter.searchString != null and sysDataPermFilter.searchString != ''">
                <bind name= "safeSearchString" value= "'%' + sysDataPermFilter.searchString + '%'" />
                AND IFNULL(common_sys_data_perm.data_perm_name, '') LIKE #{safeSearchString}
            </if>
        </if>
    </sql>

    <select id="getSysDataPermList" resultMap="BaseResultMap" parameterType="com.soft.admin.upms.model.SysDataPerm">
        SELECT
            common_sys_data_perm.*
        FROM
            common_sys_data_perm
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSysDataPermListByUserId" resultMap="BaseResultMapEx" parameterType="com.soft.admin.upms.model.SysDataPerm">
        SELECT
            common_sys_data_perm.*,
            common_sys_data_perm_dept.*,
            common_sys_data_perm_menu.*
        FROM
            common_sys_data_perm_user
        INNER JOIN
            common_sys_data_perm ON common_sys_data_perm_user.data_perm_id = common_sys_data_perm.data_perm_id
        LEFT JOIN
            common_sys_data_perm_dept ON common_sys_data_perm.data_perm_id = common_sys_data_perm_dept.data_perm_id
        LEFT JOIN
            common_sys_data_perm_menu ON common_sys_data_perm.data_perm_id = common_sys_data_perm_menu.data_perm_id
        <where>
            AND common_sys_data_perm_user.user_id = #{userId}
        </where>
    </select>

    <select id="getSysDataPermListByMenuId" resultMap="BaseResultMap" parameterType="com.soft.admin.upms.model.SysDataPerm">
        SELECT
            common_sys_data_perm.*
        FROM
            common_sys_data_perm,
            common_sys_data_perm_menu
        <where>
            common_sys_data_perm.data_perm_id = common_sys_data_perm_menu.data_perm_id
            AND common_sys_data_perm_menu.menu_id = #{menuId}
        </where>
    </select>
</mapper>