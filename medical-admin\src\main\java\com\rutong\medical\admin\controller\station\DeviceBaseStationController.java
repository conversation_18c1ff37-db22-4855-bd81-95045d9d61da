package com.rutong.medical.admin.controller.station;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.rutong.medical.admin.dto.station.DeviceBaseStationPageQueryDTO;
import com.rutong.medical.admin.dto.station.DeviceBaseStationSaveDTO;
import com.rutong.medical.admin.service.station.DeviceBaseStationService;
import com.rutong.medical.admin.vo.station.DeviceBaseStationVO;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 基站控制器类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Api(tags = "基站管理")
@RestController
@RequestMapping("/device/base/station/")
public class DeviceBaseStationController {

    @Autowired
    private DeviceBaseStationService deviceBaseStationService;

    @ApiOperation(value = "基站分页查询")
    @GetMapping("page")
    public ResponseResult<MyPageData<DeviceBaseStationVO>>
        page(DeviceBaseStationPageQueryDTO deviceBaseStationPageQuery) {
        return ResponseResult.success(deviceBaseStationService.page(deviceBaseStationPageQuery));
    }

    @ApiOperation(value = "基站查询列表")
    @GetMapping("list")
    public ResponseResult<List<DeviceBaseStationVO>> list(DeviceBaseStationPageQueryDTO deviceBaseStationPageQuery) {
        return ResponseResult.success(deviceBaseStationService.list(deviceBaseStationPageQuery));
    }

    @ApiOperation(value = "删除基站")
    @PostMapping("/delete/{id}")
    public ResponseResult<Void> delete(@PathVariable(value = "id", required = true) Long id) {
        deviceBaseStationService.delete(id);
        return ResponseResult.success();
    }

    @ApiOperation(value = "新增或者修改基站")
    @PostMapping("/saveOrUpdate")
    public ResponseResult<Void> saveOrUpdate(@RequestBody DeviceBaseStationSaveDTO deviceBaseStationSave) {
        deviceBaseStationService.saveOrUpdate(deviceBaseStationSave);
        return ResponseResult.success();
    }

    @ApiOperation(value = "基站详情接口")
    @GetMapping("detail/{id}")
    public ResponseResult<DeviceBaseStationVO> detail(@PathVariable("id") Long id) {
        return ResponseResult.success(deviceBaseStationService.detail(id));
    }
}
