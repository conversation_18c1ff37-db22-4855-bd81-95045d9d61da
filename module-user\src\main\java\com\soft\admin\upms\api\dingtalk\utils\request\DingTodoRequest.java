package com.soft.admin.upms.api.dingtalk.utils.request;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collections;
import java.util.List;

/**
 * 表示一个通知请求的Java类。
 */
@Data
@Accessors(chain = true)
public class DingTodoRequest {

    public static DingTodoRequest simpleTodo(String sendId, String acceptId, String title, String content, String pcUrl, String appUrl) {
        return new DingTodoRequest().setCreatorId(sendId)
                .setExecutorIds(Collections.singletonList(acceptId))
                .setSubject(title)
                .setDescription(content)
                .setNotifyConfigs(new NotifyConfigs())
                .setContentFieldList(Collections.singletonList(new ContentField(content)))
                .setActionList(Collections.singletonList(new Action()))
                .setDetailUrl(new DetailUrl(pcUrl, appUrl));
    }


    /**
     * 主题。
     */
    private String subject;

    /**
     * 通知配置。
     */
    private NotifyConfigs notifyConfigs;

    /**
     * 创建者ID。
     */
    private String creatorId;

    /**
     * 描述。
     */
    private String description;

    /**
     * 执行者ID列表。
     */
    private List<String> executorIds;

    /**
     * 内容字段列表。
     */
    private List<ContentField> contentFieldList;

    /**
     * 详情链接。
     */
    private DetailUrl detailUrl;

    /**
     * 优先级。
     */
    private int priority = 20;

    /**
     * 是否仅显示执行者。
     */
    private boolean isOnlyShowExecutor = true;


    /**
     * 通知配置类。 可以修改成一个对象
     */
    @Data
    public static class NotifyConfigs {

        /**
         * 钉钉通知配置。
         */
        private String dingNotify = "1";

    }

    /**
     * 动作列表。
     */
    private List<Action> actionList;

    /**
     * 动作类。
     */
    @Data
    public static class Action {

        /**
         * 标题。
         */
        private String title = "去处理";

        /**
         * 动作类型。
         */
//        private int actionType;
//
//        /**
//         * 按钮样式类型。
//         */
//        private int buttonStyleType;

    }

    /**
     * 详情链接类。
     */
    @Data
    public static class DetailUrl {

        public DetailUrl() {
        }

        public DetailUrl(String pcUrl, String appUrl) {
            this.appUrl = appUrl;
            this.pcUrl = pcUrl;
        }

        /**
         * PC端链接。
         */
        private String pcUrl;

        /**
         * 移动端链接。
         */
        private String appUrl;
    }

    /**
     * 内容字段类。
     */
    @Data
    public static class ContentField {
        public ContentField() {
        }

        public ContentField(String fieldValue) {
            this.fieldValue = fieldValue;
        }

        /**
         * 字段键。
         */
        private String fieldKey = "详情";

        /**
         * 字段值。
         */
        private String fieldValue;
    }
}
