<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.SysUserPostMapper">
    <resultMap id="BaseResultMap" type="com.soft.admin.upms.model.SysUserPost">
        <id column="user_id" jdbcType="BIGINT" property="userId"/>
        <id column="dept_post_id" jdbcType="BIGINT" property="deptPostId"/>
        <id column="post_id" jdbcType="BIGINT" property="postId"/>
    </resultMap>
    <insert id="insertBatch">
        insert into common_sys_user_post(user_id, dept_post_id, post_id)
        values
            <foreach collection="list" item="item" separator=",">
                (#{item.userId}, #{item.deptPostId}, #{item.postId})
            </foreach>
    </insert>

    <select id="queryByDeptId" resultType="com.soft.admin.upms.model.SysUserPost">
        select user_id, dept_post_id, post_id from common_sys_user_post where dept_post_id in
        (
            select d.dept_post_id from common_sys_dept_post d, common_sys_post p
            where d.post_id = p.post_id and p.leader_post = 1
            and d.dept_id = #{deptId}
        )
    </select>
</mapper>
