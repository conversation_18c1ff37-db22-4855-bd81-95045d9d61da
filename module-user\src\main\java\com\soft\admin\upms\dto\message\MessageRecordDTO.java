package com.soft.admin.upms.dto.message;

import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * SpMessageRecordDTO对象
 *
 * <AUTHOR>
 * @date 2024-02-27
 */
@ApiModel("SpMessageRecordDTO对象")
@Data
public class MessageRecordDTO {

    @ApiModelProperty(value = "消息id")
    @NotNull(message = "数据验证失败，消息id不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "消息标题")
    private String title;

    @ApiModelProperty(value = "消息级别")
    private String level;

    @ApiModelProperty(value = "消息类型")
    private String type;

    @ApiModelProperty(value = "消息内容")
    private String content;

    @ApiModelProperty(value = "业务id")
    private Long busiId;

    @ApiModelProperty(value = "业务类型")
    private String busiType;

    @ApiModelProperty(value = "超链接，json格式")
    private String hyperlink;

    @ApiModelProperty(value = "发送日期")
    private Date sendDate;

    @ApiModelProperty(value = "发送时间")
    private Date sendTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "发送人id")
    private Long sendUserId;

    @ApiModelProperty(value = "接收人id")
    private Long receiveUserId;

    @ApiModelProperty(value = "接收时间")
    private Date receiveTime;

    @ApiModelProperty(value = "阅读时间")
    private Date readTime;

    @ApiModelProperty(value = "是否已读：0未读；1已读")
    private Integer isRead;

}
