<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rutong.medical.admin.mapper.location.UserLocationTDMapper">



    <insert id="insert" parameterType="com.rutong.medical.admin.entity.location.UserLocation">
        INSERT INTO ${tableName}
            USING alarm_location_detail
    TAGS (#{deviceTypeCode}, #{deviceSn})
    VALUES
         (#{createTime},
            #{isAlarm},
            #{isKey},
            #{newLocatorSn},
            #{oldLocatorSn},
            #{userId},
            #{userName},
            #{baseStationSn},
            #{buildingId},
            #{floorId},
            #{pointId},
            #{buildingName},
            #{floorName},
            #{pointName},
            #{alarmDetailId},
            #{alarmType}
        )
    </insert>

</mapper>
