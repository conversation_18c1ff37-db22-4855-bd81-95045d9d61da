package com.soft.admin.upms.listener;

import com.alibaba.fastjson.JSONObject;
import com.dingtalk.open.app.api.OpenDingTalkStreamClientBuilder;
import com.dingtalk.open.app.api.security.AuthClientCredential;
import com.dingtalk.open.app.stream.protocol.event.EventAckStatus;
import com.soft.admin.upms.api.dingtalk.DingTalkConfig;
import com.soft.admin.upms.api.dingtalk.DingTalkEventEnums;
import com.soft.admin.upms.enums.OaTypeEnums;
import com.soft.admin.upms.service.OaSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description 钉钉事件监听
 * @Date 0008, 2023年8月8日 15:05
 * <AUTHOR>
 **/
@Slf4j
@Component
public class DingTalkEventListener implements ApplicationRunner {

    @Resource
    private DingTalkConfig dingTalkConfig;
    @Resource
    private OaSyncService oaSyncService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        if(Objects.isNull(dingTalkConfig.getEnable()) || !dingTalkConfig.getEnable() || !dingTalkConfig.isStreamEnable()) {
            log.info("系统未启用钉钉配置");
            return;
        }
        OpenDingTalkStreamClientBuilder
                .custom()
                .credential(new AuthClientCredential(dingTalkConfig.getAppKey(), dingTalkConfig.getAppSecret()))
                //注册事件监听
                .registerAllEventListener(event -> {
                    try {
                        //事件唯一Id
                        String eventId = event.getEventId();
                        //事件类型
                        String eventType = event.getEventType();
                        //事件产生时间
                        Long bornTime = event.getEventBornTime();
                        //获取事件体
                        JSONObject bizData = event.getData();
                        //初始化参数
                        oaSyncService.init();

                        //处理事件
                        switch (DingTalkEventEnums.getByValue(eventType)){
                            case DEPT_CREATE:
                            case DEPT_MODIFY:
                                oaSyncService.syncDeptByOaDeptIdsCallback(OaTypeEnums.DING_TALK, bizData.getJSONArray("deptId").toJavaList(String.class));
                                break;
                            case USER_ADD_ORG:
                            case USER_MODIFY_ORG:
                                oaSyncService.syncUserByOaUserIds(OaTypeEnums.DING_TALK, bizData.getJSONArray("userId").toJavaList(String.class));
                                break;
                            case USER_LEAVE_ORG:
                                oaSyncService.leaveUserByOaUserIds(OaTypeEnums.DING_TALK, bizData.getJSONArray("userId").toJavaList(String.class));
                                break;
                            default:
                                log.info("ding talk event no handler:{}", eventType);
                        }
                        log.info("DingTalkEventListener: {} : {}", eventType, bizData.toJSONString());
                        //消费成功
                        return EventAckStatus.SUCCESS;
                    } catch (Exception e) {
                        //消费失败
                        return EventAckStatus.LATER;
                    }
                })
                .build().start();
    }
}
