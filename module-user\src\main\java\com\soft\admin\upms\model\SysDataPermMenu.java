package com.soft.admin.upms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

/**
 * 数据权限与菜单关联实体对象。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
@Data
@ToString(of = {"menuId"})
@TableName(value = "common_sys_data_perm_menu")
public class SysDataPermMenu {

    /**
     * 数据权限Id。
     */
    private Long dataPermId;

    /**
     * 关联菜单Id。
     */
    private Long menuId;
}
