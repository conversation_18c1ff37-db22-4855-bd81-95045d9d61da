package com.rutong.medical.admin.entity.location;

import lombok.Data;

import java.sql.Timestamp;

/**
 * @ClassName UserLocation
 * @Description 人员定位
 * <AUTHOR>
 * @Date 2025/7/15 10:33
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Data
public class UserLocation {

    /**
     * 表名
     */
    private String tableName;

    /**=============标签字段================**/

    /**
     * 设备类型
     */
    private String deviceTypeCode;

    /**
     * 设备Sn
     */
    private String deviceSn;


    /**=============采集字段================**/
    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 是否报警
     * 0-否, 1-是
     */
    private Byte isAlarm = 0;

    /**
     * 是否按键
     * 0-否, 1-是
     */
    private Byte isKey = 0;


    /**
     * 报警记录表ID
     */
    private Long alarmDetailId;

    /**
     * 报警类型
     * 1-低电压, 2-按键, 3-防拆, 4-红外入侵
     */
    private Byte alarmType = 0;


    /**
     * 新基站编号
     */
    private String newLocatorSn;

    /**
     * 旧基站编号
     */
    private String oldLocatorSn;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 当前基站编号
     */
    private String baseStationSn;


    /**
     * 楼栋ID
     */
    private Long buildingId;

    /**
     * 楼层ID
     */
    private Long floorId;

    /**
     * 点位ID
     */
    private Long pointId;

    /**
     * 楼栋名称
     */
    private String buildingName;

    /**
     * 楼层名称
     */
    private String floorName;

    /**
     * 点位名称
     */
    private String pointName;

}
