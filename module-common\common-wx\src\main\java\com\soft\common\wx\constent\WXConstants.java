package com.soft.common.wx.constent;

/**
 * 微信常量。
 *
 * <AUTHOR>
 * @date 2023-09-12
 */
public class WXConstants {
    // 微信公众号获取用户信息的授权url
    public static final String WX_AUTH_URL = "https://open.weixin.qq.com/connect/oauth2/authorize";
    // 公众号网页授权，用于获取用户信息的token url
    public static final String WX_USER_ACCESS_TOKEN = "https://api.weixin.qq.com/sns/oauth2/access_token";
    // 获取用户信息
    public static final String WX_USER_INFO = "https://api.weixin.qq.com/sns/userinfo";
    // 获取公众号accessToken                        https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s
    public static final String WX_ACCESS_TOKEN = "https://api.weixin.qq.com/cgi-bin/token";
    //
    public static final String WX_DOWNLOAD_PHOTO = "http://file.api.weixin.qq.com/cgi-bin/media/get";
    //
    public static final String WX_JSAPI_TICKET = "https://api.weixin.qq.com/cgi-bin/ticket/getticket";

    public static final String WX_CREATE_MENU = "https://api.weixin.qq.com/cgi-bin/menu/create";

    // 微信客户端中授权地址
    public static final String WX_M_AUTH_LOCATION = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=%s&state=%s#wechat_redirect";
    public static final String WX_PC_AUTH_LOCATION = "https://open.weixin.qq.com/connect/qrconnect?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_login&state=%s#wechat_redirect";
}
