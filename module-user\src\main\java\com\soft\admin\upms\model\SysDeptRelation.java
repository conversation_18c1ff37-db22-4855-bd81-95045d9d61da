package com.soft.admin.upms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 部门关联实体对象。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "common_sys_dept_relation")
public class SysDeptRelation {

    /**
     * 上级部门Id。
     */
    private Long parentDeptId;

    /**
     * 部门Id。
     */
    private Long deptId;
}
