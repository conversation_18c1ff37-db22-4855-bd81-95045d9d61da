package com.soft.admin.upms;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 应用服务启动类。 要用的时候可以打开注释
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
//@EnableAsync
//@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, DruidDataSourceAutoConfigure.class})
//@ComponentScan("com.soft")
//@MapperScan(basePackages = {"com.soft.generator.dao", "com.soft.sub.dao.**"})
//public class WebAdminApplication {
//
//	public static void main(String[] args) {
//		SpringApplication.run(WebAdminApplication.class, args);
//	}
//}
