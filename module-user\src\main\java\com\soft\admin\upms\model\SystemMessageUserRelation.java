package com.soft.admin.upms.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 系统消息与用户关系对象 sp_system_message_user_relation
 *
 * <AUTHOR>
 * @date 2023-06-26
 */
@Data
@TableName(value = "sp_system_message_user_relation")
public class SystemMessageUserRelation {

    @TableId(value = "id")
    private Long id;

    /**
     * 消息id
     */
    private Long messageId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 已读1，未读0
     */
    private Boolean isRead;

    private Date createTime;

    /**
     * 发送状态 0未发 1已发
     */
    private Boolean isSend;
}
