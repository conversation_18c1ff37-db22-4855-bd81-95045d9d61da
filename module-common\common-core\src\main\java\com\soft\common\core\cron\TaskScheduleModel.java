package com.soft.common.core.cron;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/3/23 14:40
 * @Version 1.0
 */
@Data
public class TaskScheduleModel {
    /**
     * 所选作业类型:
     * 0  -> 每次
     * 1  -> 每天
     * 2  -> 每月
     * 3  -> 每周
     */
    Integer jobType;

    /**
     * 一周的哪几天
     */
    Integer[] dayOfWeeks;

    /**
     * 一个月的哪几天
     */
    Integer[] dayOfMonths;

    /**
     * 秒
     */
    Integer second;

    /**
     * 分
     */
    Integer minute;

    /**
     * 时
     */
    Integer hour;

    /**
     * 循环周期
     */
    Integer cyclePeriod;

}
