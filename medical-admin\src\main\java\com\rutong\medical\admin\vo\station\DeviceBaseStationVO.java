package com.rutong.medical.admin.vo.station;

import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * DeviceBaseStationVO视图对象
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@ApiModel("DeviceBaseStationVO视图对象")
@Data
public class DeviceBaseStationVO {

    @ApiModelProperty(value = "基站表ID")
    private Long id;

    @ApiModelProperty(value = "基站编号")
    private String deviceBaseStationCode;

    @ApiModelProperty(value = "基站分类表ID")
    private Long deviceBaseStationType;

    @ApiModelProperty(value = "基站分类表名称")
    private String deviceBaseStationTypeName;

    @ApiModelProperty(value = "基站名称")
    private String deviceBaseStationName;

    @ApiModelProperty(value = "支持协议")
    private String protocol;

    @ApiModelProperty(value = "IP地址")
    private String ip;

    @ApiModelProperty(value = "位置")
    private Long spaceId;

    @ApiModelProperty(value = "位置")
    private String spacePath;

    @ApiModelProperty(value = "位置")
    private String spaceFullName;

    @ApiModelProperty(value = "X")
    private Long x;

    @ApiModelProperty(value = "Y")
    private Long y;

    @ApiModelProperty(value = "Z")
    private Long z;

    @ApiModelProperty(value = "经度")
    private Long longitude;

    @ApiModelProperty(value = "纬度")
    private Long latitude;

    @ApiModelProperty(value = "在线状态(1:在线,0:离线)")
    private Integer isOnline;

    @ApiModelProperty(value = "创建人")
    private Long createUserId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改人")
    private Long updateUserId;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

    @ApiModelProperty(value = "视频监控表ID")
    private List<Long> deviceMonitorIdList;
}
