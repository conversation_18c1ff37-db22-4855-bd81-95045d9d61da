package com.soft.admin.upms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @className: SysUserDevice
 * @author: <PERSON><PERSON>eYu
 * @date: 2024/9/13 上午9:21
 * @description:
 */
@Data
@TableName(value = "common_sys_user_device")
public class SysUserDevice {
    /**
     * id
     */
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 设备id
     */
    private String registrationId;
    /**
     * 创建时间
     */
    private Date createTime;
}
