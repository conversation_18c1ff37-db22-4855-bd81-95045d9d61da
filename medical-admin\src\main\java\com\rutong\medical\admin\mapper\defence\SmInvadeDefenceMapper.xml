<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rutong.medical.admin.mapper.defence.SmInvadeDefenceMapper">

    <!-- 分页查询防区列表 -->
    <select id="selectDefencePage" parameterType="com.rutong.medical.admin.dto.defence.DefenceManageDTO"
            resultType="com.rutong.medical.admin.vo.defence.SmInvadeDefenceVO">
        SELECT
        id,
        defence_code as defenceCode,
        defence_name as defenceName,
        defence_state as defenceState,
        start_time as startTime,
        end_time as endTime,
        create_user_id as createUserId,
        create_time as createTime,
        update_user_id as updateUserId,
        update_time as updateTime,
        is_delete as isDelete
        FROM sm_invade_defence
        <where>
            is_delete = 0
            <if test="defenceName != null and defenceName != ''">
                AND defence_name LIKE CONCAT('%', #{defenceName}, '%')
            </if>
            <if test="defenceState != null">
                AND defence_state = #{defenceState}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!--    &lt;!&ndash; 根据防区编号查询防区详情 &ndash;&gt;-->
    <!--    <select id="selectDefenceDetailByCode" parameterType="string"-->
    <!--            resultType="com.rutong.medical.admin.vo.defence.DefenceDetailVO">-->
    <!--        SELECT-->
    <!--            id,-->
    <!--            defence_code as defenceCode,-->
    <!--            defence_name as defenceName,-->
    <!--            defence_state as defenceState,-->
    <!--            start_time as startTime,-->
    <!--            end_time as endTime,-->
    <!--            create_time as createTime,-->
    <!--            update_time as updateTime-->
    <!--        FROM sm_invade_defence-->
    <!--        WHERE defence_code = #{defenceCode}-->
    <!--          AND is_delete = 0-->
    <!--    </select>-->

    <!--    &lt;!&ndash; 根据防区ID查询关联的设备列表 &ndash;&gt;-->
    <!--    <select id="selectDefenceDevices" parameterType="long"-->
    <!--            resultType="com.rutong.medical.admin.vo.defence.DefenceDetailVO$DefenceDeviceVO">-->
    <!--        SELECT-->
    <!--            d.id as deviceId,-->
    <!--            d.device_name as deviceName,-->
    <!--            d.device_code as deviceCode,-->
    <!--            d.device_sn as deviceSn,-->
    <!--            d.space_full_name as spaceFullName,-->
    <!--            d.is_online as isOnline-->
    <!--        FROM sm_invade_defence df-->
    <!--        JOIN sm_invade_defence_device dfd ON df.id = dfd.invade_defence_id-->
    <!--        JOIN sm_device d ON dfd.device_id = d.id-->
    <!--        WHERE df.id = #{defenceId}-->
    <!--          AND df.is_delete = 0-->
    <!--          AND d.is_delete = 0-->
    <!--    </select>-->

    <select id="selectDevicePage" resultType="com.rutong.medical.admin.vo.defence.DetailVO">

        SELECT
        id,
        defence_code as defenceCode,
        defence_name as defenceName,
        space_FullName as spaceFullName
        FROM sm_device
        <where>
            is_delete = 0
            <if test="defenceName != null and defenceName != ''">
                AND defence_name LIKE CONCAT('%', #{defenceName}, '%')
            </if>
            <if test="defenceState != null">
                AND defence_state = #{defenceState}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>


<!--    解除关联-->
    <delete id="deleteConnect">
        delete
    </delete>




</mapper>