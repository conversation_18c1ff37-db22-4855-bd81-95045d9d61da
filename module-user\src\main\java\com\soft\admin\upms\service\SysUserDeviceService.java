package com.soft.admin.upms.service;

import com.soft.admin.upms.dto.SysUserDeviceDTO;
import com.soft.admin.upms.model.SysUserDevice;
import com.soft.common.core.base.service.IBaseService;

/**
 * <AUTHOR>
 * @Description
 * @Date 上午9:46 2024/9/13
 * @Param
 * @return
 **/

public interface SysUserDeviceService extends IBaseService<SysUserDevice, Long> {

    /**
    * <AUTHOR>
    * @Description 新增设备信息
    * @Date 下午5:15 2024/9/13
    * @Param [sysUserDevice]
    * @return java.lang.Boolean
    **/
    Boolean insertUserDevice(SysUserDeviceDTO sysUserDevice);
}
