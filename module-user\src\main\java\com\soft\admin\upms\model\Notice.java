package com.soft.admin.upms.model;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.soft.admin.upms.vo.NoticeVO;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.common.core.base.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Date;

/**
 * 公告通知对象 sp_notice
 *
 * <AUTHOR>
 * @date 2023-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sp_notice")
public class Notice extends BaseModel {

    @TableId(value = "id")
    private Long id;

    /**
     * 类型
     */
    private String type;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 状态：发布1，未发布0
     */
    private Integer status;

    /**
     * 有效期：开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date beginTime;

    /**
     * 有效期：结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date endTime;

    /**
     * 排序
     */
    private Integer showOrder;


    @Mapper
    public interface NoticeModelMapper extends BaseModelMapper<NoticeVO, Notice> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        Notice toModel(NoticeVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        NoticeVO fromModel(Notice entity);
    }

    public static final NoticeModelMapper INSTANCE = Mappers.getMapper(NoticeModelMapper.class);
}
