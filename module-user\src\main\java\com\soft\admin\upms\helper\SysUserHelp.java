package com.soft.admin.upms.helper;

import cn.hutool.core.collection.CollectionUtil;
import com.soft.admin.upms.dao.SysDeptMapper;
import com.soft.admin.upms.dao.SysUserDeptMapper;
import com.soft.admin.upms.dao.SysUserMapper;
import com.soft.admin.upms.model.SysDept;
import com.soft.admin.upms.model.SysUserDept;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户帮助类
 */
@Component
@RequiredArgsConstructor
public class SysUserHelp {

    private final SysUserMapper sysUserMapper;
    private final SysDeptMapper sysDeptMapper;
    private final SysUserDeptMapper sysUserDeptMapper;


    /**
     * 通过用户id查找所属部门集合
     *
     * @return 部门集合
     */
    public List<SysDept> getDeptListByUserId(Long userId) {
        if (Objects.isNull(userId)) return Collections.emptyList();
        List<SysUserDept> sysUserDeptList = sysUserDeptMapper.selectList(SysUserDept::getUserId, userId);
        if (CollectionUtil.isEmpty(sysUserDeptList)) return Collections.emptyList();

        List<Long> deptListId = sysUserDeptList.stream().map(SysUserDept::getDeptId).collect(Collectors.toList());
        return sysDeptMapper.selectBatchIds(deptListId);
    }
}
