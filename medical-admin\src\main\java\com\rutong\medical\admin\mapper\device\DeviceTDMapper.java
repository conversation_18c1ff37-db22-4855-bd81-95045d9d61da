package com.rutong.medical.admin.mapper.device;

import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.rutong.medical.admin.entity.device.AlarmLocationDetailTD;
import com.rutong.medical.admin.entity.device.DeviceTD;

@DS("tdengine")
public interface DeviceTDMapper {

    /**
     * 根据id查询设备子表数据
     * 
     * @param id
     * @return
     */
    DeviceTD selectById(Long id);

    /**
     * 删除设备子表数据
     * 
     * @param deviceSn
     * @return
     */
    int deleteByDeviceSn(@Param("deviceSn") String deviceSn);

    /**
     * 使用Map参数插入数据到设备子表
     * 
     * @param params 包含tableName和deviceData的Map
     * @return 影响的行数
     */
    int insertIntoDeviceTable(@Param("params") Map<String, Object> params);


    /**
     * 根据设备编号和表名查询空间名称
     * @param deviceSn
     * @param tableName
     * @return
     */
    AlarmLocationDetailTD getSpaceFullName(@Param("deviceSn") String deviceSn, @Param("tableName") String tableName, @Param("deviceTypeCode") String deviceTypeCode);
}