<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.OaUserMapper">
    <resultMap type="com.soft.admin.upms.model.OaUser" id="OaUserResult">
        <result property="userId" column="user_id" />
        <result property="oaUserId" column="oa_user_id" />
        <result property="oaType" column="oa_type" />
    </resultMap>

    <sql id="columns">
         user_id, oa_user_id, oa_type
    </sql>
    <sql id="selectOaUserVo">
        select <include refid="columns"/> sp_oa_user
    </sql>
    <insert id="batchInsert">
        insert into
            sp_oa_user(user_id, oa_user_id, oa_type)
        values
            <foreach collection="list" item="item" separator=",">
                (
                 #{item.userId},
                 #{item.oaUserId},
                 #{item.oaType}
                )
            </foreach>
    </insert>
    <select id="selectByOaUserIds" resultMap="OaUserResult">
        select
            <include refid="columns"/>
        from
            sp_oa_user
        where
            oa_user_id in
            <foreach collection="list" item="oaUserId" open="(" separator="," close=")">
                #{oaUserId}
            </foreach>
    </select>

</mapper>