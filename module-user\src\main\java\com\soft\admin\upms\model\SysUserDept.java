package com.soft.admin.upms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@TableName(value = "common_sys_dept_user")
public class SysUserDept {

    /**
     * 用户Id。
     */
    private Long userId;

    /**
     * 部门Id。
     */
    private Long deptId;

    /**
     * 是否领导
     */
    private Integer leader;

    /**
     * 同步版本。
     */
    private Long syncVersion;


    public SysUserDept() {
    }

    public SysUserDept(Long userId, Long deptId, Integer leader, Long syncVersion) {
        this.userId = userId;
        this.deptId = deptId;
        this.leader = leader;
        this.syncVersion = syncVersion;
    }


    public SysUserDept(Long userId, Long deptId, Integer leader) {
        this.userId = userId;
        this.deptId = deptId;
        this.leader = leader;
        // 给定一个老大的版本号
        this.syncVersion = 99999999999999L;
    }
}
