package com.soft.admin.upms.controller;

import com.github.pagehelper.PageHelper;
import com.soft.admin.upms.dto.NoticeQueryDTO;
import com.soft.admin.upms.dto.NoticeSaveDTO;
import com.soft.admin.upms.service.NoticeService;
import com.soft.admin.upms.vo.NoticeVO;
import com.soft.common.core.annotation.NoAuthInterface;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.common.core.util.MyPageUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公告通知控制器类
 *
 * <AUTHOR>
 * @date 2023-07-03
 */
@Api(tags = "公告通知接口")
@RestController
@RequestMapping("/core/notice")
public class NoticeController {

    @Autowired
    private NoticeService noticeService;

    @NoAuthInterface
    @ApiOperation(value = "数据查询")
    @GetMapping("/list")
    public ResponseResult<MyPageData<NoticeVO>> list(NoticeQueryDTO queryDTO) {
        if (queryDTO.getPageNum() != null && queryDTO.getPageSize() != null) {
            PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        }
        List<NoticeVO> list = noticeService.queryList(queryDTO);
        return ResponseResult.success(MyPageUtil.makeResponseData(list));
    }

    @ApiOperation(value = "保存")
    @PostMapping("/save")
    public ResponseResult<Void> save( @RequestBody NoticeSaveDTO saveDTO) {
        noticeService.save(saveDTO);
        return ResponseResult.success();
    }

    @ApiOperation(value = "删除")
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@RequestParam Long id, @RequestParam String type) {
        noticeService.delete(id, type);
        return ResponseResult.success();
    }

    @ApiOperation(value = "发布or撤销")
    @PostMapping("/updateStatus")
    public ResponseResult<Void> updateStatus(@RequestParam Long id, @RequestParam Integer status) {
        noticeService.updateStatus(id, status);
        return ResponseResult.success();
    }

}
