package com.soft.admin.upms.dao;

import com.soft.admin.upms.dto.NoticeQueryDTO;
import com.soft.admin.upms.model.Notice;
import com.soft.admin.upms.vo.NoticeVO;
import com.soft.common.core.base.dao.BaseDaoMapper;

import java.util.List;

/**
 * 公告通知Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-03
 */
public interface NoticeMapper extends BaseDaoMapper<Notice> {

    /**
     * 数据查询
     *
     * @param queryDTO
     * @return
     */
    List<NoticeVO> queryList(NoticeQueryDTO queryDTO);

}
