package com.soft.admin.upms.controller;

import com.soft.admin.upms.dto.message.*;
import com.soft.admin.upms.vo.SystemMessageVO;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.admin.upms.service.MessageRecordService;
import com.soft.admin.upms.vo.message.MessageRecordUnreadCountVO;
import com.soft.admin.upms.vo.message.MessageRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 消息中心记录控制器类
 *
 * <AUTHOR>
 * @date 2024-02-27
 */
@Api(tags = "消息中心记录接口管理")
@RestController
@RequestMapping("/message/record")
public class MessageRecordController {

    @Resource
    private MessageRecordService messageRecordService;


    @ApiOperation("我的消息列表")
    @GetMapping("/my/list")
    public ResponseResult<MyPageData<MessageRecordVO>> myList(MessageRecordQueryDTO messageRecordQueryDTO) {
        MyPageData<MessageRecordVO> pageData = messageRecordService.myList(messageRecordQueryDTO);
        return ResponseResult.success(pageData);
    }


    @ApiOperation("未读数量")
    @GetMapping("/unread/count")
    public ResponseResult<MessageRecordUnreadCountVO> unreadCount() {
        MessageRecordUnreadCountVO messageRecordUnreadCountVO = messageRecordService.unreadCount();
        return ResponseResult.success(messageRecordUnreadCountVO);
    }


    @ApiOperation("阅读")
    @GetMapping("/read")
    public ResponseResult<MessageRecordVO> read(@RequestParam Long id) {
        MessageRecordVO messageRecordVO = messageRecordService.read(id);
        return ResponseResult.success(messageRecordVO);
    }

    @ApiOperation("批量已读/全部已读")
    @PostMapping("/read-batch")
    public ResponseResult<Void> readBatch(@RequestBody MessageRecordReadDTO messageRecordReadDTO) {
        messageRecordService.readBatch(messageRecordReadDTO);
        return ResponseResult.success();
    }


    @ApiOperation("删除消息")
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@RequestBody MessageRecordDeleteDTO messageRecordDeleteDTO) {
        messageRecordService.delete(messageRecordDeleteDTO);
        return ResponseResult.success();
    }


    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    @ApiOperation("消息管理列表")
    @GetMapping("/manager/list")
    public ResponseResult<MyPageData<SystemMessageVO>> managerList(MessageRecordQueryDTO dto) {
        MyPageData<SystemMessageVO> pageData = messageRecordService.managerMessageList(dto);
        return ResponseResult.success(pageData);
    }

    @ApiOperation("消息管理新增编辑")
    @PostMapping("/manager/addOrUpdate")
    public ResponseResult<?> addOrUpdate(@RequestBody @Validated MessageRecordAddDTO dto) {
         messageRecordService.managerMessageAdd(dto);
        return ResponseResult.success();
    }

    @ApiOperation("消息管理发布")
    @PostMapping("/manager/issue")
    public ResponseResult<?> issue(@RequestBody @Validated MessageRecordIssueDTO dto) {
        messageRecordService.managerMessageIssue(dto);
        return ResponseResult.success();
    }
    @ApiOperation("消息管理详情")
    @GetMapping("/manager/detail")
    public ResponseResult<SystemMessageVO> managerDetail(Long id) {
        return ResponseResult.success(messageRecordService.managerMessageDetail(id));
    }


    @ApiOperation("删除消息")
    @GetMapping("/manager/delete")
    public ResponseResult<Void> managerDelete(Long id) {
        messageRecordService.managerMessageDelete(id);
        return ResponseResult.success();
    }

}
