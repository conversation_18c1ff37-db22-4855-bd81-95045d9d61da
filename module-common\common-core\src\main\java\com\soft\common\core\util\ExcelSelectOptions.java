package com.soft.common.core.util;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class ExcelSelectOptions {

    private final ConcurrentHashMap<Integer, Select> selectOptions;

    /**
     * 私有构造器
     */
    private ExcelSelectOptions() {
        selectOptions = new ConcurrentHashMap<>();
    }

    public static ExcelSelectOptions createSelectOptions() {
        return new ExcelSelectOptions();
    }

    public ExcelSelectOptions put(Integer index, String... selectOption) {
        selectOptions.put(index, new Select(SelectType.SINGLE, selectOption));
        return this;
    }

    public ExcelSelectOptions put(Integer index, SelectType selectType, String... selectOption) {
        selectOptions.put(index, new Select(selectType == null ? SelectType.SINGLE : selectType, selectOption));
        return this;
    }

    public Select get(Integer index) {
        return selectOptions.get(index);
    }

    public Map<Integer, Select> getSelectOptions() {
        return selectOptions;
    }


    public enum SelectType {

        // 单选
        SINGLE,

        // 多选
        MULTIPLE
    }

    @Data
    @AllArgsConstructor
    public static class Select {

        /**
         * 下拉选类型：1 单选；2多选
         */
        private SelectType selectType;

        /**
         * 下拉选项
         */
        private String[] selectOptions;
    }
}
