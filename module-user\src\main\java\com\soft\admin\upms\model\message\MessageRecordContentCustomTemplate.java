package com.soft.admin.upms.model.message;


import com.soft.admin.upms.api.dingtalk.enums.DingTalkMessageType;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.soft.admin.upms.enums.MessageRecordBusiTypeEnums.CUSTOM;

public class MessageRecordContentCustomTemplate extends MessageRecordContentNoticeTemplate {


    /**
     * 暂时只发通知
     */
    public MessageRecordContent custom(Long msgId, String title, String content, Long sendUserId, List<Long> receiveUserIds) {
        messageRecordContent.setTitle(title);
        messageRecordContent.setBusiId(1L);
        messageRecordContent.setBusiType(CUSTOM);
        messageRecordContent.setLevel("普通");
        messageRecordContent.setContent(content);
        messageRecordContent.setSendUserId(sendUserId);
        messageRecordContent.setReceiveUserIds(receiveUserIds);
        messageRecordContent.setHyperlink("/pages/message/message");
        messageRecordContent.setType(TYPE);
        messageRecordContent.setMsgId(msgId);
        messageRecordContent.setDingTalkMessageType(DingTalkMessageType.ACTION_CARD);
        pushJiGuangMessage(Boolean.FALSE);
        push();
        workNotice(messageRecordContent);
        return messageRecordContent;
    }

}
