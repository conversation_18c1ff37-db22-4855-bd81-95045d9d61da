package com.rutong.medical.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.rutong.medical.admin.entity.AlarmDetailTD;
import com.rutong.medical.admin.entity.Test;
import com.rutong.medical.admin.service.TestService;
import com.rutong.medical.admin.vo.system.SpaceVO;
import com.rutong.medical.common.mybatis.support.Condition;
import com.rutong.medical.common.mybatis.support.Query;
import com.soft.common.core.annotation.NoAuthInterface;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.common.core.object.TDenginePageParam;
import com.soft.common.core.object.TDenginePageResult;
import com.soft.common.core.util.MyPageUtil;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

/**
 * @ClassName UserController
 * @Description 测试接口，以后会删掉
 * <AUTHOR>
 * @Date 2025/7/9 14:41
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@RestController
@AllArgsConstructor
public class TestController {

    private TestService testService;

    @GetMapping("/list")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "获取列表", notes = "获取列表")
    @NoAuthInterface
    public ResponseResult<List<Test>> list(Test test) {
        test.setAge(14);
        List<Test> list = testService.list(Condition.getQueryWrapper(test));

        return ResponseResult.success(list);
    }

    public ResponseResult<Void> add(@RequestBody Test test){
        testService.save(test);
        return  ResponseResult.success();
    }

    public ResponseResult<Void> update(@RequestBody Test test){
        testService.updateById(test);
        return  ResponseResult.success();
    }

    public ResponseResult<Void> delete(@RequestParam Long id){
        testService.removeById(id);
        return  ResponseResult.success();
    }


    @GetMapping("/page")
    @ApiOperation(value = "单表分页", notes = "传入test")
    public ResponseResult<IPage<Test>> page(Test test, Query query) {
        IPage<Test> pages = testService.page(Condition.getPage(query), Condition.getQueryWrapper(test));
        return ResponseResult.success(pages);
    }


    @GetMapping("/zdypage")
    @ApiOperation(value = "多表关联自定义分页", notes = "传入test")
    public ResponseResult<IPage<Test>> zdypage(Test test, Query query) {
        //可以从前端传值
        query.setDescs("age");
        IPage<Test> pages = testService.selectTestPage(Condition.getPage(query), test);
        return ResponseResult.success(pages);
    }

    @GetMapping("/tdtest")
    public ResponseResult<List<AlarmDetailTD>> tdtest(){
        return ResponseResult.success(testService.selectByTimeRange());
    }

    @GetMapping("/tdpagelist")
    public  ResponseResult<TDenginePageResult<AlarmDetailTD>> tdpagelist(TDenginePageParam tDenginePageParam){
        TDenginePageResult<AlarmDetailTD> page = testService.selectByTimeRangePage();
        return ResponseResult.success(page);
    }

    @GetMapping("/addTD")
    public String addTD(){
        testService.addTD();
        return "success";
    }

    @GetMapping("/selectTestById")
    public ResponseResult<Test> selectTestById(Integer id){
        Test test = testService.selectTest(id);
        return ResponseResult.success(test);
    }


}
