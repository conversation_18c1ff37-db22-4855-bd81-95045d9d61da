package com.rutong.medical.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.rutong.medical.admin.entity.AlarmDetailTD;
import com.rutong.medical.admin.entity.Test;
import com.rutong.medical.admin.vo.system.SpaceVO;
import com.rutong.medical.common.mybatis.base.BaseService;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.TDenginePageResult;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * @ClassName UserService
 * @Description
 * <AUTHOR>
 * @Date 2025/7/9 14:43
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
public interface TestService extends BaseService<Test> {

    /**
     * 自定义分页
     *
     */
    IPage<Test> selectTestPage(IPage<Test> page, Test test);
    public Test selectTest(Integer id);

    List<AlarmDetailTD> selectByTimeRange();

    TDenginePageResult<AlarmDetailTD> selectByTimeRangePage();

    void addTD();
}
