package com.soft.admin.upms.dto;

import com.soft.common.core.object.MyPageParam;
import com.soft.common.core.validator.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * NoticeDTO对象
 *
 * <AUTHOR>
 * @date 2023-07-03
 */
@ApiModel("NoticeQueryDTO对象")
@Data
public class NoticeQueryDTO extends MyPageParam {


    @ApiModelProperty(value = "类型")
    private String type;


    @ApiModelProperty(value = "标题")
    private String title;


    @ApiModelProperty(value = "状态：发布1，未发布0")
    private Integer status;


    @ApiModelProperty(value = "用户id")
    private String userId;


    @ApiModelProperty(value = "查询来源：pc、app")
    private String source;

}
