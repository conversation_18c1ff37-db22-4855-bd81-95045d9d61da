package com.soft.admin.upms.model.message;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.admin.upms.enums.MessageRecordBusiTypeEnums;
import com.soft.admin.upms.enums.MessageRecordTypeEnums;
import com.soft.admin.upms.vo.message.MessageRecordVO;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

/**
 * 消息中心记录对象 sp_message_record
 *
 * <AUTHOR>
 * @date 2024-02-27
 */
@Data
@TableName(value = "sp_message_record")
public class MessageRecord {

    /**
     * 消息id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息级别
     */
    private String level;

    /**
     * 消息类型
     */
    private MessageRecordTypeEnums type;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 业务id
     */
    private Long busiId;

    /**
     * 业务类型
     */
    private MessageRecordBusiTypeEnums busiType;

    /**
     * 超链接
     */
    private String hyperlink;

    /**
     * 发送日期
     */
    private Date sendDate;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 创建时间。
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 发送人id
     */
    private Long sendUserId;

    /**
     * 接收人id
     */
    private Long receiveUserId;

    /**
     * 接收时间
     */
    private Date receiveTime;

    /**
     * 阅读时间
     */
    private Date readTime;

    /**
     * 是否已读：0未读；1已读
     */
    private Integer isRead;

    @TableField(exist = false)
    private List<Long> receiveList;


    /**
     * 删除标识，1正常；-1删除
     */
    private Integer deletedFlag;


    @Mapper
    public interface MessageRecordModelMapper extends BaseModelMapper<MessageRecordVO, MessageRecord> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        MessageRecord toModel(MessageRecordVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        MessageRecordVO fromModel(MessageRecord entity);
    }

    public static final MessageRecordModelMapper INSTANCE = Mappers.getMapper(MessageRecordModelMapper.class);
}
