<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rutong.medical.admin.mapper.alarm.AlarmDetailTDMapper">


    <select id="selectAlarmDetailList" resultType="com.rutong.medical.admin.vo.alarm.AlarmDetailTDVO">
        <![CDATA[
        SELECT device_type_code,
               device_sn,
               alarm_type, FIRST (new_locator_sn) AS new_locator_sn, FIRST (floor_id) AS floor_id, FIRST (point_id) AS point_id,
               FIRST (create_time) AS create_time, sum (is_key) AS is_key_count
        FROM
            MEDICAL_IOT.alarm_location_detail
        WHERE
            is_alarm = 1
          AND create_time >= #{startTime}
          AND create_time <= #{endTime}
        GROUP BY
            device_type_code,
            device_sn,
            alarm_type
        ]]>

    </select>

</mapper>
