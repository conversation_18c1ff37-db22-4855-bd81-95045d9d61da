package com.soft.admin.upms.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.admin.upms.vo.SysDeptVo;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.common.core.base.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.io.Serializable;

/**
 * SysDept实体对象。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "common_sys_dept")
public class SysDept extends BaseModel implements Serializable {

    private static final long serialVersionUID = -7450019708567746487L;
    /**
     * 部门Id。
     */
    @TableId(value = "dept_id")
    private Long deptId;


    /**
     * 部门编号：ZZ+时间
     */
    private String deptCode;

    /**
     * 部门路径
     */
    private String deptPath;

    /**
     * 部门名称。
     */
    private String deptName;

    /**
     * 部门描述
     */
    private String deptDesc;

    /**
     * 显示顺序。
     */
    private Integer showOrder;

    /**
     * 父部门Id。
     */
    private Long parentId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic(delval = "-1", value = "1")
    private Integer deletedFlag;

    @Mapper
    public interface SysDeptModelMapper extends BaseModelMapper<SysDeptVo, SysDept> {
    }
    public static final SysDeptModelMapper INSTANCE = Mappers.getMapper(SysDeptModelMapper.class);
}
