<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.SysPermCodeMapper">
    <resultMap id="BaseResultMap" type="com.soft.admin.upms.model.SysPermCode">
        <id column="perm_code_id" jdbcType="BIGINT" property="permCodeId"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="perm_code" jdbcType="VARCHAR" property="permCode"/>
        <result column="perm_code_type" jdbcType="INTEGER" property="permCodeType"/>
        <result column="show_name" jdbcType="VARCHAR" property="showName"/>
        <result column="show_order" jdbcType="INTEGER" property="showOrder"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="getPermCodeListByUserId" resultType="java.lang.String">
        SELECT
            pc.perm_code
        FROM
            common_sys_user_role ur,
            common_sys_role_menu rm,
            common_sys_menu_perm_code mpc,
            common_sys_perm_code pc
        <where>
            AND ur.user_id = #{userId}
            AND ur.role_id = rm.role_id
            AND rm.menu_id = mpc.menu_id
            AND mpc.perm_code_id = pc.perm_code_id
        </where>
    </select>

    <select id="getGroupPermListByUserId" resultType="java.lang.String">
        select DISTINCT perm_id from common_sys_perm_group_perm pgp
        LEFT JOIN common_sys_perm_group spg
        on pgp.perm_group_id = spg.id
        LEFT JOIN common_sys_user_perm_group supg
        on spg.id = supg.perm_group_id
        <where>
            and pgp.perm_type = #{permType}
            and supg.user_id = #{userId}
            and spg.deleted_flag = 1
        </where>
    </select>


    <select id="getAllGroupPermByUserId" resultType="map">
        SELECT
            ifnull(  sum( spg.has_all_space ) ,0) AS has_all_space,
            ifnull(  sum( spg.has_all_equipment ) ,0) AS has_all_equipment,
            ifnull(  sum( spg.has_all_workorder ) ,0) AS has_all_workorder
        FROM
            common_sys_perm_group spg
                LEFT JOIN common_sys_user_perm_group supg ON spg.id = supg.perm_group_id
        WHERE
            supg.user_id = #{userId}
          AND spg.deleted_flag = 1
    </select>



    <!-- 以下查询仅用于权限分配的问题定位，由于关联表较多，可能会给系统运行带来性能影响 -->
    <select id="getSysUserListWithDetail" resultType="map">
        SELECT
            u.user_id userId,
            u.login_name loginName,
            u.show_name showName,
            r.role_id roleId,
            r.role_name roleName,
            m.menu_id menuId,
            m.menu_name menuName,
            m.menu_type menuType
        FROM
            common_sys_menu_perm_code mpc,
            common_sys_menu m,
            common_sys_role_menu rm,
            common_sys_role r,
            common_sys_user_role ur,
            common_sys_user u
        <where>
            AND mpc.perm_code_id = #{permCodeId}
            AND mpc.menu_id = m.menu_id
            AND mpc.menu_id = rm.menu_id
            AND rm.role_id = r.role_id
            AND rm.role_id = ur.role_id
            AND ur.user_id = u.user_id
            <if test="loginName != null and loginName != ''">
                AND u.login_name = #{loginName}
            </if>
        </where>
        ORDER BY
            u.user_id, r.role_id, m.menu_id
    </select>

    <select id="getSysRoleListWithDetail" resultType="map">
        SELECT
            r.role_id roleId,
            r.role_name roleName,
            m.menu_id menuId,
            m.menu_name menuName,
            m.menu_type menuType
        FROM
            common_sys_menu_perm_code mpc,
            common_sys_menu m,
            common_sys_role_menu rm,
            common_sys_role r
        <where>
            AND mpc.perm_code_id = #{permCodeId}
            AND mpc.menu_id = m.menu_id
            AND mpc.menu_id = rm.menu_id
            AND rm.role_id = r.role_id
            <if test="roleName != null and roleName != ''">
                AND r.role_name = #{roleName}
            </if>
        </where>
        ORDER BY
            r.role_id, m.menu_id
    </select>

    <insert id="insertUserPermGroup">
        insert into
            common_sys_user_perm_group(user_id, perm_group_id)
        values
            (
            #{userId},
            #{permGroupId}
            )
    </insert>
</mapper>
