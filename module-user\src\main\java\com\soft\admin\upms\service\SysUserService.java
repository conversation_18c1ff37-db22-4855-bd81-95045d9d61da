package com.soft.admin.upms.service;

import com.alibaba.fastjson.JSONObject;
import com.soft.admin.upms.dto.OaNoPasswordLoginDTO;
import com.soft.admin.upms.dto.SysUserQueryDTO;
import com.soft.admin.upms.dto.SysUserSaveOrUpdateDTO;
import com.soft.admin.upms.model.SysUser;
import com.soft.admin.upms.vo.SysUserDetailVO;
import com.soft.admin.upms.vo.SysUserListVO;
import com.soft.common.core.base.service.IBaseService;
import com.soft.common.core.object.CallResult;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;

import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户管理数据操作服务接口。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
public interface SysUserService extends IBaseService<SysUser, Long> {

    /**
     * 获取指定登录名的用户对象。
     *
     * @param loginName 指定登录用户名。
     * @return 用户对象。
     */
    SysUser getSysUserByLoginName(String loginName);

    /**
     * 保存新增的用户对象。
     *
     * @param user          新增的用户对象。
     * @param roleIdSet     用户角色Id集合。
     * @param deptPostIdSet 部门岗位Id集合。
     * @param dataPermIdSet 数据权限Id集合。
     * @return 新增后的用户对象。
     */
    SysUser saveNew(SysUser user, Set<Long> roleIdSet, Set<Long> deptPostIdSet, Set<Long> dataPermIdSet, Set<Long> projectIdSet);

    /**
     * 更新用户对象。
     *
     * @param user          更新的用户对象。
     * @param originalUser  原有的用户对象。
     * @param roleIdSet     用户角色Id列表。
     * @param deptPostIdSet 部门岗位Id集合。
     * @param dataPermIdSet 数据权限Id集合。
     * @return 更新成功返回true，否则false。
     */
    boolean update(SysUser user, SysUser originalUser, Set<Long> roleIdSet, Set<Long> deptPostIdSet, Set<Long> dataPermIdSet, Set<Long> projectIdSet);

    /**
     * 修改用户密码。
     *
     * @param userId  用户主键Id。
     * @param newPass 新密码。
     * @return 成功返回true，否则false。
     */
    boolean changePassword(Long userId, String newPass);

    /**
     * 修改用户头像。
     *
     * @param userId       用户主键Id。
     * @param newHeadImage 新的头像信息。
     * @return 成功返回true，否则false。
     */
    boolean changeHeadImage(Long userId, String newHeadImage);

    /**
     * 删除指定数据。
     *
     * @param userId 主键Id。
     * @return 成功返回true，否则false。
     */
    boolean remove(Long userId);

    /**
     * 获取单表查询结果。由于没有关联数据查询，因此在仅仅获取单表数据的场景下，效率更高。
     * 如果需要同时获取关联数据，请移步(getSysUserListWithRelation)方法。
     *
     * @param filter  过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    List<SysUser> getSysUserList(SysUser filter, String orderBy);

    /**
     * 获取主表的查询结果，以及主表关联的字典数据和一对一从表数据，以及一对一从表的字典数据。
     * 该查询会涉及到一对一从表的关联过滤，或一对多从表的嵌套关联过滤，因此性能不如单表过滤。
     * 如果仅仅需要获取主表数据，请移步(getSysUserList)，以便获取更好的查询性能。
     *
     * @param filter  主表过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    List<SysUser> getSysUserListWithRelation(SysUser filter, String orderBy);

    /**
     * 获取指定角色的用户列表。
     *
     * @param roleId  角色主键Id。
     * @param filter  用户过滤对象。
     * @param orderBy 排序参数。
     * @return 用户列表。
     */
    List<SysUser> getSysUserListByRoleId(Long roleId, SysUser filter, String orderBy);

    /**
     * 获取不属于指定角色的用户列表。
     *
     * @param roleId  角色主键Id。
     * @param filter  用户过滤对象。
     * @param orderBy 排序参数。
     * @return 用户列表。
     */
    List<SysUser> getNotInSysUserListByRoleId(Long roleId, SysUser filter, String orderBy);

    /**
     * 获取指定数据权限的用户列表。
     *
     * @param dataPermId 数据权限主键Id。
     * @param filter     用户过滤对象。
     * @param orderBy    排序参数。
     * @return 用户列表。
     */
    List<SysUser> getSysUserListByDataPermId(Long dataPermId, SysUser filter, String orderBy);

    /**
     * 获取不属于指定数据权限的用户列表。
     *
     * @param dataPermId 数据权限主键Id。
     * @param filter     用户过滤对象。
     * @param orderBy    排序参数。
     * @return 用户列表。
     */
    List<SysUser> getNotInSysUserListByDataPermId(Long dataPermId, SysUser filter, String orderBy);

    /**
     * 获取指定部门岗位的用户列表。
     *
     * @param deptPostId 部门岗位主键Id。
     * @param filter     用户过滤对象。
     * @param orderBy    排序参数。
     * @return 用户列表。
     */
    List<SysUser> getSysUserListByDeptPostId(Long deptPostId, SysUser filter, String orderBy);

    /**
     * 获取不属于指定部门岗位的用户列表。
     *
     * @param deptPostId 部门岗位主键Id。
     * @param filter     用户过滤对象。
     * @param orderBy    排序参数。
     * @return 用户列表。
     */
    List<SysUser> getNotInSysUserListByDeptPostId(Long deptPostId, SysUser filter, String orderBy);

    /**
     * 获取指定岗位的用户列表。
     *
     * @param postId  岗位主键Id。
     * @param filter  用户过滤对象。
     * @param orderBy 排序参数。
     * @return 用户列表。
     */
    List<SysUser> getSysUserListByPostId(Long postId, SysUser filter, String orderBy);

    /**
     * 查询用户的权限资源地址列表。同时返回详细的分配路径。
     *
     * @param userId 用户Id。
     * @param url    url过滤条件。
     * @return 包含从用户到权限资源的完整权限分配路径信息的查询结果列表。
     */
    List<Map<String, Object>> getSysPermListWithDetail(Long userId, String url);

    /**
     * 查询用户的权限字列表。同时返回详细的分配路径。
     *
     * @param userId   用户Id。
     * @param permCode 权限字名称过滤条件。
     * @return 包含从用户到权限字的权限分配路径信息的查询结果列表。
     */
    List<Map<String, Object>> getSysPermCodeListWithDetail(Long userId, String permCode);

    /**
     * 查询用户的菜单列表。同时返回详细的分配路径。
     *
     * @param userId   用户Id。
     * @param menuName 菜单名称过滤条件。
     * @return 包含从用户到菜单的权限分配路径信息的查询结果列表。
     */
    List<Map<String, Object>> getSysMenuListWithDetail(Long userId, String menuName);

    /**
     * 验证用户对象关联的数据是否都合法。
     *
     * @param sysUser         当前操作的对象。
     * @param originalSysUser 原有对象。
     * @param roleIds         逗号分隔的角色Id列表字符串。
     * @param deptPostIds     逗号分隔的部门岗位Id列表字符串。
     * @param dataPermIds     逗号分隔的数据权限Id列表字符串。
     * @return 验证结果。
     */
    CallResult verifyRelatedData(
            SysUser sysUser, SysUser originalSysUser, String roleIds, String deptPostIds, String dataPermIds, String projectIds);

    /**
     * 根据oa用户id获取用户信息
     *
     * @param oaUserId
     * @return
     */
    SysUser getSysUserByOaUserId(String oaType, String oaUserId);


    /**
     * 新增或修改用户
     *
     * @param sysUserSaveOrUpdateDTO
     */
    Long saveOrUpdate(SysUserSaveOrUpdateDTO sysUserSaveOrUpdateDTO);

    /**
     * 上传头像
     *
     * @param sysUserSaveOrUpdateDTO
     */
    void updateImg(SysUserSaveOrUpdateDTO sysUserSaveOrUpdateDTO);


    /**
     * 查询人员列表
     *
     * @param sysUserQueryDTO
     * @return
     */
    MyPageData<SysUserListVO> list(SysUserQueryDTO sysUserQueryDTO);

    /**
     * 修改用户状态
     *
     * @param userId
     */
    void updateStatus(Long userId);


    /**
     * 用户详情信息
     *
     * @param userId
     * @return
     */
    SysUserDetailVO detail(Long userId);

    /**
     * 修改用户显示名称
     * @param showName
     * @return
     */
    ResponseResult<Void> changeShowName(String showName);

//    /**
//     * 上传人脸照
//     * @param params
//     */
//    void uploadFacePicture(UploadFacePictureDTO params);

    /**
    * <AUTHOR>
    * @Description
    * @Date 上午11:19 2024/9/6
    * @Param [sysUserQueryDTO]
    * @return com.soft.common.core.object.MyPageData<com.soft.admin.upms.vo.SysUserListVO>
    **/
    MyPageData<SysUserListVO> listByDeptId(SysUserQueryDTO sysUserQueryDTO);

    /**
     * 反转用户身份
     * @return
     */
    void flipManager(Long userId, Long deptId);

    /**
    * <AUTHOR>
    * @Description 登录接口
    * @Date 上午10:35 2024/9/10
    * @Param [loginName, password]
    * @return com.soft.common.core.object.ResponseResult<java.lang.String>
    **/
    ResponseResult<JSONObject> doLogin(String loginName, String password) throws Exception;

    /**
    * <AUTHOR>
    * @Description
    * @Date 上午10:54 2024/9/10
    * @Param [loginParam]
    * @return com.soft.common.core.object.ResponseResult<java.lang.String>
    **/
    ResponseResult<String> noPasswordLogin(OaNoPasswordLoginDTO loginParam);

    /**
    * <AUTHOR>
    * @Descriptionx
    * @Date 下午4:31 2024/9/10
    * @Param [aLong, newPass]
    * @return com.soft.common.core.object.ResponseResult<java.lang.Void>
    **/
    ResponseResult<Void> updatePassword(String aLong, String newPass) throws UnsupportedEncodingException;

    /**
     * 区分部门查询全部用户
     * @param sysUserQueryDTO
     * @return
     */
    MyPageData<SysUserListVO> listOfDiffDept(SysUserQueryDTO sysUserQueryDTO);

    /**
     * 修改手机号
     * @param shortPhone
     * @return
     */
    ResponseResult<Void> changeShortPhone(String shortPhone);

    /**
     * 根据终端SN查询用户
     * @param deviceSn
     * @return
     */
    SysUser selectUserBydeviceSn(String deviceSn);

}
