<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rutong.medical.admin.mapper.TestMapper">



    <select id="selectTestPage"  resultType="com.rutong.medical.admin.entity.Test">
        select * from test where 1=1
        <if test="null != test.userId and '' != test.userId">
            AND user_id = #{test.userId}
        </if>
    </select>

</mapper>
