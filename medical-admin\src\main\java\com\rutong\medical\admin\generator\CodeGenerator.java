package com.rutong.medical.admin.generator;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.fill.Column;

import java.io.File;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 代码生成器工具类 暂时不用
 * <AUTHOR>
 */
public class CodeGenerator {

    // ========== 数据库配置 ==========
    private static final String JDBC_URL = "************************************************************************************************************************************";
    private static final String JDBC_USERNAME = "root";
    private static final String JDBC_PASSWORD = "root123456";

    // ========== 项目配置 ==========
    private static final String PROJECT_PATH = System.getProperty("user.dir"); // 项目根路径
    private static final String PROJECT_NAME = "medical-admin"; // 模块名称
    private static final String AUTHOR = "chaibo"; // 作者
    private static final String PACKAGE_NAME = "com.rutong.medical.admin"; // 基础包名

    // ========== 表配置 ==========
    private static final String[] TABLE_NAMES = {"sm_test"}; // 要生成的表名(可配置多个)
    private static final String TABLE_PREFIX = "sm_"; // 表前缀

    // ========== 文件存在检查 ==========
    private static boolean checkFileExists(String entityName) {
        String lowerEntityName = entityName.substring(0, 1).toLowerCase() + entityName.substring(1);

        // 检查各层文件是否存在
        String basePath = PROJECT_PATH + "/" + PROJECT_NAME + "/src/main/java/" +
                PACKAGE_NAME.replace(".", "/") + "/";

        // 检查实体类
        String entityPath = basePath + "entity/" + lowerEntityName + "/" + entityName + ".java";
        // 检查Mapper
        String mapperPath = basePath + "mapper/" + lowerEntityName + "/" + entityName + "Mapper.java";
        // 检查Service接口
        String servicePath = basePath + "service/" + lowerEntityName + "/" + entityName + "Service.java";
        // 检查ServiceImpl
        String serviceImplPath = basePath + "service/" + lowerEntityName + "/impl/" + entityName + "ServiceImpl.java";
        // 检查Controller
        String controllerPath = basePath + "controller/" + lowerEntityName + "/" + entityName + "Controller.java";
        // 检查Mapper XML
        String xmlPath = PROJECT_PATH + "/" + PROJECT_NAME + "/src/main/resources/mapper/" +
                lowerEntityName + "/" + entityName + "Mapper.xml";

        // 如果任一文件已存在，则返回true
        return new File(entityPath).exists() ||
                new File(mapperPath).exists() ||
                new File(servicePath).exists() ||
                new File(serviceImplPath).exists() ||
                new File(controllerPath).exists() ||
                new File(xmlPath).exists();
    }

    public static void main(String[] args) {
        generateCode();
    }

    /**
     * 代码生成核心方法
     */
    private static void generateCode() {
        FastAutoGenerator.create(JDBC_URL, JDBC_USERNAME, JDBC_PASSWORD)
                // ========== 全局配置 ==========
                .globalConfig(builder -> {
                    builder.author(AUTHOR) // 设置作者
                            .enableSwagger() // 开启swagger支持
                            .dateType(DateType.ONLY_DATE) // 使用java.util.Date
                            .commentDate("yyyy/MM/dd") // 注释日期格式
                            .outputDir(PROJECT_PATH + "/" + PROJECT_NAME + "/src/main/java"); // 输出目录
                })

                // ========== 包配置 ==========
                .packageConfig(builder -> {
                    builder.parent(PACKAGE_NAME) // 父包名
                            .moduleName(null) // 不设置模块名
                            .pathInfo(Collections.singletonMap(
                                    OutputFile.xml,
                                    PROJECT_PATH + "/" + PROJECT_NAME + "/src/main/resources/mapper")); // mapper.xml路径
                })

                // ========== 策略配置 ==========
                .strategyConfig(builder -> {
                    builder
                            // 表名配置
                            .addInclude(TABLE_NAMES) // 需要生成的表
                            .addTablePrefix(TABLE_PREFIX) // 过滤表前缀

                            // 实体类配置
                            .entityBuilder()
                            .enableLombok() // 启用Lombok
                            .enableTableFieldAnnotation() // 字段注解
                            .idType(IdType.ASSIGN_ID) // 主键策略(雪花ID)
                            .addTableFills(new Column("create_time", FieldFill.INSERT)) // 自动填充创建时间

                            // Controller配置
                            .controllerBuilder()
                            .enableRestStyle() // 使用@RestController
                            .formatFileName("%sController") // 文件名格式

                            // Service配置
                            .serviceBuilder()
                            .formatServiceFileName("%sService") // 接口名格式
                            .formatServiceImplFileName("%sServiceImpl") // 实现类名格式

                            // Mapper配置
                            .mapperBuilder()
                            .enableMapperAnnotation() // 启用@Mapper注解
                            .formatMapperFileName("%sMapper"); // 文件名格式
                })

                // ========== 自定义包路径 ==========
                .injectionConfig(builder -> {
                    builder.beforeOutputFile((tableInfo, objectMap) -> {
                        // 获取实体类名称(如Device)
                        String entityName = tableInfo.getEntityName();

                        // 检查文件是否已存在
                        if (checkFileExists(entityName)) {
                            System.out.println("文件已存在，跳过生成: " + entityName);
                            throw new RuntimeException("文件已存在，跳过生成: " + entityName);
                        }

                        // 转换为首字母小写(如device)
                        String lowerEntityName = entityName.substring(0, 1).toLowerCase() + entityName.substring(1);

                        // 设置各层级的包路径
                        objectMap.put("controllerPackage", PACKAGE_NAME + ".controller." + lowerEntityName);
                        objectMap.put("servicePackage", PACKAGE_NAME + ".service." + lowerEntityName);
                        objectMap.put("serviceImplPackage", PACKAGE_NAME + ".service." + lowerEntityName + ".impl");
                        objectMap.put("mapperPackage", PACKAGE_NAME + ".mapper." + lowerEntityName);
                        objectMap.put("entityPackage", PACKAGE_NAME + ".entity." + lowerEntityName);
                    });
                })

                // ========== 模板引擎配置 ==========
                .templateEngine(new FreemarkerTemplateEngine()) // 使用Freemarker模板
                .execute();
    }

    /**
     * 处理表名字符串(兼容旧方法)
     */
    protected static List<String> getTables(String tables) {
        return "all".equals(tables) ? Collections.emptyList() : Arrays.asList(tables.split(","));
    }
}