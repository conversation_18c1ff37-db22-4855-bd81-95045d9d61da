package com.rutong.medical.admin.tool.websocket.listener;

import com.rutong.medical.admin.tool.websocket.manager.WebSocketSubscriptionManager;
import com.soft.common.core.object.TokenData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;

@Component
@Slf4j
public class WebSocketEventListener {

    @Autowired
    private WebSocketSubscriptionManager subscriptionManager;

    /**
     * 监听 WebSocket 断开事件
     */
    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        String sessionId = headerAccessor.getSessionId();
        MessageHeaders messageHeaders = headerAccessor.getMessageHeaders();
        TokenData simpUser = (TokenData)messageHeaders.get("simpUser");
        String userId = String.valueOf(simpUser.getUserId());

        log.info("WebSocket 连接断开，userId: {}", userId);

        // 取消订阅，清理资源
        subscriptionManager.removeSession(userId);
    }
}

