package com.soft.common.core.util.easyexcel;

import java.util.List;
import java.util.Map;

/**
 * @description: excel自主校验接口
 * @author: duanyashu
 * @time: 2020-07-10 09:16
 */
public interface  ExcelCheckManager<T> {

    /**
     * @description: 校验方法
     * @param map
     * @param object 读取到的数据
     * @param resultMap
     * @return
     */
    Map<Integer, String> checkImportExcel(T object, Map<Integer, String> resultMap, Long batchId);


    /**
     * @description: 校验方法
     * @param objects 读取到的数据
     * @return
     */
    ExcelCheckResult checkImportExcel(List<ExcelImportErrDto> objects, Long batchId);

    /**
     * 初始化校验参数
     */
    void checkDataInit(String businessType);
}
