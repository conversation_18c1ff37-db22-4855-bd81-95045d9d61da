package com.soft.common.core.object;

import lombok.Data;

import java.util.List;

/**
 * @ClassName TDenginePageResult
 * @Description
 * <AUTHOR>
 * @Date 2025/7/12 15:10
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Data
public class TDenginePageResult<T> {
    private Long totalCount;
    private List<T> dataList;

    public TDenginePageResult( Long total, List<T> list) {
        this.totalCount = total;
        this.dataList = list;
    }

    // getters
}
