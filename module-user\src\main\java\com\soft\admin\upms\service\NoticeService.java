package com.soft.admin.upms.service;

import com.soft.admin.upms.dto.NoticeQueryDTO;
import com.soft.admin.upms.dto.NoticeSaveDTO;
import com.soft.admin.upms.model.Notice;
import com.soft.admin.upms.vo.NoticeVO;
import com.soft.common.core.base.service.IBaseService;

import java.util.List;

/**
 * 公告通知Service接口
 *
 * <AUTHOR>
 * @date 2023-07-03
 */
public interface NoticeService extends IBaseService<Notice, Long> {

    /**
     * 数据查询
     *
     * @param queryDTO
     * @return
     */
    List<NoticeVO> queryList(NoticeQueryDTO queryDTO);

    /**
     * 保存
     *
     * @param saveDTO
     */
    void save(NoticeSaveDTO saveDTO);

    /**
     * 删除
     *
     * @param id
     * @param type
     */
    void delete(Long id, String type);

    /**
     * 发布or撤销
     *
     * @param id
     * @param status
     */
    void updateStatus(Long id, Integer status);

}
