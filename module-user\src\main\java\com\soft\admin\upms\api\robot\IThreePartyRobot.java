package com.soft.admin.upms.api.robot;

import java.util.List;

/**
 * @Description 三方聊天机器人接口
 * @Date 0024, 2023年5月24日 16:54
 * <AUTHOR>
 **/
public interface IThreePartyRobot {

    String getPlatform();

    /**
     * 推送消息
     * @param webhook webhook
     * @param secret 密钥
     * @param message 消息内容
     * @param atUserList @用户手机号
     */
    void send(String webhook, String secret, String message, List<String> atUserList);
}
