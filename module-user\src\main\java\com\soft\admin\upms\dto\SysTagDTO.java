package com.soft.admin.upms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;

import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * SpTagDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@ApiModel("SpTagDTO对象")
@Data
public class SysTagDTO {

    @ApiModelProperty(value = "标签ID")
    @NotNull(message = "数据验证失败，标签ID不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "名称")
    @NotNull(message = "数据验证失败，名称不能为空！")
    private String name;

    @ApiModelProperty(value = "编码")
//    @NotBlank(message = "数据验证失败，编码不能为空！")
    private String code;

    @ApiModelProperty(value = "标签类型 1用户标签 2设备标签")
    @NotNull(message = "数据验证失败，标签类型不能为空！")
    private Integer type;

    @ApiModelProperty(value = "备注")
    private String remark;
}
