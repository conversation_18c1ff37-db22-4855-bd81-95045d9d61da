<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.OaDeptMapper">
    <resultMap type="com.soft.admin.upms.model.OaDept" id="OaDeptResult">
        <result property="deptId" column="dept_id" />
        <result property="oaDeptId" column="oa_dept_id" />
        <result property="oaType" column="oa_type" />
    </resultMap>

    <sql id="baseColumn">
        dept_id, oa_dept_id, oa_type
    </sql>

    <sql id="selectOaDeptVo">
        select <include refid="baseColumn"/> from sp_oa_dept
    </sql>
    <insert id="batchInsert">
        insert into
            sp_oa_dept (dept_id, oa_dept_id, oa_type)
        values
            <foreach collection="list" item="item" separator=",">
                (
                 #{item.deptId},
                 #{item.oaDeptId},
                 #{item.oaType}
                )
            </foreach>
    </insert>

    <select id="selectByOaDeptIds" resultMap="OaDeptResult">
        select
            <include refid="baseColumn"/>
        from
            sp_oa_dept
        where
            oa_dept_id in
            <foreach collection="oaDeptIds" item="oaDeptId" open="(" separator="," close=")">
                #{oaDeptId}
            </foreach>
    </select>
    <select id="selectAllOaDeptIdList" resultType="java.lang.String">
        select
            oa_dept_id
        from
            sp_oa_dept
        where
            oa_type = #{oaType}
    </select>
    <select id="selectAll" resultMap="OaDeptResult">
        select
            <include refid="baseColumn"/>
        from
            sp_oa_dept
    </select>

</mapper>