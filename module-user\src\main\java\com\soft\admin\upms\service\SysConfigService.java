package com.soft.admin.upms.service;

import com.soft.admin.upms.dto.SysConfigDTO;
import com.soft.admin.upms.vo.SysConfigVO;
import com.soft.common.core.base.service.IBaseService;
import com.soft.admin.upms.model.SysConfig;

import java.util.List;

/**
 * 系统参数配置标Service接口
 * 
 * <AUTHOR>
 * @date 2023-09-14
 */
public interface SysConfigService extends IBaseService<SysConfig, Long> {

    List<SysConfig> list(SysConfig sysConfig);

    /**
     * 更新
     * @param sysConfig
     * @param originalSysDept
     * @return
     */
    boolean update(SysConfig sysConfig, SysConfig originalSysDept);

    /**
     * 获取配置值
     * @param key
     * @return
     */
    String getValue(String key);

    /**
     * 获取配置值
     * @param key
     * @return
     */
    boolean getBooleanValue(String key);

    /**
     *
     * @param sysConfigDTO
     * @return
     */
    SysConfigVO getSysConfig(SysConfigDTO sysConfigDTO);
}
