package com.soft.admin.upms.api.dingtalk.service;


import com.soft.admin.upms.dto.dintalk.OaRemindTodoDTO;

import java.util.List;

/**
 * @Description OA系统用户接口
 * @Date 0009, 2023年8月9日 9:08
 * <AUTHOR>
 **/
public interface IOaTodoApi {

    /**
     * 创建钉钉待办任务
     * @param dto
     */
    void createTodo(OaRemindTodoDTO dto);

    /**
     * 更新钉钉待办任务
     * @param busiId
     * @param busiType
     * @param titles
     * @param receiverIds
     */
    void updateTodo(Long busiId, String busiType, List<String> titles, Long receiverIds);

    /**
     * 查询用户待办列表
     * @param unionId
     */
    void queryTodo(String unionId);
}
