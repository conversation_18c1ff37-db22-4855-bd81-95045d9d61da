package com.rutong.medical.admin.tool.websocket.controller;


import com.rutong.medical.admin.tool.websocket.manager.WebSocketSubscriptionManager;
import com.soft.common.core.object.TokenData;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.stereotype.Controller;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Controller
public class WebSocketSubscriptionController {
    private final WebSocketSubscriptionManager subscriptionManager;

    public WebSocketSubscriptionController(WebSocketSubscriptionManager subscriptionManager) {
        this.subscriptionManager = subscriptionManager;
    }

    /**
     * 处理 WebSocket 订阅
     */
    @MessageMapping("/subscribePayload")
    public void subscribePayload(Map<String, Object> payload, SimpMessageHeaderAccessor headerAccessor) {
        TokenData tokenData = (TokenData) headerAccessor.getUser();
        String userId = tokenData.getUserId() + "";

        String topic = (String) payload.get("topic");   // 订阅的 WebSocket 主题
        Object rawItems = payload.get("items");

        Set<String> items;
        if (rawItems instanceof List) {
            items = new HashSet<>((List<String>) rawItems);  // 直接转换成 HashSet
        } else if (rawItems instanceof Set) {
            items = (Set<String>) rawItems;
        } else {
            throw new IllegalArgumentException("Invalid type for 'items': " + rawItems.getClass().getName());
        }// 订阅的 ID 列表（如设备、事件）
        //这里直接替换掉原有的订阅
        subscriptionManager.removeSubscription(userId, topic);
        subscriptionManager.addSubscription(userId, topic, items);
    }

    /**
     * 取消订阅
     */
    @MessageMapping("/unsubscribePayload")
    public void unsubscribePayload(Map<String, Object> payload, SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        String topic = (String) payload.get("topic");

        subscriptionManager.removeSubscription(sessionId, topic);
    }
}
