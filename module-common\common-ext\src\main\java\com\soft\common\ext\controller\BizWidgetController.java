package com.soft.common.ext.controller;

import com.alibaba.fastjson.JSONObject;
import com.soft.common.ext.util.BizWidgetDatasourceExtHelper;
import com.soft.common.core.annotation.MyRequestBody;
import com.soft.common.core.object.MyOrderParam;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.MyPageParam;
import com.soft.common.core.object.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/admin/commonext/bizwidget")
public class BizWidgetController {

    @Autowired
    private BizWidgetDatasourceExtHelper bizWidgetDatasourceExtHelper;

    @PostMapping("/list")
    public ResponseResult<MyPageData<Map<String, Object>>> list(
            @MyRequestBody(required = true) String widgetType,
            @MyRequestBody JSONObject filter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        MyPageData<Map<String, Object>> pageData =
                bizWidgetDatasourceExtHelper.getDataList(widgetType, filter, orderParam, pageParam);
        return ResponseResult.success(pageData);
    }

    @PostMapping("/view")
    public ResponseResult<List<Map<String, Object>>> view(
            @MyRequestBody(required = true) String widgetType,
            @MyRequestBody(required = true) String ids) {
        List<Map<String, Object>> dataMapList =
                bizWidgetDatasourceExtHelper.getDataListByIds(widgetType, ids);
        return ResponseResult.success(dataMapList);
    }
}
