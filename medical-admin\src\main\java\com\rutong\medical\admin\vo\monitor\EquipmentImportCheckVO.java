package com.rutong.medical.admin.vo.monitor;

import java.util.List;

import com.rutong.medical.admin.entity.monitor.DeviceMonitor;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 设备导入校验返回
 */
@Data
public class EquipmentImportCheckVO {

    @ApiModelProperty(value = "新增设备列表")
    private List<DeviceMonitor> insertEquipments;

    @ApiModelProperty(value = "更新设备列表")
    private List<DeviceMonitor> updateEquipments;

    @ApiModelProperty(value = "错误信息")
    private List<ErrorMessage> errorMessages;


    @Data
    public static class ErrorMessage {

        @ApiModelProperty(value = "Excel表格第几行")
        private int rowNo;

        @ApiModelProperty(value = "异常设备数据")
        private DeviceMonitorImportVO equipmentExcelTemplateVO;

        @ApiModelProperty(value = "错误信息")
        private List<String> errorMessages;
    }
}
