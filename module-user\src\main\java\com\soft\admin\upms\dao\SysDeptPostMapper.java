package com.soft.admin.upms.dao;

import com.soft.admin.upms.model.SysDeptPost;
import com.soft.common.core.base.dao.BaseDaoMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 部门岗位数据操作访问接口。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
public interface SysDeptPostMapper extends BaseDaoMapper<SysDeptPost> {

    /**
     * 获取指定部门Id的部门岗位多对多关联数据列表，以及关联的部门和岗位数据。
     *
     * @param deptId 部门Id。如果参数为空则返回全部数据。
     * @return 部门岗位多对多数据列表。
     */
    List<Map<String, Object>> getSysDeptPostListWithRelationByDeptId(@Param("deptId") List<Long> deptId);

    /**
     * 获取指定部门Id的领导部门岗位列表。
     *
     * @param deptId 部门Id。
     * @return 指定部门Id的领导部门岗位列表
     */
    List<SysDeptPost> getLeaderDeptPostList(@Param("deptId") Long deptId);

    /**
     * 批量插入
     * @param sysDeptPostList
     */
    void insertBatch(List<SysDeptPost> sysDeptPostList);
}
