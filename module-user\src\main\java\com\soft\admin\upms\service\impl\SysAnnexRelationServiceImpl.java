package com.soft.admin.upms.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.admin.upms.dao.SysAnnexRelationMapper;
import com.soft.admin.upms.model.SysAnnexRelation;
import com.soft.admin.upms.service.SysAnnexRelationService;
import com.soft.common.core.util.MyModelUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 业务附件关联关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-18
 */
@Service
public class SysAnnexRelationServiceImpl extends ServiceImpl<SysAnnexRelationMapper, SysAnnexRelation> implements SysAnnexRelationService {

    @Autowired
    private SysAnnexRelationMapper systemAnnexRelationMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdate(List<Long> annexIdList, Long busiId, Class clazz) {
        if(CollectionUtils.isEmpty(annexIdList) || busiId == null){
            log.error("保存附件信息出错,请检查参数");
            return;
        }

        String busiTable = MyModelUtil.mapToTableName(clazz);
        this.remove(
                new LambdaQueryWrapper<SysAnnexRelation>().eq(SysAnnexRelation::getBusiId, busiId)
                        .eq(SysAnnexRelation::getBusiTable, busiTable)
        );

        if (CollectionUtil.isNotEmpty(annexIdList)) {
            List<SysAnnexRelation> relationList = Lists.newArrayList();
            for (Long annexId : annexIdList) {
                SysAnnexRelation annexRelation = new SysAnnexRelation();
                annexRelation.setAnnexId(annexId);
                annexRelation.setBusiId(busiId);
                annexRelation.setBusiTable(busiTable);
                relationList.add(annexRelation);
            }
            this.saveBatch(relationList);
        }
    }
}
