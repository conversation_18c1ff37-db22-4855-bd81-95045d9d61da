package com.soft.common.ext.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.soft.common.ext.base.BizWidgetDatasource;
import com.soft.common.core.exception.MyRuntimeException;
import com.soft.common.core.object.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 高级通用业务组件的扩展帮助实现类。
 *
 * <AUTHOR>
 * @date 2022-03-04
 */
@Slf4j
@Component
public class BizWidgetDatasourceExtHelper {

    /**
     * 全部框架使用橙单框架，同时组件所在模块，如在线表单，报表等和业务服务位于同一服务内是使用。
     */
    private static final String DEFAULT_ORANGE_APP = "__DEFAULT_ORANGE_APP__";

    /**
     * Map的数据结构为：Map<AppId, Map<widgetDatasourceType, DatasourceWrapper>>
     */
    private Map<String, Map<String, DatasourceWrapper>> dataExtractorMap = new HashMap<>();

    /**
     * 为默认APP注册基础组件数据源对象。
     *
     * @param type       数据源类型。
     * @param datasource 业务通用组件的数据源接口。
     */
    public void registerDatasource(String type, BizWidgetDatasource datasource) {
        Assert.notBlank(type);
        Assert.notNull(datasource);
        Map<String, DatasourceWrapper> datasourceWrapperMap =
                dataExtractorMap.computeIfAbsent(DEFAULT_ORANGE_APP, k -> new HashMap<>(2));
        datasourceWrapperMap.put(type, new DatasourceWrapper(datasource));
    }

    /**
     * 根据过滤条件获取指定通用业务组件的数据列表。APP_ID使用默认的"DEFAULT_ORANGE_APP"。
     *
     * @param type       组件数据源类型。
     * @param filter     过滤参数。不同的数据源参数不同。这里我们以键值对的方式传递。
     * @param orderParam 排序参数。
     * @param pageParam  分页参数。
     * @return 查询后的分页数据列表。
     */
    public MyPageData<Map<String, Object>> getDataList(
            String type, Map<String, Object> filter, MyOrderParam orderParam, MyPageParam pageParam) {
        if (StrUtil.isBlank(type)) {
            throw new MyRuntimeException("Argument [types] can't be BLANK");
        }
        DatasourceWrapper wrapper = this.getDatasourceWrapper(DEFAULT_ORANGE_APP, type);
        return wrapper.getBizWidgetDataSource().getDataList(filter, orderParam, pageParam);
    }

    /**
     * 根据主键获取指定通用业务组件的数据对象。APP_ID使用默认的"DEFAULT_ORANGE_APP"。
     *
     * @param type 组件数据源类型。
     * @param id   主键Id。
     * @return 指定主键Id的数据对象。
     */
    public Map<String, Object> getDataById(String type, String id) {
        List<Map<String, Object>> dataList = this.getDataListByIds(type, id);
        return CollUtil.isEmpty(dataList) ? null : dataList.get(0);
    }

    /**
     * 根据主键集合获取指定通用业务组件的数据列表。APP_ID使用默认的"DEFAULT_ORANGE_APP"。
     *
     * @param type 组件数据源类型。
     * @param ids  主键Id集合。
     * @return 指定主键Id集合的数据对象列表。
     */
    public List<Map<String, Object>> getDataListByIds(String type, String ids) {
        if (StrUtil.isBlank(type)) {
            throw new MyRuntimeException("Argument [types] can't be BLANK");
        }
        if (StrUtil.isBlank(ids)) {
            throw new MyRuntimeException("Argument [ids] can't be BLANK");
        }
        DatasourceWrapper wrapper = this.getDatasourceWrapper(DEFAULT_ORANGE_APP, type);
        return wrapper.getBizWidgetDataSource().getDataListByIds(StrUtil.split(ids, ","));
    }

    private DatasourceWrapper getDatasourceWrapper(String appId, String type) {
        Map<String, DatasourceWrapper> datasourceWrapperMap = dataExtractorMap.get(appId);
        Assert.notNull(datasourceWrapperMap);
        DatasourceWrapper wrapper = datasourceWrapperMap.get(type);
        Assert.notNull(wrapper);
        return wrapper;
    }

    @NoArgsConstructor
    @Data
    public static class DatasourceWrapper {
        private BizWidgetDatasource bizWidgetDataSource;
        private String listUrl;
        private String viewUrl;

        public DatasourceWrapper(BizWidgetDatasource bizWidgetDataSource) {
            this.bizWidgetDataSource = bizWidgetDataSource;
        }
    }
}
