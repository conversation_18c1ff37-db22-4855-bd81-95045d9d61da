package com.soft.admin.upms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.admin.upms.dto.message.*;
import com.soft.admin.upms.vo.SystemMessageVO;
import com.soft.common.core.object.MyPageData;
import com.soft.admin.upms.model.message.MessageRecord;
import com.soft.admin.upms.model.message.MessageRecordContent;
import com.soft.admin.upms.vo.message.MessageRecordUnreadCountVO;
import com.soft.admin.upms.vo.message.MessageRecordVO;

import java.util.List;

/**
 * 消息中心记录Service接口
 *
 * <AUTHOR>
 * @date 2024-02-27
 */
public interface MessageRecordService extends IService<MessageRecord> {

    /**
     * 推送消息
     *
     * @param messageRecordContent
     */
    List<Long> push(MessageRecordContent messageRecordContent);

    /**
     * 我的消息列表
     *
     * @param messageRecordQueryDTO
     * @return
     */
    MyPageData<MessageRecordVO> myList(MessageRecordQueryDTO messageRecordQueryDTO);

    /**
     * 消息管理列表
     * @param messageRecordQueryDTO
     * @return
     */
    MyPageData<SystemMessageVO> managerMessageList(MessageRecordQueryDTO messageRecordQueryDTO);

    /**
     * 新增
     * @param dto
     */
    void managerMessageAdd(MessageRecordAddDTO dto);

    /**
     * 发布
     * @param dto
     */
    void managerMessageIssue(MessageRecordIssueDTO dto);

    /**
     * 阅读消息
     *
     * @param id
     */
    MessageRecordVO read(Long id);

    /**
     * 批量已读
     *
     * @param messageRecordReadDTO
     */
    void readBatch(MessageRecordReadDTO messageRecordReadDTO);


    /**
     * 未读消息数量
     *
     * @return
     */
    MessageRecordUnreadCountVO unreadCount();

    /**
     * 删除消息
     *
     * @param messageRecordDeleteDTO
     */
    void delete(MessageRecordDeleteDTO messageRecordDeleteDTO);

    /**
     * 管理详情
     * @param id
     * @return
     */
    SystemMessageVO managerMessageDetail(Long id);

    /**
     * 删除消息
     * @param id
     */
    void managerMessageDelete(Long id);
}
