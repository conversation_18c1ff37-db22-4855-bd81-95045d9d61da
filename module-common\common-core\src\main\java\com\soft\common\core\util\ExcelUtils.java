package com.soft.common.core.util;

import com.alibaba.excel.write.metadata.WriteSheet;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.DataFormatData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.excel.util.StyleUtil;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.SneakyThrows;
import org.apache.poi.hssf.usermodel.HSSFDataValidation;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.jetbrains.annotations.NotNull;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023-05-31  13:54:24
 */
public class ExcelUtils {


    /**
     * 导出 Excel文件
     *
     * @param headClazz
     * @param data
     * @param fileName
     */
    public static void export(Class<?> headClazz, Collection<?> data, String fileName) {
        export(headClazz, data, fileName, "sheet");
    }

    /**
     * 导出 Excel文件
     *
     * @param headClazz
     * @param data
     * @param fileName
     */
    public static void exportImg(Class<?> headClazz, Collection<?> data, String fileName) {
        exportImg(headClazz, data, fileName, "sheet");
    }

    /**
     * 导出 Excel 图片文件
     *
     * @param headClazz
     * @param data
     * @param fileName
     */
    public static void exportImg(Class<?> headClazz, Collection<?> data, String fileName, String sheetName) {
        HttpServletResponse httpResponse = ContextUtil.getHttpResponse();
        try {
            httpResponse.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            httpResponse.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码
            String fileNameEncode = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            httpResponse.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileNameEncode + ".xlsx");

            // 表头样式
//            HorizontalCellStyleStrategy horizontalCellStyleStrategy = getHorizontalCellStyleStrategy();

            EasyExcel.write(httpResponse.getOutputStream())
                    // 默认的文件类型
                    .excelType(ExcelTypeEnum.XLSX)
                    // 表头
                    //.head(headClazz)
                    // 自定义样式
                    //.registerWriteHandler(horizontalCellStyleStrategy)
                    // sheet 名称
                    .registerWriteHandler(new SimpleColumnWidthStyleStrategy(38)) // 简单的列宽策略，列宽20
                    .registerWriteHandler(new SimpleRowHeightStyleStrategy((short)228,(short)228)) // 简单的行高策略：头行高，内容行高
//                                        .registerWriteHandler(new SimpleColumnWidthStyleStrategy(50)) // 简单的列宽策略，列宽20
//                    .registerWriteHandler(new SimpleRowHeightStyleStrategy((short)300,(short)300)) // 简单的行高策略：头行高，内容行高
                    .sheet(sheetName)
                    .doWrite(data);
        } catch (Exception e) {
            e.printStackTrace();
            // 重置response
            httpResponse.reset();
            httpResponse.setContentType("application/json");
            httpResponse.setCharacterEncoding("utf-8");
            Map<String, Object> map = MapUtils.newHashMap();
            map.put("success", false);
            map.put("errorMessage", "文件下载失败" + e.getMessage());
            try {
                httpResponse.getWriter().println(JSON.toJSONString(map));
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            }
        }
    }

    @SneakyThrows
    public static void multipleExport(List<Class<?>> headClazz, List<Collection<?>> data, List<String> sheetName, List<WriteHandler> writeHandlerList,int rowIndex, String fileName) {
        HttpServletResponse httpResponse = ContextUtil.getHttpResponse();
        httpResponse.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        httpResponse.setCharacterEncoding("utf-8");
        String fileNameEncode = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        httpResponse.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileNameEncode + ".xlsx");

        ExcelWriter excelWriter = EasyExcel.write(httpResponse.getOutputStream(), headClazz.get(0))
                .excelType(ExcelTypeEnum.XLSX)
//                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(20)) // 设置列宽策略
//                .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 45, (short) 45)) // 设置行高策略
                .build();

        if (sheetName.size() != data.size() || headClazz.size() != data.size()) {
            throw new IllegalArgumentException("The size of sheetName, headClazz, and data must be the same.");
        }

        for (int i = 0; i < sheetName.size(); i++) {
            WriteSheet writeSheet = EasyExcel.writerSheet(i, sheetName.get(i))
                    .head(headClazz.get(i))
                    .registerWriteHandler(writeHandlerList.get(i))
                    // 自定义处理
                    .relativeHeadRowIndex(rowIndex)
                    .build();
            excelWriter.write(data.get(i), writeSheet);
        }

        excelWriter.finish();
    }

    /**
     * 导出 Excel文件
     *
     * @param headClazz
     * @param data
     * @param fileName
     */
    public static void export(Class<?> headClazz, Collection<?> data, String fileName, String sheetName) {
        HttpServletResponse httpResponse = ContextUtil.getHttpResponse();
        try {
            httpResponse.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            httpResponse.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码
            String fileNameEncode = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            httpResponse.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileNameEncode + ".xlsx");

            // 表头样式
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = getHorizontalCellStyleStrategy();

            EasyExcel.write(httpResponse.getOutputStream())
                    // 默认的文件类型
                    .excelType(ExcelTypeEnum.XLSX)
                    // 表头
                    .head(headClazz)
                    // 自定义样式
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    // sheet 名称
                    .sheet(sheetName)
                    .doWrite(data);
        } catch (Exception e) {
            e.printStackTrace();
            // 重置response
            httpResponse.reset();
            httpResponse.setContentType("application/json");
            httpResponse.setCharacterEncoding("utf-8");
            Map<String, Object> map = MapUtils.newHashMap();
            map.put("success", false);
            map.put("errorMessage", "文件下载失败" + e.getMessage());
            try {
                httpResponse.getWriter().println(JSON.toJSONString(map));
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            }
        }
    }


    /**
     * 导出 Excel文件，自定义处理
     *
     * @param headClazz
     * @param data
     * @param fileName
     */
    public static void exportWriteHandler(Class<?> headClazz, Collection<?> data, String fileName, String sheetName, int headRowIndex, WriteHandler... writeHandlers) {
        HttpServletResponse httpResponse = ContextUtil.getHttpResponse();
        try {
            httpResponse.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            httpResponse.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码
            String fileNameEncode = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            httpResponse.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileNameEncode + ".xlsx");

            HorizontalCellStyleStrategy horizontalCellStyleStrategy = getHorizontalCellStyleStrategy();

            ExcelWriterSheetBuilder excelWriterSheetBuilder = EasyExcel.write(httpResponse.getOutputStream())
                    // 默认的文件类型
                    .excelType(ExcelTypeEnum.XLSX)
                    // 表头
                    .head(headClazz)
                    // 自定义样式
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    // 自定义处理
                    .relativeHeadRowIndex(headRowIndex)
                    // sheet 名称
                    .sheet(sheetName);
            for (WriteHandler writeHandler : writeHandlers) {
                excelWriterSheetBuilder.registerWriteHandler(writeHandler);
            }
            excelWriterSheetBuilder.doWrite(data);
        } catch (Exception e) {
            e.printStackTrace();
            // 重置response
            httpResponse.reset();
            httpResponse.setContentType("application/json");
            httpResponse.setCharacterEncoding("utf-8");
            Map<String, Object> map = MapUtils.newHashMap();
            map.put("success", false);
            map.put("errorMessage", "文件下载失败" + e.getMessage());
            try {
                httpResponse.getWriter().println(JSON.toJSONString(map));
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            }
        }
    }


    public static void exportPresetHandler(Class<?> headClazz, Collection<?> data, String fileName, String sheetName, ExcelTitle excelTitle, ExcelSelectOptions excelSelectOptions) {
        exportWriteHandler(headClazz, data, fileName, sheetName, excelTitle.getHeadRowIndex(), new CellWriteHandler() {
            @Override
            public void afterCellDispose(CellWriteHandlerContext context) {
                WriteCellData<?> cellData = context.getFirstCellData();
                // 单元格样式
                WriteCellStyle writeCellStyle = cellData.getOrCreateStyle();

                // 获取 Sheet和 Workbook 实例
                WriteSheetHolder writeSheetHolder = context.getWriteSheetHolder();
                Sheet sheet = writeSheetHolder.getSheet();
                Workbook workbook = writeSheetHolder.getSheet().getWorkbook();

                // 获取单元格对象
                Cell cell = context.getCell();
                if (context.getHead()) {
                    // 设置单元格为文本格式
                    DataFormatData dataFormatData = new DataFormatData();
                    dataFormatData.setIndex((short) 49);
                    writeCellStyle.setDataFormatData(dataFormatData);
//                    // 获取单元格值
//                    String cellValue = cell.getStringCellValue();
//                    if (StrUtil.isNotBlank(cellValue) && cellValue.contains("*")) {
//                        // 设置标题字体样式
//                        WriteFont headWriteFont = new WriteFont();
//                        // 加粗
//                        headWriteFont.setBold(true);
//                        // 设置字体颜色
//                        headWriteFont.setColor(Font.COLOR_RED);
//                        writeCellStyle.setWriteFont(headWriteFont);
//                        CellStyle cellStyle = StyleUtil.buildCellStyle(workbook, null, writeCellStyle);
//                        cell.setCellStyle(cellStyle);
//                    }

                    // 设置批注内容
                    // 创建绘图对象
//                    Drawing<?> drawing = sheet.createDrawingPatriarch();
//                    Comment comment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) cell.getColumnIndex(), 0, (short) 5, 5));
//                    comment.setString(new XSSFRichTextString("批注内容"));
//                    cell.setCellComment(comment);
                } else {
                    if (excelTitle.dataIsExample && CollectionUtil.isNotEmpty(data)) {
                        // 设置标题字体样式
                        WriteFont headWriteFont = new WriteFont();
                        // 加粗
                        headWriteFont.setBold(true);
                        // 设置字体颜色
                        headWriteFont.setColor(Font.COLOR_RED);
                        writeCellStyle.setWriteFont(headWriteFont);
                        CellStyle cellStyle = StyleUtil.buildCellStyle(workbook, null, writeCellStyle);
                        cell.setCellStyle(cellStyle);
                    }
                    // 日期时间格式字段，设置格式化
                    Head headData = context.getHeadData();
                    // 获取字段
                    Field field = headData.getField();
                    // 获取指定的注解
                    DateTimeFormat dateTimeFormat = field.getAnnotation(DateTimeFormat.class);
                    if (dateTimeFormat != null) {
                        // 获取注解内容
                        String format = dateTimeFormat.value();
                        // 设置格式化
                        DataFormatData dataFormatData = new DataFormatData();
                        dataFormatData.setFormat(format);
                        writeCellStyle.setDataFormatData(dataFormatData);

                        // 获取字段类型
                        Class<?> type = field.getType();
                        // 根据不同的时间类型，格式化成字符串后，再设值
                        if (type == Date.class) {
                            Date dateCellValue = cell.getDateCellValue();
                            cell.setCellValue(DateUtil.format(dateCellValue, format));
                        } else if (type == LocalDateTime.class) {
                            LocalDateTime localDateTimeCellValue = cell.getLocalDateTimeCellValue();
                            cell.setCellValue(DateUtil.format(localDateTimeCellValue, format));
                        }
                    }
                }
            }
        }, new SheetWriteHandler() {
            @Override
            public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
                Workbook workbook = writeWorkbookHolder.getWorkbook();
                Sheet sheet = workbook.getSheetAt(0);
                //设置标题
                if (StrUtil.isNotBlank(excelTitle.getTitle())) {
                    Row row = sheet.createRow(excelTitle.getRow());
                    row.setHeight(excelTitle.height);
                    Cell cell1 = row.createCell(0);
                    cell1.setCellValue(excelTitle.getTitle());
                    CellStyle cellStyle = workbook.createCellStyle();
                    cellStyle.setVerticalAlignment(VerticalAlignment.JUSTIFY);
                    cellStyle.setAlignment(HorizontalAlignment.LEFT);
                    Font font = workbook.createFont();
                    font.setBold(excelTitle.isBold());
                    font.setFontHeight(excelTitle.getFontHeight());
                    cellStyle.setFont(font);
                    cell1.setCellStyle(cellStyle);
                    sheet.addMergedRegionUnsafe(excelTitle.getCellRange());
                }

                // 设置下拉框
                // 获取sheet页的数据校验对象
                DataValidationHelper helper = sheet.getDataValidationHelper();

                if (excelSelectOptions != null && CollectionUtil.isNotEmpty(excelSelectOptions.getSelectOptions())) {
                    int firstRow = excelTitle.getHeadRowIndex() + 1;
                    // k 为存在下拉数据集的单元格下表， v为下拉数据集
                    AtomicInteger index = new AtomicInteger(1);
                    excelSelectOptions.getSelectOptions().forEach((colIndex, select) -> {
                        // 设置下拉列表覆盖的行数，从第一行开始到最后一行，这里注意，Excel行的
                        // 索引是从0开始的，我这边第0行是标题行，第1行开始时数据化，可根据实
                        // 际业务设置真正的数据开始行，如果要设置到最后一行，那么一定注意，
                        // 最后一行的行索引是1048575，千万别写成1048576，不然会导致下拉列表失效，出不来
                        CellRangeAddressList infoList = new CellRangeAddressList(firstRow, 1048575, colIndex, colIndex);

                        ExcelSelectOptions.SelectType selectType = select.getSelectType();
                        String[] selectOptions = select.getSelectOptions();

                        // 设置存放下拉数据的字典sheet，并把这些sheet隐藏掉，这样用户交互更友好
                        String dictSheetName = "dict_hide_sheet" + index;
                        Sheet dictSheet = workbook.createSheet(dictSheetName);
                        // 隐藏字典sheet页
                        workbook.setSheetHidden(index.getAndIncrement(), true);

                        int rowLen = selectOptions.length;
                        for (int i = 0; i < rowLen; i++) {
                            // 向字典sheet写数据，从第一行开始写，此处可根据自己业务需要，自定
                            // 义从第几行还是写，写的时候注意一下行索引是从0开始的即可
                            dictSheet.createRow(i).createCell(0).setCellValue(selectOptions[i]);
                        }
                        // 将上面设置好的下拉列表字典sheet页和目标sheet关联起来
                        DataValidationConstraint constraint = helper.createFormulaListConstraint(dictSheetName);
                        // 设置关联数据公式，这个格式跟Excel设置有效性数据的表达式是一样的
                        String refers = dictSheetName + "!$A$1:$A$" + selectOptions.length;
                        Name name = workbook.createName();
                        name.setNameName(dictSheetName);
                        // 将关联公式和sheet页做关联
                        name.setRefersToFormula(refers);

                        DataValidation dataValidation = helper.createValidation(constraint, infoList);
                        if (dataValidation instanceof HSSFDataValidation) {
                            dataValidation.setSuppressDropDownArrow(false);
                        } else {
                            dataValidation.setSuppressDropDownArrow(true);
                            dataValidation.setShowErrorBox(true);
                        }
                        dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
                        dataValidation.createErrorBox("提示", "请选择下拉选项");
                        sheet.addValidationData(dataValidation);


                        // 下拉列表约束数据，下拉选项的数量小于 256 个可以使用该方式，否则使用上面方式
//                    DataValidationConstraint constraint = helper.createExplicitListConstraint(content);
//                    // 设置下拉单元格的首行， 末行，首列， 末列
//                    CellRangeAddressList rangeList = new CellRangeAddressList(2, 65536, colIndex, colIndex);
//                    // 设置约束
//                    DataValidation validation = helper.createValidation(constraint, rangeList);
//                    if (validation instanceof HSSFDataValidation) {
//                        validation.setSuppressDropDownArrow(false);
//                    } else {
//                        validation.setSuppressDropDownArrow(true);
//                        validation.setShowErrorBox(true);
//                    }
//                    validation.setErrorStyle(DataValidation.ErrorStyle.STOP);
//                    validation.createErrorBox("提示","请选择下拉选项");
//                    sheet.addValidationData(validation);
                    });
                }
            }
        });
    }

    /**
     * 导出 Excel文件，自定义处理
     *
     * @param fileName
     */
    public static void exportWriteHandler(List<List<String>> head, String fileName, String sheetName, int headRowIndex, WriteHandler writeHandler) {
        HttpServletResponse httpResponse = ContextUtil.getHttpResponse();
        try {
            httpResponse.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            httpResponse.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码
            String fileNameEncode = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            httpResponse.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileNameEncode + ".xlsx");


            HorizontalCellStyleStrategy horizontalCellStyleStrategy = getHorizontalCellStyleStrategy();

            // 设置列宽的策略
            // WriteHandler writeHandler2 = new AbstractColumnWidthStyleStrategy() {
            //     @Override
            //     protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
            //         for (int i = 4; i < 50; i++) {
            //             writeSheetHolder.getSheet().setColumnWidth(i, 1200);
            //         }
            //     }
            // };

            EasyExcel.write(httpResponse.getOutputStream())
                    // 默认的文件类型
                    .excelType(ExcelTypeEnum.XLSX)
                    // 表头
                    .head(head)
                    // 自定义样式
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    // 自定义处理
                    .registerWriteHandler(writeHandler)
                    // .registerWriteHandler(writeHandler2)
                    .relativeHeadRowIndex(headRowIndex)
                    // sheet 名称
                    .sheet(sheetName)
                    .doWrite(Lists.newArrayList());
        } catch (Exception e) {
            e.printStackTrace();
            // 重置response
            httpResponse.reset();
            httpResponse.setContentType("application/json");
            httpResponse.setCharacterEncoding("utf-8");
            Map<String, Object> map = MapUtils.newHashMap();
            map.put("success", false);
            map.put("errorMessage", "文件下载失败" + e.getMessage());
            try {
                httpResponse.getWriter().println(JSON.toJSONString(map));
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            }
        }
    }

    @NotNull
    public static HorizontalCellStyleStrategy getHorizontalCellStyleStrategy() {
        // 表头样式
        WriteCellStyle headerWriteCellStyle = new WriteCellStyle();
        // 垂直居中,水平居中
        headerWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headerWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        headerWriteCellStyle.setBorderTop(BorderStyle.THIN);
        headerWriteCellStyle.setBorderRight(BorderStyle.THIN);
        headerWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        // 设置 自动换行
        headerWriteCellStyle.setWrapped(true);
        // 字体策略
        WriteFont headerWriteFont = new WriteFont();
        // 字体大小
        headerWriteFont.setFontHeightInPoints((short) 12);
        headerWriteFont.setFontName("黑体");
        headerWriteCellStyle.setWriteFont(headerWriteFont);

        // 内容样式策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 垂直居中,水平居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        // 设置 自动换行
        contentWriteCellStyle.setWrapped(true);
        // 字体策略
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 10);
        contentWriteFont.setFontName("楷体");
        contentWriteCellStyle.setWriteFont(contentWriteFont);

        // 自定义样式策略
        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headerWriteCellStyle, contentWriteCellStyle);
        return horizontalCellStyleStrategy;
    }


    @Data
    public static class ExcelTitle {

        /**
         * 标题行
         */
        private int row = 0;

        /**
         * 标题行高
         */
        private short height = 800;

        /**
         * 标题内容
         */
        private String title;

        /**
         * 标题内容是否加粗
         */
        private boolean bold = true;

        /**
         * 标题内容字体高度
         */
        private short fontHeight = 200;

        /**
         * 单元格参数
         */
        private CellRangeAddress cellRange;

        /**
         * 表头行，如果没有标题，则表头在第一行（0），否则需要排在标题之后
         */
        private int headRowIndex = 0;

        /**
         * 数据集是否是示例：如果是示例，则会将内容标红
         */
        private boolean dataIsExample = false;
    }


//    /**
//     * 使用示例：文件读取
//     */
//    private void testRead(MultipartFile file) throws IOException {
//        List<Object> propertyDormitoryTemplateVOS = EasyExcelFactory.read(file.getInputStream())
//                .headRowNumber(2)               //  读取数据开始的行，索引从0开始，表示第一行；2表示第三行
//                .head(Object.class)             //  指定模板类
//                .sheet()                        //  指定读取的 sheet
//                .doReadSync();                  //  执行读取
//    }


//    /**
//     * 使用示例：导出文件
//     */
//    private void testWrite() {
//        // 第一步：创建 ExcelSelectOptions 对象
//        ExcelSelectOptions excelSelectOptions = ExcelSelectOptions.createSelectOptions();
//
//        // 第二步：为每个下拉选设置所在列位置（索引从0开始，表示第一列）和下拉选项内容
//        // 选项 1
//        String[] select1 = {};
//        excelSelectOptions.put(2, select1);
//
//        // 选项 2
//        String[] select2 = {};
//        excelSelectOptions.put(3, select2);
//
//        /**
//         * 参数1（headClazz）：导出的模板类，类中通过注解 excel
//         *      @ColumnWidth(value = 15)    定义字段（单元格）宽度
//         *      @HeadRowHeight(25)          定义表头行高度
//         *      @ContentRowHeight(20)       定义内容行高度
//         *      @ExcelIgnoreUnannotated     定义忽略导出类中未使用注解的字段
//         *      @ExcelProperty              定义字段对应表格名称
//         * 参数2（data）：导出的数据
//         * 参数3（fileName）：模板文件名
//         * 参数4（sheetName）：sheet名称
//         * 参数5（headRowIndex）：表格头所在行（索引从0开始，0表示第一行）
//         * 参数6：自定义写出处理
//         */
//        ExcelUtils.exportWriteHandler(Objects.class, new ArrayList<>(), "设备导入模板", "sheet", 1, new SheetWriteHandler() {
//            @Override
//            public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
////                        Workbook workbook = writeWorkbookHolder.getWorkbook();
////
////                        // 第三步：获取第一个 sheet
////                        Sheet sheet = workbook.getSheetAt(0);
////
////                        // 第四步：设置标题（可以不设置标题内容）
////                        // 设置标题说明，索引从 0开始，表示第一行
////                        Row row = sheet.createRow(0);
////                        // 行高
////                        row.setHeight((short) 5200);
////                        // 第几个单元格，从 0 开始，表示第一个
////                        Cell cell1 = row.createCell(0);
////                        // 标题说明内容
////                        cell1.setCellValue("填写须知：\n" +
////                                "1、不能对表头信息进行增加、删除、修改，否则无法正确匹配数据\n" +
////                                "  ①设备编号：系统自动生成，不能修改\n" +
////                                "  ②设备名称：1至30字符；不能包含 ' / \\ : * ? \" < > |  & % 等特殊字符\n" +
////                                "  ③设备类别：下拉选项，单选，选项来自系统数据\n" +
////                                "  ④设备型号：填写时请严格按照硬件上对应的型号信息进行录入\n" +
////                                "  ⑤安装位置：下拉选项，单选，选项来自系统数据\n" +
////                                "  ⑥标签：下拉选项，多选，用“,”隔开，选项来自系统数据\n" +
////                                "  ⑦生产厂商：填写设备对应的生产厂商信息\n" +
////                                "  ⑧保修开始日期：年月日\n" +
////                                "  ⑨保修截止日期：年月日，且日期晚于保修开始日期\n" +
////                                "  ⑩管理部门：下拉选项，单选，选项来自系统数据\n" +
////                                "2、*为必填项");
////                        // 定义单元格样式
////                        CellStyle cellStyle = workbook.createCellStyle();
////                        cellStyle.setVerticalAlignment(VerticalAlignment.JUSTIFY);
////                        cellStyle.setAlignment(HorizontalAlignment.LEFT);
////                        Font font = workbook.createFont();
////                        font.setBold(true);
////                        font.setFontHeight((short) 200);
////                        cellStyle.setFont(font);
////                        cell1.setCellStyle(cellStyle);
////                        // 单元格合并
////                        sheet.addMergedRegionUnsafe(new CellRangeAddress(0, 0, 0, 14));
////
////
////                        // 第四步：设置下拉框（方式一：建议使用）
////                        // 获取sheet页的数据校验对象
////                        DataValidationHelper helper = sheet.getDataValidationHelper();
////
////                        // 用于创建隐藏 sheet，存放下拉选项内容
////                        AtomicInteger index = new AtomicInteger(1);
////                        // k 为存在下拉数据集的单元格下表， v为下拉数据集
////                        excelSelectOptions.getSelectOptions().forEach((colIndex, select) -> {
////                            // 设置下拉列表覆盖的行数，从第一行开始到最后一行，这里注意，Excel行的索引是从0开始的；
////                            // 第0行是标题行，第1行是表格头，第2行才开始数据化，可根据实际业务设置真正的数据开始行；
////                            // 如果要设置到最后一行，那么一定注意，最后一行的行索引是1048575，千万别写成1048576，不然会导致下拉列表失效
////                            CellRangeAddressList infoList = new CellRangeAddressList(2, 1048575, colIndex, colIndex);
////
////                            // 设置单选或多选
//////                            select.setSelectType(ExcelSelectOptions.SelectType.MULTIPLE);
////                            String[] selectOptions = select.getSelectOptions();
////
////                            // 设置存放下拉数据的字典sheet，并把这些sheet隐藏掉，这样用户交互更友好
////                            String dictSheetName = "dict_hide_sheet" + index;
////                            Sheet dictSheet = workbook.createSheet(dictSheetName);
////                            // 隐藏字典sheet页
////                            workbook.setSheetHidden(index.getAndIncrement(), true);
////
////                            int rowLen = selectOptions.length;
////                            for (int i = 0; i < rowLen; i++) {
////                                // 向字典sheet写数据，从第一行开始写，此处可根据自己业务需要，自定
////                                // 义从第几行还是写，写的时候注意一下行索引是从0开始的即可
////                                dictSheet.createRow(i).createCell(0).setCellValue(selectOptions[i]);
////                            }
////                            // 将上面设置好的下拉列表字典sheet页和目标sheet关联起来
////                            DataValidationConstraint constraint = helper.createFormulaListConstraint(dictSheetName);
////                            // 设置关联数据公式，这个格式跟Excel设置有效性数据的表达式是一样的
////                            String refers = dictSheetName + "!$A$1:$A$" + selectOptions.length;
////                            Name name = workbook.createName();
////                            name.setNameName(dictSheetName);
////                            // 将关联公式和sheet页做关联
////                            name.setRefersToFormula(refers);
////
////                            DataValidation dataValidation = helper.createValidation(constraint, infoList);
////                            if (dataValidation instanceof HSSFDataValidation) {
////                                dataValidation.setSuppressDropDownArrow(false);
////                            } else {
////                                dataValidation.setSuppressDropDownArrow(true);
////                                dataValidation.setShowErrorBox(true);
////                            }
////                            dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
////                            dataValidation.createErrorBox("提示", "请选择下拉选项");
////                            sheet.addValidationData(dataValidation);
//
//
//                // 第四步：设置下拉框（下拉列表约束数据，下拉选项的数量小于 256 个可以使用该方式，否则使用上面方式）
////                            DataValidationConstraint constraint = helper.createExplicitListConstraint(content);
////                            // 设置下拉单元格的首行， 末行，首列， 末列
////                            CellRangeAddressList rangeList = new CellRangeAddressList(2, 65536, colIndex, colIndex);
////                            // 设置约束
////                            DataValidation validation = helper.createValidation(constraint, rangeList);
////                            if (validation instanceof HSSFDataValidation) {
////                                validation.setSuppressDropDownArrow(false);
////                            } else {
////                                validation.setSuppressDropDownArrow(true);
////                                validation.setShowErrorBox(true);
////                            }
////                            validation.setErrorStyle(DataValidation.ErrorStyle.STOP);
////                            validation.createErrorBox("提示", "请选择下拉选项");
////                            sheet.addValidationData(validation);
////                        });
////                    }
////                }
////        );
//            }
//        }
//    }validation
}
