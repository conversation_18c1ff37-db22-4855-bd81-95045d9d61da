package com.soft.admin.upms.service.impl;

import com.soft.admin.upms.service.SysPermissionService;
import com.soft.common.core.object.TokenData;
import com.soft.admin.upms.dao.SysMenuMapper;
import com.soft.admin.upms.dao.SysRoleMapper;
import com.soft.admin.upms.model.SysMenu;
import org.apache.commons.compress.utils.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description 权限Service实现层
 * @Date 0021, 2023年3月21日 10:36
 * <AUTHOR>
 **/
@Service
public class SysPermissionServiceImpl implements SysPermissionService {

    @Resource
    private SysRoleMapper sysRoleMapper;
    @Resource
    private SysMenuMapper sysMenuMapper;


    @Override
    public Set<String> getMenuPermission(TokenData tokenData) {
        if(tokenData.getIsAdmin()) {
            return Sets.newHashSet("*:*:*");
        }
        String[] roleIds = StringUtils.split(tokenData.getRoleIds(), ",");
        List<SysMenu> sysMenus = sysMenuMapper.getMenuListByUserId(tokenData.getUserId(), null);
        return sysMenus.stream().map(SysMenu::getPerms).collect(Collectors.toSet());
    }
}
