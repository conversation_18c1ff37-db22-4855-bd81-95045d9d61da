package com.rutong.medical.admin.service.location;

import com.rutong.medical.admin.dto.station.BaseStationDataDTO;
import com.rutong.medical.admin.entity.location.UserLocation;
import com.rutong.medical.admin.vo.location.UserLocationVO;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

/**
 * @ClassName LocationService
 * @Description 人员定位
 * <AUTHOR>
 * @Date 2025/7/15 10:27
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
public interface UserLocationService {

    /**
     * 保存人员定位信息
     * @return
     */
    Boolean save(BaseStationDataDTO baseStationDataDTO) throws InvocationTargetException, IllegalAccessException;

    /**
     * 单个用户订阅
     * @param userId
     * @return
     */
    Boolean subscriptionSingle(Long userId);


    List<UserLocationVO> getSecurityAll();

}
