package com.soft.common.mqtt;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.soft.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;

/**
 * @Description MQTT客户端包装类
 * @Date 0023, 2023年3月23日 9:36
 * <AUTHOR>
 **/
@Slf4j
public class MQTTClientWrapper {

    private MQTTProperties properties;
    private MqttClient mqttClient;

    public MQTTClientWrapper(MQTTProperties properties) {
        this.properties = properties;
        try {
            this.mqttClient = new MqttClient(properties.getHostUrl(), properties.getClientId(), new MemoryPersistence());
        } catch (MqttException e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public Boolean isConnected() {
        return mqttClient.isConnected();
    }

    public void disConnect() {
        if (mqttClient.isConnected()) {
            log.info("mqtt disconnecting ...");
            try {
                mqttClient.disconnect();
            } catch (MqttException e) {
                throw new RuntimeException(e);
            }
        }
    }


    /**
     * 连接服务端
     */
    public void connect() {
        try {
            if (mqttClient.isConnected()) {
                log.info("mqtt client already connected, disconnecting first...");
                mqttClient.disconnect();
            }
            MqttConnectOptions connectOptions = new MqttConnectOptions();
            String username = properties.getUsername();
            if (username!=null && !username.isEmpty()) {
                connectOptions.setUserName(username);
            }
            String password = properties.getPassword();
            if (password!=null && !password.isEmpty()) {
                connectOptions.setPassword(password.toCharArray());
            }
            connectOptions.setConnectionTimeout(properties.getTimeout());
            connectOptions.setKeepAliveInterval(properties.getKeepAlive());
            connectOptions.setAutomaticReconnect(properties.getReconnect());
            connectOptions.setCleanSession(properties.getCleanSession());
            mqttClient.connect(connectOptions);
            log.info("创建mqtt连接完成：{},{}", properties.getHostUrl(), properties.getClientId());
        } catch (MqttException e) {
            log.error("mqtt connect :{}", JSONObject.toJSONString(properties));
            log.error(e.getMessage(),e);
        }
    }

    /**
     * 发布消息
     * @param topic
     * @param data
     */
    public void send(String topic, Object data) {
        if (!mqttClient.isConnected()) {
            throw new ServiceException("mqtt client is disconnect, must connect before use");
        }
        // 获取客户端实例
        ObjectMapper mapper = new ObjectMapper();
        try {
            // 转换消息为json字符串
            String json = mapper.writeValueAsString(data);
            mqttClient.publish(topic, new MqttMessage(json.getBytes(StandardCharsets.UTF_8)));
        } catch (JsonProcessingException e) {
            log.error(String.format("MQTT: 主题[%s]发送消息转换json失败", topic));
        } catch (MqttException e) {
            log.error(String.format("MQTT: 主题[%s]发送消息失败", topic));
        }
    }

    /**
     * 订阅主题
     * @param listener 消息监听处理器
     */
    private void subscribe(MQTTTopicMessageListener listener) {
        if (!mqttClient.isConnected()) {
            log.error("mqtt client is disconnect, must connect before use");
        }
        String topic = listener.getTopic();
        try {
            if(Objects.isNull(listener.getQos())) {
                mqttClient.subscribe(topic, listener);
            }else {
                mqttClient.subscribe(topic, listener.getQos(), listener);
            }
        } catch (MqttException e) {
            log.error(String.format("MQTT: 订阅主题[%s]失败", topic));
            log.error(e.getMessage(),e);
        }
    }

    /**
     * 订阅主题
     * @param mqttTopicMessageListenerMap
     */
    public void subscribes(Map<String, MQTTTopicMessageListener> mqttTopicMessageListenerMap) {
        mqttClient.setCallback(new SharedSubCallbackRouter(mqttTopicMessageListenerMap));
        mqttTopicMessageListenerMap.values().forEach(this::subscribe);
    }
}
