package com.soft.common.online.exception;

import com.soft.common.core.exception.MyRuntimeException;

/**
 * 在线表单运行时异常。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
public class OnlineRuntimeException extends MyRuntimeException {

    /**
     * 构造函数。
     */
    public OnlineRuntimeException() {

    }

    /**
     * 构造函数。
     *
     * @param msg 错误信息。
     */
    public OnlineRuntimeException(String msg) {
        super(msg);
    }
}
