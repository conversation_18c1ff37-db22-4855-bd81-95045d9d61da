<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.SysDeptPostMapper">
    <resultMap id="BaseResultMap" type="com.soft.admin.upms.model.SysDeptPost">
        <id column="dept_post_id" jdbcType="BIGINT" property="deptPostId"/>
        <result column="dept_id" jdbcType="BIGINT" property="deptId"/>
        <result column="post_id" jdbcType="BIGINT" property="postId"/>
        <result column="post_show_name" jdbcType="VARCHAR" property="postShowName"/>
    </resultMap>
    <insert id="insertBatch">
        insert into common_sys_dept_post(dept_post_id, dept_id, post_id, post_show_name)
        values
        <foreach collection="list" item="deptPost" separator=",">
            (#{deptPost.deptPostId}, #{deptPost.deptId}, #{deptPost.postId}, #{deptPost.postShowName})
        </foreach>
    </insert>

    <select id="getSysDeptPostListWithRelationByDeptId" resultType="map">
        SELECT
        a.dept_post_id deptPostId,
        a.dept_id deptId,
        a.post_id postId,
        a.post_show_name postShowName,
        b.dept_name deptName,
        c.post_level postLevel,
        c.leader_post leaderPost
        FROM
        common_sys_dept_post a,
        common_sys_dept b,
        common_sys_post c
        <where>
            a.dept_id = b.dept_id
            AND a.post_id = c.post_id

            <if test="deptId != null and deptId.size > 0">
                and a.dept_id in
                <foreach collection="deptId" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getLeaderDeptPostList" resultMap="BaseResultMap">
        SELECT
            a.*
        FROM
            common_sys_dept_post a,
            common_sys_post b
        WHERE
            a.post_id = b.post_id
            AND b.leader_post = 1
            AND a.dept_id = #{deptId}
        ORDER BY
            b.post_level
    </select>
</mapper>
