package com.soft.admin.upms.dto.message;

import com.soft.admin.upms.enums.MessageRecordTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;


@Data
public class MessageRecordAddDTO {
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty("内容")
    private String content;
    @ApiModelProperty(value = "是否全员发送 1全员 0部分人员")
    private Integer allUser;
    @ApiModelProperty("接收人")
    private List<Long> receiveList;
}
