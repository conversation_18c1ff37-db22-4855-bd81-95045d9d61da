package com.soft.admin.upms.api.dingtalk.service.impl;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiAuthScopesRequest;
import com.dingtalk.api.request.OapiGettokenRequest;
import com.dingtalk.api.response.OapiAuthScopesResponse;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.soft.admin.upms.api.dingtalk.DingTalkConfig;
import com.soft.admin.upms.api.dingtalk.DingTalkConstants;
import com.soft.admin.upms.api.dingtalk.service.IOaOauth2Api;
import com.soft.admin.upms.dto.dintalk.OaAuthScopeDTO;
import com.soft.common.core.exception.ServiceException;
import com.soft.common.core.util.StringUtils;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.soft.admin.upms.api.dingtalk.DingTalkConstants.GET;

/**
 * @Description 钉钉Oauth2 Api
 * @Date 0008, 2023年8月8日 13:38
 * <AUTHOR>
 **/
@Slf4j
@Service
public class DingTalkOauth2Api implements IOaOauth2Api {
    @Resource
    private DingTalkConfig dingTalkConfig;
    @Resource
    private RedissonClient redissonClient;

    @Override
    public String getAccessToken() {
        this.checkEnable();
        RBucket<String> accessTokenBucket = redissonClient.getBucket(DingTalkConstants.CACHE_KEY_ACCESS_TOKEN+":"+dingTalkConfig.getAppKey());
        String accessToken = accessTokenBucket.get();
        if (StringUtils.isNoneBlank(accessToken)) {
            return accessToken;
        }

        DingTalkClient client = new DefaultDingTalkClient(dingTalkConfig.getServerUrl(DingTalkConstants.API_GET_ACCESS_TOKEN));
        OapiGettokenRequest request = new OapiGettokenRequest();
        request.setAppkey(dingTalkConfig.getAppKey());
        request.setAppsecret(dingTalkConfig.getAppSecret());
        request.setHttpMethod(GET);

        OapiGettokenResponse response = null;
        try {
            response = client.execute(request);
        } catch (ApiException e) {
            log.error("ding talk api getAccessToken error: {}", e.getErrMsg());
            throw new ServiceException(e.getErrMsg());
        }
        log.info("ding talk api getAccessToken: {}", response.getBody());
        if (!response.isSuccess()) {
            throw new ServiceException(response.getErrmsg());
        }
        accessTokenBucket.set(response.getAccessToken(), response.getExpiresIn() - DingTalkConstants.EARLY_REFRESH_SECONDS,
                TimeUnit.SECONDS);
        return response.getAccessToken();
    }

    @Override
    public OaAuthScopeDTO getAuthScope() {
        this.checkEnable();
        DingTalkClient client = new DefaultDingTalkClient(dingTalkConfig.getServerUrl(DingTalkConstants.API_GET_AUTH_SCOPE));
        OapiAuthScopesRequest req = new OapiAuthScopesRequest();
        req.setHttpMethod(GET);
        OapiAuthScopesResponse rsp = null;
        try {
            rsp = client.execute(req, getAccessToken());
        } catch (ApiException e) {
            log.error("ding talk api getAuthScope error: {}", e.getErrMsg());
            throw new ServiceException(e.getErrMsg());
        }
        log.info("ding talk api getAuthScope: {}", rsp.getBody());
        if (!rsp.isSuccess()) {
            throw new ServiceException(rsp.getErrmsg());
        }
        OaAuthScopeDTO authScope = new OaAuthScopeDTO();
        authScope.setOaDeptIds(CollectionUtils.emptyIfNull(rsp.getAuthOrgScopes().getAuthedDept())
                .stream().map(String::valueOf).collect(Collectors.toList()));
        authScope.setOaUserIds((List<String>) CollectionUtils.emptyIfNull(rsp.getAuthOrgScopes().getAuthedUser()));
        return authScope;
    }

    private void checkEnable() {
        if (Objects.isNull(dingTalkConfig.getEnable()) || !dingTalkConfig.getEnable()) {
            log.info("系统未启用钉钉配置");
            throw new ServiceException("系统未启用钉钉配置");
        }
    }
}
