package com.rutong.medical.admin.config;

import com.soft.common.mqtt.MQTTClientWrapper;
import com.soft.common.mqtt.MQTTProperties;
import com.soft.common.mqtt.MQTTTopicMessageListener;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * @ClassName MQTTConfig
 * @Description
 * <AUTHOR>
 * @Date 2025/7/14 16:16
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Configuration
public class MQTTConfig {

    @Resource
    private ObjectProvider<MQTTTopicMessageListener> listeners;

    @Bean
    @ConfigurationProperties(prefix = "mqtt")
    public MQTTProperties getProperties(){
        return new MQTTProperties();
    }

    /**
     * MQTT客户端
     * <p>
     * 容器加载bean即连接服务;需要发送消息的地方直接注入bean即可
     * <p>
     * {@link org.springframework.beans.factory.annotation.Autowired @Autowired}.
     * <p>
     * private {@link MQTTClientWrapper MQTTClientWrapper} mqttClient;
     * @param properties
     * @return MQTTClientWrapper
     */
    @Bean
    public MQTTClientWrapper mqttClient(MQTTProperties properties) {
        MQTTClientWrapper wrapper = new MQTTClientWrapper(properties);
        // 连接MQTT服务
        wrapper.connect();
        // 订阅主题，不同主题通过MQTTTopicMessageListener来实现不同的内容处理
        Map<String, MQTTTopicMessageListener> mqttTopicMessageListenerMap = listeners
                .stream()
                .collect(Collectors.toMap(MQTTTopicMessageListener::getTopic, listener -> listener));
        wrapper.subscribes(mqttTopicMessageListenerMap);
//        listeners.stream().forEach(wrapper::subscribe);
        return wrapper;
    }
}
