package com.soft.admin.upms.controller;

import com.soft.admin.upms.dto.SystemMessagePageDTO;
import com.soft.admin.upms.service.SystemMessageService;
import com.soft.admin.upms.vo.SystemMessageVO;
import com.soft.common.core.object.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 系统消息控制器类
 *
 * <AUTHOR>
 * @date 2023-06-26
 */
@Api(tags = "系统消息接口")
@RestController
@RequestMapping("/system/message")
public class SystemMessageController {

    @Autowired
    private SystemMessageService systemMessageService;

    /**
     * 分页查询系统消息
     *
     * @param param
     * @return
     */
    @ApiOperation("分页查询系统消息")
    @GetMapping("/page")
    public ResponseResult<MyPageData<SystemMessageVO>> page(SystemMessagePageDTO param) {
        param.setUserId(TokenData.takeFromRequest().getUserId());
        return ResponseResult.success(systemMessageService.page( param));
    }

    /**
     * 批量已读
     *
     * @param param
     * @return
     */
    @ApiOperation("批量已读")
    @PostMapping("/batchRead")
    public ResponseResult batchRead( @RequestBody @Validated IdListDTO param) {
        systemMessageService.batchRead(TokenData.takeFromRequest().getUserId(), param.getIdList());
        return ResponseResult.success();
    }


    /**
     * 删除
     *
     * @param param
     * @return
     */
    @ApiOperation("删除")
    @PostMapping("/delete")
    public ResponseResult<Void> delete( @RequestBody @Validated IdDTO param) {
        systemMessageService.removeMessageUserRelation(TokenData.takeFromRequest().getUserId(), param.getId());
        return ResponseResult.success();
    }
}
