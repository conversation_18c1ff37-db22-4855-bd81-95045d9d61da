package com.soft.admin.upms.model;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.admin.upms.vo.SysAnnexVO;
import com.soft.common.core.annotation.TenantFilterColumn;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.common.core.base.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 公共附件对象 common_sys_annex
 *
 * <AUTHOR>
 * @date 2023-09-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "common_sys_annex")
public class SysAnnex extends BaseModel {

    /**
     * 生产制度附件ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 附件名称
     */
    private String name;

    /**
     * 路径
     */
    private String path;

    /**
     * 后缀
     */
    private String suffix;

    /**
     * 文件大小
     */
    private Integer size;

    /**
     * 删除标记(1: 正常 -1: 已删除)
     */
    private Integer deletedFlag;


    @Mapper
    public interface SysAnnexModelMapper extends BaseModelMapper<SysAnnexVO, SysAnnex> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        SysAnnex toModel(SysAnnexVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        SysAnnexVO fromModel(SysAnnex entity);
    }

    public static final SysAnnexModelMapper INSTANCE = Mappers.getMapper(SysAnnexModelMapper.class);
}