<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.common.online.dao.OnlineTableMapper">
    <resultMap id="BaseResultMap" type="com.soft.common.online.model.OnlineTable">
        <id column="table_id" jdbcType="BIGINT" property="tableId"/>
        <result column="table_name" jdbcType="VARCHAR" property="tableName"/>
        <result column="model_name" jdbcType="VARCHAR" property="modelName"/>
        <result column="dblink_id" jdbcType="BIGINT" property="dblinkId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.soft.common.online.dao.OnlineTableMapper.inputFilterRef"/>
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="onlineTableFilter != null">
            <if test="onlineTableFilter.tableName != null and onlineTableFilter.tableName != ''">
                AND zz_online_table.table_name = #{onlineTableFilter.tableName}
            </if>
            <if test="onlineTableFilter.modelName != null and onlineTableFilter.modelName != ''">
                AND zz_online_table.model_name = #{onlineTableFilter.modelName}
            </if>
            <if test="onlineTableFilter.dblinkId != null">
                AND zz_online_table.dblink_id = #{onlineTableFilter.dblinkId}
            </if>
        </if>
    </sql>

    <select id="getOnlineTableList" resultMap="BaseResultMap" parameterType="com.soft.common.online.model.OnlineTable">
        SELECT * FROM zz_online_table
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getOnlineTableListByDatasourceId" resultMap="BaseResultMap">
        SELECT a.* FROM zz_online_table a, zz_online_datasource_table b
        WHERE b.datasource_id = #{datasourceId} AND b.table_id = a.table_id
    </select>
</mapper>
