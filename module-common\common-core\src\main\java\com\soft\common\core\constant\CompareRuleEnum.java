package com.soft.common.core.constant;

import lombok.Getter;

@Getter
public enum CompareRuleEnum {

    RULE_GT(">", "大于"),
    RULE_GE(">=", "大于等于"),
    RULE_LT("<", "小于"),
    RULE_LE("<=", "小于等于"),
    RULE_EQ("=", "等于"),
    RULE_MAX(">=", "上限值"),
    RULE_MIN("<=", "下限值");

    private String rule;

    private String desc;

    CompareRuleEnum(String rule, String desc) {
        this.rule = rule;
        this.desc = desc;
    }

}
