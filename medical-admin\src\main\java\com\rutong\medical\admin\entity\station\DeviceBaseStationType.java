package com.rutong.medical.admin.entity.station;

import com.rutong.medical.admin.vo.station.DeviceBaseStationTypeVO;
import lombok.Data;
import com.soft.common.core.base.model.BaseModel;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.EqualsAndHashCode;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * 基站分类对象 device_base_station_type
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sm_device_base_station_type")
public class DeviceBaseStationType extends BaseModel {

    /** 基站分类表ID */
    @TableId(value = "id")
    private Long id;

    /** 编号 */
    private String typeCode;

    /** 名称 */
    private String typeName;
    /**
     *父级id
     */
    private Long parentId;
    /**
     *id路径
     */
    private String pathId;

    /** 是否删除 */
    private Integer isDelete;



    @Mapper
    public interface DeviceBaseStationTypeModelMapper extends BaseModelMapper<DeviceBaseStationTypeVO, DeviceBaseStationType> {
        /**
        * 转换Vo对象到实体对象。
        *
        * @param entityVo 域对象。
        * @return 实体对象。
        */
        @Override
        DeviceBaseStationType toModel(DeviceBaseStationTypeVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        DeviceBaseStationTypeVO fromModel(DeviceBaseStationType entity);
    }

    public static final DeviceBaseStationTypeModelMapper INSTANCE = Mappers.getMapper(DeviceBaseStationTypeModelMapper.class);
}
