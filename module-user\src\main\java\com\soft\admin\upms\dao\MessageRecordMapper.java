package com.soft.admin.upms.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.soft.admin.upms.model.message.MessageRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Collections;
import java.util.List;

/**
 * 消息中心记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-27
 */
public interface MessageRecordMapper extends BaseMapper<MessageRecord> {
    /**
     * <AUTHOR>
     * @Description 查询是否有相同消息
     * @Date 11:58 AM 2024/8/21
     * @Param [receiveUserId, busiType, title]
     * @return int
     **/
    int countMessageRecord(@Param("receiveUserId") Long receiveUserId, @Param("busiId") Long busiId);
}
