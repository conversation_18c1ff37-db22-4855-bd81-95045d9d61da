package com.rutong.medical.admin.entity.monitor;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rutong.medical.admin.vo.monitor.DeviceMonitorVO;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.common.core.base.model.BaseModel;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 视频监控对象 sm_device_monitor
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sm_device_monitor")
public class DeviceMonitor extends BaseModel {

    /** 视频监控表ID */
    @TableId(value = "id")
    private Long id;

    /** 监控编号 */
    private String monitorCode;

    /** 监控名称 */
    private String monitorName;

    /** 监控类型(1:枪机,2:球机) */
    private Integer monitorType;

    /** 安装位置 */
    private Long spaceId;

    /** 所属楼层路径 */
    private String spacePath;

    /** 所属楼层全名称 */
    private String spaceFullName;

    /** ip地址 */
    private String ip;

    /** 端口 */
    private Long port;

    /** 通道号 */
    private String channelNum;

    /** 用户名 */
    private String userCode;

    /** 密码 */
    private String password;

    /** 是否删除 */
    private Integer isDelete;
    /**
     * 生产厂家
     */
    private String factory;

    @Mapper
    public interface SmDeviceMonitorModelMapper extends BaseModelMapper<DeviceMonitorVO, DeviceMonitor> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        DeviceMonitor toModel(DeviceMonitorVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        DeviceMonitorVO fromModel(DeviceMonitor entity);
    }

    public static final SmDeviceMonitorModelMapper INSTANCE = Mappers.getMapper(SmDeviceMonitorModelMapper.class);
}
