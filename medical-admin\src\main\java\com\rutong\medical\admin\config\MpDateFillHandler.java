package com.rutong.medical.admin.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.soft.common.core.object.TokenData;
import com.soft.common.core.util.ContextUtil;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @ClassName MpDateFillHandler
 * @Description 自动填充时间，修改操作的自动填充只有是通过 updateById() 才会生效，而通过 Wrapper 查询条件的更新不会生效
 * <AUTHOR>
 * @Date 2025/7/16 10:05
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Component
public class MpDateFillHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        // 先判断是否存在该字段
        boolean createTime = metaObject.hasSetter("createTime");
        if (createTime && metaObject.getValue("createTime") == null) {
            setFieldValByName("createTime", new Date(), metaObject);
        }

        // 修改时间和创建时间要相同
        boolean updateTime = metaObject.hasSetter("updateTime");
        if (updateTime && metaObject.getValue("updateTime") == null) {
            setFieldValByName("updateTime", new Date(), metaObject);
        }

        // 判断是在 HTTP 请求，才能从请求中获取认证信息
        if (ContextUtil.hasRequestContext()) {
            TokenData tokenData = TokenData.takeFromRequest();
            boolean createUserId = metaObject.hasSetter("createUserId");
            if (createUserId) {
                if (tokenData != null) {
                    Long userId = tokenData.getUserId();
                    setFieldValByName("createUserId", userId, metaObject);
                }
            }
            boolean updateUserId = metaObject.hasSetter("updateUserId");
            if (updateUserId) {
                if (tokenData != null) {
                    Long userId = tokenData.getUserId();
                    setFieldValByName("updateUserId", userId, metaObject);
                }
            }
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        boolean hasUpdateTime = metaObject.hasSetter("updateTime");
        if (hasUpdateTime) {
            // 先判断该值是否为空，为空才填充
            setFieldValByName("updateTime", new Date(), metaObject);
        }

        boolean hasUpdateUserId = metaObject.hasSetter("updateUserId");
        if (ContextUtil.hasRequestContext() && hasUpdateUserId) {
            // 先判断该值是否为空，为空才填充
            TokenData tokenData = TokenData.takeFromRequest();
            if (tokenData != null) {
                Long userId = tokenData.getUserId();
                setFieldValByName("updateUserId", userId, metaObject);
            }
        }
    }
}

