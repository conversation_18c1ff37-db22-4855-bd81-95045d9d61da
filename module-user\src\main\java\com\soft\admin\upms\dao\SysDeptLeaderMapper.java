package com.soft.admin.upms.dao;

import com.soft.admin.upms.model.SysDeptLeader;
import com.soft.common.core.base.dao.BaseDaoMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户部门领导数据操作访问接口。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
public interface SysDeptLeaderMapper extends BaseDaoMapper<SysDeptLeader> {

    /**
     * 根据版本批量删除
     */
    void deleteBatch(@Param("syncVersion") Long syncVersion);



    List<SysDeptLeader> selectSysDeptLeader(@Param("userId") Long userId,@Param("deptIds")List<Long> deptId);


    /**
     *查询部门领导
     * @param deptId
     * @return
     */
    List<SysDeptLeader> selectByDeptId(Long deptId);


    /**
     * 根据部门Id删除
     * @param deptId
     */
    void deleteByDeptId(@Param("deptId") Long deptId);
}
