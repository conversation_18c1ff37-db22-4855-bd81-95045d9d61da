package com.soft.admin.upms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.soft.admin.upms.dao.SysTagMapper;
import com.soft.admin.upms.dto.SysTagDTO;
import com.soft.admin.upms.dto.SysTagPageDTO;
import com.soft.admin.upms.model.SysTag;
import com.soft.admin.upms.service.SysTagService;
import com.soft.admin.upms.vo.SysTagVO;
import com.soft.common.core.constant.GlobalDeletedFlag;
import com.soft.common.core.exception.MyRuntimeException;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.util.MyModelUtil;
import com.soft.common.core.util.MyPageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 标签Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Service
public class SysTagServiceImpl extends ServiceImpl<SysTagMapper, SysTag> implements SysTagService {

    private static final String TAGSTAR = "TAG_";

    @Autowired
    private SysTagMapper tagMapper;

    @Override
    public boolean update(SysTagDTO tagDTO) {
        SysTag tag = MyModelUtil.copyTo(tagDTO, SysTag.class);

        //判断名称是否存在
        LambdaQueryWrapper<SysTag> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SysTag::getName, tagDTO.getName());
        queryWrapper.eq(SysTag::getType, tagDTO.getType());
        queryWrapper.eq(SysTag::getDeletedFlag, GlobalDeletedFlag.NORMAL);
        List<SysTag> tagList = tagMapper.selectList(queryWrapper);

        if (CollectionUtils.isNotEmpty(tagList)) {
            SysTag tags = tagList.get(0);
            if (tagDTO.getId() == null || !tags.getId().equals(tagDTO.getId())) {
                throw new MyRuntimeException("标签名称已存在");
            }
        }

        if (tagDTO.getId() == null) {
            LambdaQueryWrapper<SysTag> queryWrapper2 = Wrappers.lambdaQuery();
            Long count = tagMapper.selectCount(queryWrapper2);
            // 将整数格式化为5位数字
            String TagNum = String.format(TAGSTAR + "%05d", count + 1);
            tag.setCode(TagNum);
            tag.setDeletedFlag(GlobalDeletedFlag.NORMAL);
            this.save(tag);
        } else {
            this.updateById(tag);
        }
        return true;
    }

    @Override
    public MyPageData<SysTagVO> pageList(SysTagPageDTO pageDTO) {
        Integer pageNum = pageDTO.getPageNum();
        Integer pageSize = pageDTO.getPageSize();
        if (pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize);
        }
        LambdaQueryWrapper<SysTag> queryWrapper = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(pageDTO.getKey())) {
            queryWrapper.or((wrapper) -> {
                wrapper.like(SysTag::getCode, pageDTO.getKey()).or();
                wrapper.like(SysTag::getName, pageDTO.getKey());
            });
        }
        queryWrapper.eq(SysTag::getType, pageDTO.getType());
        queryWrapper.eq(SysTag::getDeletedFlag, GlobalDeletedFlag.NORMAL);
        List<SysTag> tagList = tagMapper.selectList(queryWrapper);
        return MyPageUtil.makeResponseData(tagList, SysTag.INSTANCE);
    }

    @Override
    public boolean delete(Long tagId) {
        SysTag tag = tagMapper.selectById(tagId);
        tag.setDeletedFlag(GlobalDeletedFlag.DELETED);
        return tagMapper.updateById(tag) == 1 ? true : false;
    }
}
