<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.common.online.dao.OnlineColumnMapper">
    <resultMap id="BaseResultMap" type="com.soft.common.online.model.OnlineColumn">
        <id column="column_id" jdbcType="BIGINT" property="columnId"/>
        <result column="column_name" jdbcType="VARCHAR" property="columnName"/>
        <result column="table_id" jdbcType="BIGINT" property="tableId"/>
        <result column="column_type" jdbcType="VARCHAR" property="columnType"/>
        <result column="full_column_type" jdbcType="VARCHAR" property="fullColumnType"/>
        <result column="primary_key" jdbcType="BOOLEAN" property="primaryKey"/>
        <result column="auto_increment" jdbcType="BOOLEAN" property="autoIncrement"/>
        <result column="nullable" jdbcType="BOOLEAN" property="nullable"/>
        <result column="column_default" jdbcType="VARCHAR" property="columnDefault"/>
        <result column="column_show_order" jdbcType="INTEGER" property="columnShowOrder"/>
        <result column="column_comment" jdbcType="VARCHAR" property="columnComment"/>
        <result column="object_field_name" jdbcType="VARCHAR" property="objectFieldName"/>
        <result column="object_field_type" jdbcType="VARCHAR" property="objectFieldType"/>
        <result column="numeric_precision" jdbcType="INTEGER" property="numericPrecision"/>
        <result column="numeric_scale" jdbcType="INTEGER" property="numericScale"/>
        <result column="filter_type" jdbcType="INTEGER" property="filterType"/>
        <result column="parent_key" jdbcType="BOOLEAN" property="parentKey"/>
        <result column="dept_filter" jdbcType="BOOLEAN" property="deptFilter"/>
        <result column="user_filter" jdbcType="BOOLEAN" property="userFilter"/>
        <result column="field_kind" jdbcType="INTEGER" property="fieldKind"/>
        <result column="max_file_count" jdbcType="INTEGER" property="maxFileCount"/>
        <result column="upload_file_system_type" jdbcType="INTEGER" property="uploadFileSystemType"/>
        <result column="encoded_rule" jdbcType="VARCHAR" property="encodedRule"/>
        <result column="dict_id" jdbcType="BIGINT" property="dictId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.soft.common.online.dao.OnlineColumnMapper.inputFilterRef"/>
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="onlineColumnFilter != null">
            <if test="onlineColumnFilter.tableId != null">
                AND zz_online_column.table_id = #{onlineColumnFilter.tableId}
            </if>
            <if test="onlineColumnFilter.columnName != null and onlineColumnFilter.columnName != ''">
                AND zz_online_column.column_name = #{onlineColumnFilter.columnName}
            </if>
        </if>
    </sql>

    <select id="getOnlineColumnList" resultMap="BaseResultMap" parameterType="com.soft.common.online.model.OnlineColumn">
        SELECT * FROM zz_online_column
        <where>
            <include refid="filterRef"/>
        </where>
        ORDER BY column_show_order
    </select>
</mapper>
