package com.rutong.medical.admin.vo.station;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * DeviceBaseStationTypeVO视图对象
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@ApiModel("DeviceBaseStationTypeVO视图对象")
@Data
public class DeviceBaseStationTypeVO {

    @ApiModelProperty(value = "基站分类表ID")
    private Long id;

    @ApiModelProperty(value = "编号")
    private String typeCode;

    @ApiModelProperty(value = "名称")
    private String typeName;

    @ApiModelProperty(value = "创建人")
    private Long createUserId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改人")
    private Long updateUserId;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

    @ApiModelProperty(value = "父级id")
    private Long parentId;

    @ApiModelProperty(value = "id路径")
    private String pathId;

}
