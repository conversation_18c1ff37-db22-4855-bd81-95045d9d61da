package com.soft.admin.upms.dto.dintalk;

import lombok.Data;

@Data
public class OaRemindTodoDTO {

    /**
     * 消息id
     */
    private Long busiId;

    /**
     * 消息id
     */
    private Long messageId;

    /**
     * 执行者id
     */
    private String executorId;

    /**
     * 创建者id
     */
    private String creatorId;

    /**
     * 待办任务标题
     */
    private String title;

    /**
     * 跳转RUL
     */
    private String url;

    /**
     * 业务id
     */
    // private String bizId;

    /**
     * 待办备注描述
     */
    private String description;

    /**
     * 业务类型
     */
    private String busiType;

    private Long sendUserId;

    private Long receiveUserId;
}
