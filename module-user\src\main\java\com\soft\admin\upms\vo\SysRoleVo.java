package com.soft.admin.upms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 角色VO。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
@ApiModel("角色VO")
@Data
public class SysRoleVo {

    /**
     * 角色Id。
     */
    @ApiModelProperty(value = "角色Id")
    private Long roleId;

    /**
     * 角色名称。
     */
    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "角色描述")
    private String roleDesc;


    @ApiModelProperty("角色状态，0禁用；1启用")
    private Integer status;

    /**
     * 创建者Id。
     */
    @ApiModelProperty(value = "创建者Id")
    private Long createUserId;

    /**
     * 创建时间。
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新者Id。
     */
    @ApiModelProperty(value = "更新者Id")
    private Long updateUserId;

    /**
     * 更新时间。
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 角色与菜单关联对象列表。
     */
    @ApiModelProperty(value = "角色与菜单关联对象列表")
    private List<Map<String, Object>> sysRoleMenuList;
}
