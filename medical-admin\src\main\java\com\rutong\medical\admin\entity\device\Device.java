package com.rutong.medical.admin.entity.device;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.rutong.medical.admin.vo.device.DeviceVO;
import lombok.Data;
import com.soft.common.core.base.model.BaseModel;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.EqualsAndHashCode;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * 设备对象 sm_device
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sm_device")
public class Device extends BaseModel {

    /** 设备表ID */
    @TableId(value = "id")
    private Long id;

    /** 设备分类表ID */
    private Long deviceTerminalTypeId;

    /** 设备编号 */
    private String deviceCode;

    /** 设备名称 */
    private String deviceName;

    /** 业务系统编号 */
    private String businessCode;

    /** 安装位置 */
    private Long spaceId;

    /** 所属楼层路径 */
    private String spacePath;

    /** 所属楼层全名称 */
    private String spaceFullName;

    /** x */
    private Long x;

    /** y */
    private Long y;

    /** z */
    private Long z;

    /** 经度 */
    @TableField(value = "latitude", updateStrategy = FieldStrategy.IGNORED)
    private Long longitude;

    /** 纬度 */
    @TableField(value = "latitude", updateStrategy = FieldStrategy.IGNORED)
    private Long latitude;

    /** 在线状态(1:在线,0:离线) */
    private Integer isOnline;

    /** 是否删除(0:否,1:删除) */
    private Integer isDelete;

    /**
     * 终端sn
     */
    private String deviceSn;


    @Mapper
    public interface SmDeviceModelMapper extends BaseModelMapper<DeviceVO, Device> {
        /**
        * 转换Vo对象到实体对象。
        *
        * @param entityVo 域对象。
        * @return 实体对象。
        */
        @Override
        Device toModel(DeviceVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        DeviceVO fromModel(Device entity);
    }

    public static final SmDeviceModelMapper INSTANCE = Mappers.getMapper(SmDeviceModelMapper.class);
}
