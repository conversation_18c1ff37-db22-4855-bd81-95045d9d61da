package com.soft.admin.upms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * SystemMessageDTO对象
 *
 * <AUTHOR>
 * @date 2023-06-26
 */
@ApiModel("SystemMessageDTO对象")
@Data
public class SystemMessageDTO {

    @ApiModelProperty(value = "${column.columnComment}")
    @NotNull(message = "数据验证失败，${column.columnComment}不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "系统消息标题")
    private String title;


    @ApiModelProperty(value = "系统消息类型")
    private String type;


    @ApiModelProperty(value = "系统消息紧急程度")
    private Integer priority;


    @ApiModelProperty(value = "业务id")
    private Long businessId;


    @ApiModelProperty(value = "创建时间")
    private Date createTime;

}
