<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.SysConfigMapper">
    <resultMap type="com.soft.admin.upms.model.SysConfig" id="SysConfigResult">
        <result property="configKey" column="config_key" />
        <result property="keyValue" column="key_value" />
        <result property="valueType" column="value_type" />
        <result property="enumValue" column="enum_value" />
        <result property="explainInit" column="explain_init" />
        <result property="isShow" column="is_show" />
        <result property="isEdit" column="is_edit" />
        <result property="remark" column="remark" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectSysConfigVo">
        select config_key, key_value, value_type, enum_value, explain_init, is_show, is_edit, remark, create_user_id, create_time, update_user_id, update_time from common_sys_config
    </sql>

</mapper>