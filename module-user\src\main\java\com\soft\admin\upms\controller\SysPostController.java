package com.soft.admin.upms.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.jimmyshi.beanquery.BeanQuery;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.page.PageMethod;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.soft.admin.upms.dao.SysDeptMapper;
import com.soft.admin.upms.dao.SysDeptPostMapper;
import com.soft.admin.upms.dao.SysUserMapper;
import com.soft.admin.upms.dao.SysUserPostMapper;
import com.soft.admin.upms.dto.SysPostDto;
import com.soft.admin.upms.enums.PostEnums;
import com.soft.admin.upms.model.SysDept;
import com.soft.admin.upms.model.SysDeptPost;
import com.soft.admin.upms.model.SysPost;
import com.soft.admin.upms.model.SysUserPost;
import com.soft.admin.upms.service.SysPostService;
import com.soft.admin.upms.vo.SysPostVo;
import com.soft.common.core.annotation.MyRequestBody;
import com.soft.common.core.constant.ErrorCodeEnum;
import com.soft.common.core.exception.MyRuntimeException;
import com.soft.common.core.object.*;
import com.soft.common.core.util.MyCommonUtil;
import com.soft.common.core.util.MyModelUtil;
import com.soft.common.core.util.MyPageUtil;
import com.soft.common.core.validator.UpdateGroup;
import com.soft.common.log.annotation.OperationLog;
import com.soft.common.log.model.constant.SysOperationLogType;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.groups.Default;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 岗位管理操作控制器类。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
@Api(tags = "岗位管理操作管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/upms/sysPost")
public class SysPostController {

    @Autowired
    private SysPostService sysPostService;

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Resource
    private SysDeptPostMapper sysDeptPostMapper;

    @Resource
    private SysUserPostMapper sysUserPostMapper;

    @Resource
    private SysUserMapper sysUserMapper;


    /**
     * 新增岗位管理数据。
     *
     * @param sysPostDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {"sysPostDto.postId"})
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Long> add(@MyRequestBody SysPostDto sysPostDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(sysPostDto);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SysPost sysPost = MyModelUtil.copyTo(sysPostDto, SysPost.class);

        // 校验岗位编号
        String postCode = sysPost.getPostCode();
        long count = sysPostService.count(Wrappers.lambdaQuery(SysPost.class).eq(SysPost::getPostCode, postCode));
        if (count > 0) {
            throw new MyRuntimeException("岗位编号已存在！");
        }

        sysPost = sysPostService.saveNew(sysPost);
        Long postId = sysPost.getPostId();

        List<Long> deptIds = sysPostDto.getDeptIds();
        if (CollectionUtil.isNotEmpty(deptIds)) {
            String postName = sysPost.getPostName();
            // 查询部门列表
            List<SysDept> sysDepts = sysDeptMapper.selectBatchIds(deptIds);
            if (CollectionUtil.isNotEmpty(sysDepts)) {
               deptIds = sysDepts.stream().map(SysDept::getDeptId).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(deptIds)) {
                    List<SysDeptPost> sysDeptPostList = new ArrayList<>();
                    deptIds.forEach(deptId -> {
                        SysDeptPost sysDeptPost = new SysDeptPost();
                        sysDeptPost.setDeptId(deptId);
                        sysDeptPost.setPostId(postId);
                        sysDeptPost.setPostShowName(postName);
                        sysDeptPostList.add(sysDeptPost);
                    });
                    sysDeptPostMapper.insertBatch(sysDeptPostList);
                }
            }
        }
        return ResponseResult.success(postId);
    }

    /**
     * 更新岗位管理数据。
     *
     * @param sysPostDto 更新对象。
     * @return 应答结果对象。
     */
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> update(@MyRequestBody SysPostDto sysPostDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(sysPostDto, Default.class, UpdateGroup.class);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SysPost sysPost = MyModelUtil.copyTo(sysPostDto, SysPost.class);

        // 校验岗位编号
        String postCode = sysPost.getPostCode();
        long count = sysPostService.count(Wrappers.lambdaQuery(SysPost.class)
                .eq(SysPost::getPostCode, postCode)
                .ne(SysPost::getPostId, sysPost.getPostId()));
        if (count > 0) {
            throw new MyRuntimeException("岗位编号已存在！");
        }

        SysPost originalSysPost = sysPostService.getById(sysPost.getPostId());
        if (originalSysPost == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!sysPostService.update(sysPost, originalSysPost)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }


        // 查询已经添加过的部门
        List<SysDeptPost> sysDeptPosts = sysDeptPostMapper.selectList(Wrappers.lambdaQuery(SysDeptPost.class)
                .eq(SysDeptPost::getPostId, sysPost.getPostId()));
        List<Long> deptIdsExist = sysDeptPosts.stream().map(SysDeptPost::getDeptId).collect(Collectors.toList());
        List<Long> delDeptIds = new ArrayList<>();

        // 修改或新增岗位所属部门
        List<Long> deptIds = sysPostDto.getDeptIds();

        if (CollectionUtil.isNotEmpty(deptIds)) {
            List<Long> newDeptIds = deptIds;
            if (CollectionUtil.isNotEmpty(deptIdsExist)) {
                // 新增岗位部门
                newDeptIds = deptIds.stream().filter(deptId -> !deptIdsExist.contains(deptId)).collect(Collectors.toList());
                // 删除岗位部门
                delDeptIds = deptIdsExist.stream().filter(deptId -> !deptIds.contains(deptId)).collect(Collectors.toList());
            }

            if (CollectionUtil.isNotEmpty(newDeptIds)) {
                // 查询部门列表
                List<SysDept> sysDepts = sysDeptMapper.selectBatchIds(newDeptIds);
                if (CollectionUtil.isNotEmpty(sysDepts)) {
                    List<SysDeptPost> sysDeptPostList = sysDepts.stream()
                            .map(SysDept::getDeptId)
                            .map(deptId -> {
                                SysDeptPost sysDeptPost = new SysDeptPost();
                                sysDeptPost.setDeptId(deptId);
                                sysDeptPost.setPostId(sysPost.getPostId());
                                sysDeptPost.setPostShowName(sysPost.getPostName());
                                return sysDeptPost;
                            })
                            .collect(Collectors.toList());
                   if (CollectionUtil.isNotEmpty(sysDeptPostList)) {
                       sysDeptPostMapper.insertBatch(sysDeptPostList);
                   }
                }
            }
        }
        if (CollectionUtil.isNotEmpty(delDeptIds)) {
            sysDeptPostMapper.delete(Wrappers.lambdaQuery(SysDeptPost.class).eq(SysDeptPost::getPostId, sysPost.getPostId()).in(SysDeptPost::getDeptId, delDeptIds));
        }
        return ResponseResult.success();
    }

    /**
     * 删除岗位管理数据。
     *
     * @param postId 删除对象主键Id。
     * @return 应答结果对象。
     */
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long postId) {
        String errorMessage;
        if (MyCommonUtil.existBlankArgument(postId)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        // 验证关联Id的数据合法性
        SysPost originalSysPost = sysPostService.getById(postId);
        if (originalSysPost == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        PostEnums postEnums = PostEnums.getByCode(originalSysPost.getPostCode());
        if (postEnums != null) {
            return ResponseResult.error(ErrorCodeEnum.UNHANDLED_EXCEPTION, "固有岗位不允许删除！");
        }
        if (!sysPostService.remove(postId)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的岗位管理列表。
     *
     * @param sysPostDtoFilter 过滤对象。
     * @param orderParam       排序参数。
     * @param pageParam        分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @PostMapping("/list")
    public ResponseResult<MyPageData<SysPostVo>> list(
            @MyRequestBody SysPostDto sysPostDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        SysPost sysPostFilter = MyModelUtil.copyTo(sysPostDtoFilter, SysPost.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SysPost.class);

        // 查询岗位列表
        List<SysPost> sysPostList = sysPostService.getSysPostListWithRelation(sysPostFilter, orderBy);
        if (CollectionUtil.isEmpty(sysPostList)) {
            return ResponseResult.success(MyPageData.emptyPageData());
        }

        MyPageData<SysPostVo> sysPostVoMyPageData = MyPageUtil.makeResponseData(sysPostList, SysPost.INSTANCE);
        List<SysPostVo> sysPostVos = sysPostVoMyPageData.getDataList();

        Set<Long> postIds = sysPostVos.stream().map(SysPostVo::getPostId).collect(Collectors.toSet());
        // 查询岗位所属部门
        List<SysDeptPost> sysDeptPosts = sysDeptPostMapper.selectList(Wrappers.lambdaQuery(SysDeptPost.class)
                .in(SysDeptPost::getPostId, postIds));
        Map<Long, Set<Long>> postDeptIdsMap = sysDeptPosts.stream()
                .collect(Collectors.groupingBy(SysDeptPost::getPostId, Collectors.mapping(SysDeptPost::getDeptId, Collectors.toSet())));

        Set<Long> deptIds = new HashSet<>();
        postDeptIdsMap.values().forEach(deptIds::addAll);

        List<SysDept> sysDepts = sysDeptMapper.selectBatchIds(deptIds);
        Map<Long, SysDept> sysDeptMap = sysDepts.stream().collect(Collectors.toMap(SysDept::getDeptId, sysDept -> sysDept));
        Map<Long, Set<SysDept>> postDeptNameMap = new HashMap<>();
        postDeptIdsMap.forEach((postId, deptIdSet) -> {
            if (CollectionUtil.isNotEmpty(deptIdSet)) {
                Set<SysDept> sysDeptSet = deptIdSet.stream().map(sysDeptMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                postDeptNameMap.put(postId, sysDeptSet);
            }
        });

        // 查询岗位用户数量
        List<SysUserPost> sysUserPosts = sysUserPostMapper.selectList(Wrappers.lambdaQuery(SysUserPost.class)
                .in(SysUserPost::getPostId, postIds));
        Map<Long, List<SysUserPost>> userPostMap = sysUserPosts.stream()
                .collect(Collectors.groupingBy(SysUserPost::getPostId));

        for (SysPostVo sysPostVo : sysPostVos) {
            Long postId = sysPostVo.getPostId();
            sysPostVo.setSysDepts(postDeptNameMap.get(postId));

            List<SysUserPost> userPosts = userPostMap.get(sysPostVo.getPostId());
            long userCount = 0L;
            if (CollectionUtil.isNotEmpty(userPosts)) {
                Set<Long> userIds = userPosts.stream().map(SysUserPost::getUserId).collect(Collectors.toSet());
                userCount = sysUserMapper.selectBatchIds(userIds).size();
            }
            sysPostVo.setPostUserNum(userCount);
        }

        return ResponseResult.success(sysPostVoMyPageData);
    }

    /**
     * 查看指定岗位管理对象详情。
     *
     * @param postId 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @GetMapping("/view")
    public ResponseResult<SysPostVo> view(@RequestParam Long postId) {
        if (MyCommonUtil.existBlankArgument(postId)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        SysPost sysPost = sysPostService.getByIdWithRelation(postId, MyRelationParam.full());
        if (sysPost == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SysPostVo sysPostVo = SysPost.INSTANCE.fromModel(sysPost);
        return ResponseResult.success(sysPostVo);
    }

    /**
     * 以字典形式返回全部岗位管理数据集合。字典的键值为[postId, postName]。
     * 白名单接口，登录用户均可访问。
     *
     * @param filter 过滤对象。
     * @return 应答结果对象，包含的数据为 List<Map<String, String>>，map中包含两条记录，key的值分别是id和name，value对应具体数据。
     */
    @GetMapping("/listDict")
    public ResponseResult<List<Map<String, Object>>> listDict(SysPost filter) {
        List<SysPost> resultList = sysPostService.getListByFilter(filter);
        return ResponseResult.success(BeanQuery.select("postId as id", "postName as name").executeFrom(resultList));
    }

    /**
     * 根据字典Id集合，获取查询后的字典数据。
     *
     * @param postIds 字典Id集合。
     * @return 应答结果对象，包含字典形式的数据集合。
     */
    @PostMapping("/listDictByIds")
    public ResponseResult<List<Map<String, Object>>> listDictByIds(@MyRequestBody List<Long> postIds) {
        List<SysPost> resultList = sysPostService.getInList(new HashSet<>(postIds));
        return ResponseResult.success(BeanQuery.select("postId as id", "postName as name").executeFrom(resultList));
    }
}
