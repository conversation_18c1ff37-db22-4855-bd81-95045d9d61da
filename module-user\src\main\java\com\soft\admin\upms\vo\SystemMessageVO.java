package com.soft.admin.upms.vo;

import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SystemMessageVO视图对象
 *
 * <AUTHOR>
 * @date 2023-06-26
 */
@ApiModel("SystemMessageVO视图对象")
@Data
public class SystemMessageVO {

    @ApiModelProperty(value = "${column.columnComment}")
    private Long id;

    @ApiModelProperty(value = "系统消息标题")
    private String title;

    @ApiModelProperty(value = "系统消息内容")
    private String content;

    @ApiModelProperty(value = "发布状态 0未发布 1已发布")
    private Integer status;

    @ApiModelProperty(value = "是否全员发送 1全员 0部分人员")
    private Integer allUser;

    @ApiModelProperty(value = "发布时间")
    private Date sendTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "是否已读")
    private Boolean isRead;

    @ApiModelProperty(value = "用户列表")
    private List<SysUserVo> userList;

}
