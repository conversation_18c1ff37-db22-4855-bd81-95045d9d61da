package com.rutong.medical.admin.vo.alarm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName AlarmDetailTDVO
 * @Description
 * <AUTHOR>
 * @Date 2025/7/22 16:13
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@ApiModel("报警记录")
@Data
public class AlarmDetailTDVO {

    @ApiModelProperty(value = "报警记录表ID")
    private Long alarmDetailId;

    @ApiModelProperty(value = "基站表ID")
    private Long deviceBaseStationId;

    @ApiModelProperty(value = "用户表ID")
    private Long userId;

    @ApiModelProperty(value = "设备ID")
    private Long deviceId;

    @ApiModelProperty(value = "设备sn")
    private String deviceSn;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "设备类别编号")
    private String deviceTypeCode;

    @ApiModelProperty(value = "报警类型名称")
    private String deviceTypeName;

    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @ApiModelProperty(value = "报警类型名称")
    private String alarmTypeName;

    @ApiModelProperty(value = "楼栋ID")
    private Long buildingId;

    @ApiModelProperty(value = "楼层ID")
    private Long floorId;

    @ApiModelProperty(value = "点位ID")
    private Long pointId;

    @ApiModelProperty(value = "楼栋名称")
    private String buildingName;

    @ApiModelProperty(value = "楼层名称")
    private String floorName;

    @ApiModelProperty(value = "点位名称")
    private String pointName;

    @ApiModelProperty(value = "报警类型(1-低电压, 2-按键, 3-防拆, 4-红外入侵)")
    private Integer alarmType;

    @ApiModelProperty(value = "处理状态(0:未确认,1:已确认)")
    private Integer disposeState;

    @ApiModelProperty(value = "报警次数")
    private Integer isKeyCount;

    @ApiModelProperty(value = "报警时间")
    private Date alarmDate;

}
