package com.soft.admin.upms.model.message;


import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.soft.admin.upms.api.dingtalk.enums.DingTalkMessageType;
import com.soft.admin.upms.api.dingtalk.service.IWorkNoticeApi;
import com.soft.admin.upms.api.dingtalk.service.impl.DingTalkTodoApi;
import com.soft.admin.upms.api.dingtalk.service.impl.WorkNoticeApi;
import com.soft.admin.upms.dao.OaUserMapper;
import com.soft.admin.upms.dao.SysUserDeviceMapper;
import com.soft.admin.upms.dto.dintalk.OaRemindTodoDTO;
import com.soft.admin.upms.dto.dintalk.OaSendMessageDTO;
import com.soft.admin.upms.enums.MessageRecordBusiTypeEnums;
import com.soft.admin.upms.enums.MessageRecordTypeEnums;
import com.soft.admin.upms.model.OaUser;
import com.soft.admin.upms.model.SysUserDevice;
import com.soft.admin.upms.service.MessageRecordService;
import com.soft.common.push.dto.MessageRecordContentDTO;
import com.soft.common.push.service.JiguangMessageService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Getter
@Setter
public class MessageRecordContent {

    private String title;

    private Long busiId;

    private String level;

    private String content;

    private Long sendUserId;

    private List<Long> receiveUserIds;

    private String hyperlink;

    /**
     * 详情链接的标题
     */
    private String singleTitle;

    private MessageRecordTypeEnums type;

    private MessageRecordBusiTypeEnums busiType;

    /**
     * 工作通知类型
     */
    private DingTalkMessageType dingTalkMessageType;
    /**
     * 通知标题
     */
    private String head;

    /**
     * 是否已经存在
     */
    private Boolean isExist = Boolean.FALSE;
    private Long msgId;

    /**
     * 内容列表 比如 : 创建人: 张三; 显示的内容会将列表转换
     */
    private List<OaSendMessageDTO.Form> contentList;

    private MessageRecordContent() {
    }

    @Slf4j
    public abstract static class MessageRecordContentTemplate {
        protected final JiguangMessageService jiguangMessageService = SpringUtil.getBean(JiguangMessageService.class);
        protected final MessageRecordService messageRecordService = SpringUtil.getBean(MessageRecordService.class);
        protected final DingTalkTodoApi dingTalkTodoApi = SpringUtil.getBean(DingTalkTodoApi.class);
        protected final IWorkNoticeApi workNoticeApi = SpringUtil.getBean(WorkNoticeApi.class);
        protected final OaUserMapper oaUserMapper = SpringUtil.getBean(OaUserMapper.class);
        protected final SysUserDeviceMapper sysUserDeviceMapper = SpringUtil.getBean(SysUserDeviceMapper.class);
        protected MessageRecordContent messageRecordContent;

        public MessageRecordContentTemplate() {
            messageRecordContent = new MessageRecordContent();
        }

        /**
         * 保存本地消息记录
         *
         * @return
         */
        protected final List<Long> push() {
            return messageRecordService.push(messageRecordContent);
        }

        protected final void pushJiGuangMessage(Boolean voiceReminderEnabled) {
            try {
                Boolean switchFlag = jiguangMessageService.getJiGuangConfig();
                log.info("jiguang push switch start flag : {}", switchFlag);
                if (switchFlag) {
                    log.info("jiguang push switch end flag : {}", switchFlag);
                    List<Long> receiveUserId = messageRecordContent.getReceiveUserIds();
                    LambdaQueryWrapper<SysUserDevice> queryWrapper = Wrappers.lambdaQuery();
                    queryWrapper.in(SysUserDevice::getUserId, receiveUserId);
                    List<SysUserDevice> sysUserDeviceList = sysUserDeviceMapper.selectList(queryWrapper);
                    List<String> registrationIdList = sysUserDeviceList.stream().map(SysUserDevice::getRegistrationId).collect(Collectors.toList());
                    //如果registrationIdList为空，则不推送
                    if (CollectionUtil.isEmpty(registrationIdList)) {
                        return;
                    }
                    MessageRecordContentDTO pushMessage = new MessageRecordContentDTO();
                    BeanUtil.copyProperties(messageRecordContent, pushMessage);
                    pushMessage.setVoiceReminderEnabled(voiceReminderEnabled);
                    pushMessage.setRegistrationId(registrationIdList);
                    jiguangMessageService.pushJiGuangMessage(pushMessage);
                }
            } catch (Exception e) {
                log.error("推送极光推送失败！", e.getMessage());
            }
        }

        /**
         * 待办发送接口
         *
         * @param messageRecordContent
         */
        protected void callback(MessageRecordContent messageRecordContent) {
            try {
                List<Long> receiveUserIds = messageRecordContent.getReceiveUserIds();
                if (CollectionUtil.isNotEmpty(receiveUserIds)) {
                    OaUser sendOaUser = oaUserMapper.selectOne(Wrappers.lambdaQuery(OaUser.class).eq(OaUser::getUserId, messageRecordContent.getSendUserId()));
                    List<OaUser> oaUsers = oaUserMapper.selectList(Wrappers.lambdaQuery(OaUser.class).in(OaUser::getUserId, receiveUserIds));
                    Map<Long, OaUser> oaUserMap = new HashMap<>();
                    if (CollectionUtil.isNotEmpty(oaUsers)) {
                        oaUserMap = oaUsers.stream().collect(Collectors.toMap(OaUser::getUserId, oaUser -> oaUser));
                    }
                    for (Long receiveUserId : receiveUserIds) {
                        OaUser oaUser = oaUserMap.get(receiveUserId);
                        if (oaUser == null) {
                            log.error("钉钉待办接收用户{}未绑定钉钉账号，未能发送成功！", receiveUserId);
                            continue;
                        }
                        OaRemindTodoDTO oaRemindTodoDTO = new OaRemindTodoDTO();
                        oaRemindTodoDTO.setBusiId(messageRecordContent.busiId);
                        oaRemindTodoDTO.setExecutorId(oaUser.getOaUserId());
                        // 如果发送人没有 则用自己作为发送人(不会出现弹窗)
                        oaRemindTodoDTO.setSendUserId(sendOaUser != null ? sendOaUser.getUserId():receiveUserId);
                        oaRemindTodoDTO.setCreatorId(sendOaUser != null ? sendOaUser.getOaUserId() : String.valueOf(receiveUserId));
                        oaRemindTodoDTO.setTitle(messageRecordContent.title);
                        oaRemindTodoDTO.setUrl(messageRecordContent.hyperlink);
                        oaRemindTodoDTO.setDescription(messageRecordContent.content);
                        oaRemindTodoDTO.setBusiType(messageRecordContent.busiType.name());
//                        oaRemindTodoDTO.setSendUserId(messageRecordContent.sendUserId);
                        oaRemindTodoDTO.setReceiveUserId(receiveUserId);
                        dingTalkTodoApi.createTodo(oaRemindTodoDTO);
                    }
                }
            } catch (Exception e) {
                log.error("钉钉待办创建失败！", e);
            }
        }

        protected void workNotice(MessageRecordContent messageRecordContent) {
            try {
                List<Long> receiveUserIds = messageRecordContent.getReceiveUserIds();
                if (CollectionUtil.isNotEmpty(receiveUserIds)) {
                    List<OaUser> oaUsers = oaUserMapper.selectList(Wrappers.lambdaQuery(OaUser.class).in(OaUser::getUserId, receiveUserIds));

                    OaSendMessageDTO oaSendMessageDTO = OaSendMessageDTO.builder().busiId(messageRecordContent.busiId).busiType(messageRecordContent.busiType.name())
                            .head(messageRecordContent.head)
                            .title(messageRecordContent.title)
                            .contentList(messageRecordContent.contentList)
                            .content(messageRecordContent.content)
                            .from(messageRecordContent.content)
                            .singleTitle(messageRecordContent.singleTitle)
                            .receiverSet(new HashSet<>(oaUsers.stream().map(OaUser::getOaUserId).collect(Collectors.toList())))
                            .appUrl(messageRecordContent.hyperlink).build();
                    switch (messageRecordContent.getDingTalkMessageType()) {
                        case OA:
                            workNoticeApi.sendOAWorkNotice(oaSendMessageDTO);
                            break;
                        case MARKDOWN:
                            workNoticeApi.sendMarkdownWorkNotice(oaSendMessageDTO);
                            break;
                        case ACTION_CARD:
                            workNoticeApi.sendActionCardWorkNotice(oaSendMessageDTO);
                            break;
                        default:
                            log.error("钉钉工作通知创建失败！");
                    }
                }
            } catch (Exception e) {
                log.error("钉钉工作通知创建失败！", e);
            }
        }

        /**
         * 消息发送格式统一
         */
        protected String formatDate(Date time){
            return time!=null?new SimpleDateFormat("yyyy年MM月dd日 HH:mm").format(time):"";
        }

    }
}
