package com.rutong.medical.common.mybatis.plugin;

import com.rutong.medical.common.mybatis.intercept.QueryInterceptor;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;

import lombok.Setter;
import lombok.SneakyThrows;

/**
 * 分页工具
 *
 * <AUTHOR>
 */

@Setter
public class PaginationInterceptor extends PaginationInnerInterceptor {
	
	/**
	 * 查询拦截器
	 */
	private QueryInterceptor[] queryInterceptors;

	@SneakyThrows
	@Override
	public boolean willDoQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultH<PERSON><PERSON> resultHandler, BoundSql boundSql) {
		QueryInterceptorExecutor.exec(queryInterceptors, executor, ms, parameter, rowBounds, resultHandler, boundSql);
		return super.willDoQuery(executor, ms, parameter, rowBounds, resultHandler, boundSql);
	}

}
