package com.soft.common.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * common-core的配置属性类。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "common-core")
public class CoreProperties {

    public static final String MYSQL_TYPE = "mysql";
    public static final String POSTGRESQL_TYPE = "postgresql";
    public static final String ORACLE_TYPE = "oracle";

    /**
     * 数据库类型。
     */
    private String databaseType = MYSQL_TYPE;

    /**
     * 是否为MySQL。
     *
     * @return 是返回true，否则false。
     */
    public boolean isMySql() {
        return this.databaseType.equals(MYSQL_TYPE);
    }

    /**
     * 是否为PostgreSQl。
     *
     * @return 是返回true，否则false。
     */
    public boolean isPostgresql() {
        return this.databaseType.equals(POSTGRESQL_TYPE);
    }

    /**
     * 是否为Oracle。
     *
     * @return 是返回true，否则false。
     */
    public boolean isOracle() {
        return this.databaseType.equals(ORACLE_TYPE);
    }
}
