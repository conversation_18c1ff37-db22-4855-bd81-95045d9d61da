package com.soft.admin.upms.model.message;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.soft.admin.upms.enums.MessageRecordBusiTypeEnums;
import com.soft.admin.upms.enums.MessageRecordTypeEnums;

import cn.hutool.core.date.DateUtil;

public class MessageRecordContentAlarmTemplate extends MessageRecordContent.MessageRecordContentTemplate {

    private static final MessageRecordTypeEnums TYPE = MessageRecordTypeEnums.ALARM;

    private static final MessageRecordBusiTypeEnums BUSI_TYPE = MessageRecordBusiTypeEnums.ALARM;

    private static final String CONTENT = "${spaceFullName}的${equipmentName}于${alarmTime}发生设备故障，请尽快处理！";

    public MessageRecordContent push(String equipmentName, String spaceFullName, Long busiId, Date alarmTime,
        String level, Long sendUserId, Long... receiveUserIds) {
        messageRecordContent.setTitle(equipmentName);
        messageRecordContent.setBusiId(busiId);
        messageRecordContent.setLevel(level);
        String content = CONTENT.replace("${spaceFullName}", spaceFullName).replace("${equipmentName}", equipmentName)
            .replace("${alarmTime}", DateUtil.format(alarmTime, "yyyy年MM月dd日 HH:mm"));
        messageRecordContent.setContent(content);
        messageRecordContent.setSendUserId(sendUserId);
        messageRecordContent.setReceiveUserIds(Stream.of(receiveUserIds).collect(Collectors.toList()));
        messageRecordContent.setHyperlink("");
        messageRecordContent.setType(TYPE);
        messageRecordContent.setBusiType(BUSI_TYPE);
        pushJiGuangMessage(Boolean.FALSE);
        push();
        callback(messageRecordContent);
        return messageRecordContent;
    }

    public MessageRecordContent pushWarningMessage(MessageRecordBusiTypeEnums busiType, MessageRecordTypeEnums type,
        String content, String title, Long busiId, String level, Long sendUserId,  List<Long> receiveUserIds) {
        messageRecordContent.setTitle(title);
        messageRecordContent.setBusiId(busiId);
        messageRecordContent.setLevel(level);
        messageRecordContent.setContent(content);
        messageRecordContent.setSendUserId(sendUserId);
        messageRecordContent.setReceiveUserIds(receiveUserIds);
        messageRecordContent.setType(type);
        messageRecordContent.setBusiType(busiType);
        pushJiGuangMessage(Boolean.FALSE);
        push();
        callback(messageRecordContent);
        return messageRecordContent;
    }
}
