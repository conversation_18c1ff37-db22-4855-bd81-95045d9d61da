<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.soft</groupId>
        <artifactId>smart-medical</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.rutong.medical.admin</groupId>
    <artifactId>medical-admin</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    <name>medical-admin</name>
    <description>medical-admin</description>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>

        <!-- 业务组件依赖 -->
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>module-user</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-ext</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-app-push</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-redis</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-online-api</artifactId>
            <version>1.0.0</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.soft</groupId>-->
<!--            <artifactId>common-flow-online</artifactId>-->
<!--            <version>1.0.0</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-log</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-minio</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-monitor-reader</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-sequence</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-datafilter</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-swagger</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-dict</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.soft</groupId>
            <artifactId>common-mqtt</artifactId>
            <version>1.0.0</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.soft</groupId>-->
<!--            <artifactId>common-debezium</artifactId>-->
<!--            <version>1.0.0</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <artifactId>slf4j-reload4j</artifactId>-->
<!--                    <groupId>org.slf4j</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>org.slf4j</groupId>-->
<!--                    <artifactId>slf4j-api</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>shardingsphere-jdbc-core-spring-boot-starter</artifactId>
            <version>5.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-dbcp</artifactId>
            <version>10.0.16</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-websocket</artifactId>
            <version>5.3.18</version>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.4.0-SNAPSHOT</version>
        </dependency>
        <!-- word导出 方式：easypoi -->
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-base</artifactId>
            <version>4.3.0</version>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-web</artifactId>
            <version>4.3.0</version>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-annotation</artifactId>
            <version>4.3.0</version>
        </dependency>
        <!-- word中要使用循环等标签必须单独导入以下依赖 -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>ooxml-schemas</artifactId>
            <version>1.4</version>
        </dependency>
        <dependency>
            <groupId>com.taosdata.jdbc</groupId>
            <artifactId>taos-jdbcdriver</artifactId>
            <version>3.2.8</version>
        </dependency>
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
            <version>4.0.3</version>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>taosdata-repo</id>
            <name>TDengine Repository</name>
            <url>https://repo.taosdata.com/repository/maven-public/</url>
        </repository>
    </repositories>

    <build>
        <finalName>smart-medical</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>true</filtering>
            </testResource>
        </testResources>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <excludes>
                        <exclude>**/test/**</exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <profiles.active>dev</profiles.active>
                <maven.test.skip>true</maven.test.skip>
                <scope.jar>compile</scope.jar>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <profiles.active>test</profiles.active>
                <maven.test.skip>true</maven.test.skip>
                <scope.jar>provided</scope.jar>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
                <maven.test.skip>true</maven.test.skip>
                <scope.jar>provided</scope.jar>
            </properties>
        </profile>
    </profiles>
</project>

