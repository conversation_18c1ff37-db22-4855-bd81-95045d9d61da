package com.soft.common.core.util;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.InetAddress;

/**
 * @Description 网络工具类
 * @Date 0020, 2023年4月20日 10:46
 * <AUTHOR>
 **/
@Slf4j
public final class NetworkUtils {

    private NetworkUtils() {}

    public static boolean ping(String ipAddress){
        int  timeOut =  3000 ;   // 超时应该在3钞以上
        boolean status = false;      //  当返回值是true时，说明host是可用的，false则不可。
        try {
            log.info("ping:{}", ipAddress);
            status = InetAddress.getByName(ipAddress).isReachable(timeOut);
        } catch (IOException e) {
            log.error("ping {} error", ipAddress, e);
        }
        return status;
    }
}
